﻿using System.Text.Json.Serialization;

namespace MyDSitefinityAPI.Models
{
    public class PracticeLocationResponse
    {
        public int PracticeId { get; set; }
        public string PracticeName { get; set; }
        public string Postcode { get; set; }
        public decimal? PracticeLat { get; set; }
        public decimal? PracticeLong { get; set; }
        public double? Distance { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public List<string> AvailableTreatments { get; set; }
    }
}
