﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace MyDSitefinityAPI.Models
{
    [Table("sf_WebJobApplication", Schema = "dbo")]
    public class sf_WebJobApplication
    {
        [Key]
        [Column("Id")]
        public int Id { get; set; }
    
        public string ApplicationJson { get; set; }
        public string CVUrl { get; set; }
       
        public DateTime? ProcessedDate { get; set; }
        public DateTime? ErrorDate { get; set; }
        public DateTime? CreatedDate { get; set; }
        public string EntryId { get; set; }
    }
}
