﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Mydentist.MyDSitefinityAPI.Persistence;

#nullable disable

namespace Mydentist.MyDSitefinityAPI.Persistence.Migrations
{
    [DbContext(typeof(DbContextWarehouse))]
    [Migration("20230810165343_ReferralPeleFix")]
    partial class ReferralPeleFix
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.4")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Mydentist.MyDSitefinityAPI.Domain.RecruitmentWebsiteFormModel", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CRMLeadContactCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ContactNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("CountryOfQualification")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedDateTime")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2");

                    b.Property<int?>("DesiredRole")
                        .HasColumnType("int");

                    b.Property<string>("DesiredWorkingLocation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("DesiredWorkingLocationLatitude")
                        .HasColumnType("float");

                    b.Property<double?>("DesiredWorkingLocationLongitude")
                        .HasColumnType("float");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FormName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("GAClientId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("GdcNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("LastProcessedDateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("PostgraduateQualification")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReferrerContactNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReferrerEmailAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReferrerGdcNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReferrerName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Status")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("UniversityPostgraduate")
                        .HasColumnType("int");

                    b.Property<int?>("UniversityUndergraduate")
                        .HasColumnType("int");

                    b.Property<DateTime?>("YearQualifiedPostgraduate")
                        .HasColumnType("date");

                    b.Property<DateTime?>("YearQualifiedUndergraduate")
                        .HasColumnType("date");

                    b.HasKey("Id");

                    b.ToTable("RecruitmentWebsiteForm", "Recruitment");
                });
#pragma warning restore 612, 618
        }
    }
}
