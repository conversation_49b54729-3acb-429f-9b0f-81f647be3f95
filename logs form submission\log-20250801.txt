2025-08-01T00:41:33.2377648+01:00  [WRN] Using an in-memory repository. Keys will not be persisted to storage. (28e83010)
2025-08-01T00:41:33.2408903+01:00  [WRN] Neither user profile nor HKLM registry available. Using an ephemeral key repository. Protected data will be unavailable when application exits. (54f66960)
2025-08-01T00:41:33.2796502+01:00  [WRN] No XML encryptor configured. Key {7847cf1b-5eb5-48d0-8171-b55b23cd5aa6} may be persisted to storage in unencrypted form. (9ca7e61e)
2025-08-01T00:41:33.3564952+01:00 80106196-0001-b600-b63f-84710c7967bb [WRN] Failed to determine the https port for redirect. (ca76cc21)
2025-08-01T00:41:33.6931048+01:00 80106196-0001-b600-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "TextFieldController": "Maruf ",
    "TextFieldController_0": "Uddin ",
    "TextFieldController_1": "07745816129",
    "TextFieldController_2": "<EMAIL>",
    "TextFieldController_3": "01/01/2003",
    "TextFieldController_4": "DN35 8LY",
    "CheckboxesFieldController": "Teeth whitening",
    "GAClientId": "1688053427.1754005176",
    "SelectedPractice": "",
    "SelectedPostCode": "",
    "SelectedTreatment": "",
    "SourcePage": "HomePageEnquiry"
  },
  "OriginalEvent": {
    "EntryId": "56ee5c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "192191",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T23:41:32.1629842Z",
    "FormId": "03a5ad9c-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_quick_contact_generic_form",
    "FormTitle": "Quick contact Generic form",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "44a6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "First name",
        "FieldName": "TextFieldController",
        "Value": "Maruf ",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "51a6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "44a6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Last name",
        "FieldName": "TextFieldController_0",
        "Value": "Uddin ",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "5ea6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "51a6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Phone number",
        "FieldName": "TextFieldController_1",
        "Value": "07745816129",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "6da6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "5ea6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Email",
        "FieldName": "TextFieldController_2",
        "Value": "<EMAIL>",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "7ca6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "6da6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Date of birth (dd/mm/yyyy)",
        "FieldName": "TextFieldController_3",
        "Value": "01/01/2003",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "93a6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8aa6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Postcode",
        "FieldName": "TextFieldController_4",
        "Value": "DN35 8LY",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "a1a6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "93a6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Interested in",
        "FieldName": "CheckboxesFieldController",
        "Value": "Teeth whitening",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "c1a6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "afa6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "GAClientId",
        "FieldName": "GAClientId",
        "Value": "1688053427.1754005176",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "852daf9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "c1a6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "SelectedPractice",
        "FieldName": "SelectedPractice",
        "Value": "",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "301faf9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "852daf9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "SelectedPostCode",
        "FieldName": "SelectedPostCode",
        "Value": "",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "86aaae9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "301faf9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "SelectedTreatment",
        "FieldName": "SelectedTreatment",
        "Value": "",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "54bdae9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "86aaae9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "SourcePage",
        "FieldName": "SourcePage",
        "Value": "HomePageEnquiry",
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (cfcf709f)
2025-08-01T00:41:33.7802686+01:00 80106196-0001-b600-b63f-84710c7967bb [INF]  Logging only, not processed - The General Enquiry form JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "TextFieldController": "Maruf ",
    "TextFieldController_0": "Uddin ",
    "TextFieldController_1": "07745816129",
    "TextFieldController_2": "<EMAIL>",
    "TextFieldController_3": "01/01/2003",
    "TextFieldController_4": "DN35 8LY",
    "CheckboxesFieldController": "Teeth whitening",
    "GAClientId": "1688053427.1754005176",
    "SelectedPractice": "",
    "SelectedPostCode": "",
    "SelectedTreatment": "",
    "SourcePage": "HomePageEnquiry"
  },
  "OriginalEvent": {
    "EntryId": "56ee5c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "192191",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T23:41:32.1629842Z",
    "FormId": "03a5ad9c-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_quick_contact_generic_form",
    "FormTitle": "Quick contact Generic form",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "44a6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "First name",
        "FieldName": "TextFieldController",
        "Value": "Maruf ",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "51a6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "44a6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Last name",
        "FieldName": "TextFieldController_0",
        "Value": "Uddin ",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "5ea6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "51a6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Phone number",
        "FieldName": "TextFieldController_1",
        "Value": "07745816129",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "6da6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "5ea6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Email",
        "FieldName": "TextFieldController_2",
        "Value": "<EMAIL>",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "7ca6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "6da6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Date of birth (dd/mm/yyyy)",
        "FieldName": "TextFieldController_3",
        "Value": "01/01/2003",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "93a6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8aa6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Postcode",
        "FieldName": "TextFieldController_4",
        "Value": "DN35 8LY",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "a1a6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "93a6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Interested in",
        "FieldName": "CheckboxesFieldController",
        "Value": "Teeth whitening",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "c1a6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "afa6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "GAClientId",
        "FieldName": "GAClientId",
        "Value": "1688053427.1754005176",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "852daf9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "c1a6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "SelectedPractice",
        "FieldName": "SelectedPractice",
        "Value": "",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "301faf9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "852daf9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "SelectedPostCode",
        "FieldName": "SelectedPostCode",
        "Value": "",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "86aaae9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "301faf9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "SelectedTreatment",
        "FieldName": "SelectedTreatment",
        "Value": "",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "54bdae9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "86aaae9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "SourcePage",
        "FieldName": "SourcePage",
        "Value": "HomePageEnquiry",
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (133feb34)
2025-08-01T00:41:35.6615626+01:00 80106196-0001-b600-b63f-84710c7967bb [INF] (General Enquiry) Form has been saved to DB successfully (c3b2ea50)
2025-08-01T00:42:43.9786441+01:00 8003d10e-0002-7400-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "TextFieldController": "Merajul",
    "TextFieldController_0": "Islam",
    "TextFieldController_1": "07349151666",
    "TextFieldController_2": "<EMAIL>",
    "TextFieldController_3": "01/10/1994",
    "TextFieldController_4": "CF24 0LH",
    "CheckboxesFieldController": "I would like more information on all treatments",
    "GAClientId": "1738485051.1754005299",
    "SelectedPractice": "",
    "SelectedPostCode": "",
    "SelectedTreatment": "",
    "SourcePage": "HomePageEnquiry"
  },
  "OriginalEvent": {
    "EntryId": "19ee5c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "192192",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T23:42:43.8451357Z",
    "FormId": "03a5ad9c-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_quick_contact_generic_form",
    "FormTitle": "Quick contact Generic form",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "44a6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "First name",
        "FieldName": "TextFieldController",
        "Value": "Merajul",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "51a6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "44a6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Last name",
        "FieldName": "TextFieldController_0",
        "Value": "Islam",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "5ea6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "51a6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Phone number",
        "FieldName": "TextFieldController_1",
        "Value": "07349151666",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "6da6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "5ea6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Email",
        "FieldName": "TextFieldController_2",
        "Value": "<EMAIL>",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "7ca6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "6da6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Date of birth (dd/mm/yyyy)",
        "FieldName": "TextFieldController_3",
        "Value": "01/10/1994",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "93a6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8aa6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Postcode",
        "FieldName": "TextFieldController_4",
        "Value": "CF24 0LH",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "a1a6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "93a6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Interested in",
        "FieldName": "CheckboxesFieldController",
        "Value": "I would like more information on all treatments",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "c1a6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "afa6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "GAClientId",
        "FieldName": "GAClientId",
        "Value": "1738485051.1754005299",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "852daf9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "c1a6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "SelectedPractice",
        "FieldName": "SelectedPractice",
        "Value": "",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "301faf9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "852daf9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "SelectedPostCode",
        "FieldName": "SelectedPostCode",
        "Value": "",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "86aaae9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "301faf9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "SelectedTreatment",
        "FieldName": "SelectedTreatment",
        "Value": "",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "54bdae9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "86aaae9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "SourcePage",
        "FieldName": "SourcePage",
        "Value": "HomePageEnquiry",
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (aa9b28c2)
2025-08-01T00:42:43.9796815+01:00 8003d10e-0002-7400-b63f-84710c7967bb [INF]  Logging only, not processed - The General Enquiry form JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "TextFieldController": "Merajul",
    "TextFieldController_0": "Islam",
    "TextFieldController_1": "07349151666",
    "TextFieldController_2": "<EMAIL>",
    "TextFieldController_3": "01/10/1994",
    "TextFieldController_4": "CF24 0LH",
    "CheckboxesFieldController": "I would like more information on all treatments",
    "GAClientId": "1738485051.1754005299",
    "SelectedPractice": "",
    "SelectedPostCode": "",
    "SelectedTreatment": "",
    "SourcePage": "HomePageEnquiry"
  },
  "OriginalEvent": {
    "EntryId": "19ee5c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "192192",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T23:42:43.8451357Z",
    "FormId": "03a5ad9c-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_quick_contact_generic_form",
    "FormTitle": "Quick contact Generic form",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "44a6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "First name",
        "FieldName": "TextFieldController",
        "Value": "Merajul",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "51a6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "44a6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Last name",
        "FieldName": "TextFieldController_0",
        "Value": "Islam",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "5ea6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "51a6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Phone number",
        "FieldName": "TextFieldController_1",
        "Value": "07349151666",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "6da6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "5ea6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Email",
        "FieldName": "TextFieldController_2",
        "Value": "<EMAIL>",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "7ca6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "6da6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Date of birth (dd/mm/yyyy)",
        "FieldName": "TextFieldController_3",
        "Value": "01/10/1994",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "93a6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8aa6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Postcode",
        "FieldName": "TextFieldController_4",
        "Value": "CF24 0LH",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "a1a6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "93a6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Interested in",
        "FieldName": "CheckboxesFieldController",
        "Value": "I would like more information on all treatments",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "c1a6ad9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "afa6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "GAClientId",
        "FieldName": "GAClientId",
        "Value": "1738485051.1754005299",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "852daf9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "c1a6ad9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "SelectedPractice",
        "FieldName": "SelectedPractice",
        "Value": "",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "301faf9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "852daf9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "SelectedPostCode",
        "FieldName": "SelectedPostCode",
        "Value": "",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "86aaae9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "301faf9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "SelectedTreatment",
        "FieldName": "SelectedTreatment",
        "Value": "",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "54bdae9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "86aaae9c-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "SourcePage",
        "FieldName": "SourcePage",
        "Value": "HomePageEnquiry",
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (e4fd0c2f)
2025-08-01T00:42:44.0171768+01:00 8003d10e-0002-7400-b63f-84710c7967bb [INF] (General Enquiry) Form has been saved to DB successfully (c3b2ea50)
2025-08-01T00:51:54.0704414+01:00 8035b662-0000-af00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "FormTextBox_C010": "Benjamin",
    "FormTextBox_C011": "Hoskin",
    "FormTextBox_C003": "07488300533",
    "FormTextBox_C004": "<EMAIL>",
    "FormTextBox_C015": "12/01/1979",
    "FormCheckboxes_C022": "I would like more information on all treatments",
    "": null,
    "FormTextBox": null,
    "HiddenPracticeId": "20308=>mydentist, Howard Street, Bedford",
    "GAClientId": "2125672950.1754005843",
    "FormTextBox_C001": null,
    "processed_date": null,
    "error_date": null
  },
  "OriginalEvent": {
    "EntryId": "57ee5c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "485857",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": null,
    "SubmissionTime": "2025-07-31T23:51:53.9778952Z",
    "FormId": "065ddc9a-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_quickcontactpmform",
    "FormTitle": "Quick contact PM form",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "75b17a9b-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "First name",
        "FieldName": "FormTextBox_C010",
        "Value": "Benjamin",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "83b17a9b-cae7-6322-9971-ff0000979a84",
        "SiblingId": "75b17a9b-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Last name",
        "FieldName": "FormTextBox_C011",
        "Value": "Hoskin",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ad5ddc9a-cae7-6322-9971-ff0000979a84",
        "SiblingId": "83b17a9b-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Phone Number",
        "FieldName": "FormTextBox_C003",
        "Value": "07488300533",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "bc5ddc9a-cae7-6322-9971-ff0000979a84",
        "SiblingId": "ad5ddc9a-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Email",
        "FieldName": "FormTextBox_C004",
        "Value": "<EMAIL>",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ebd17a9b-cae7-6322-9971-ff0000979a84",
        "SiblingId": "bc5ddc9a-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Date of birth (dd/mm/yyyy)",
        "FieldName": "FormTextBox_C015",
        "Value": "12/01/1979",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "4690ae9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "ebd17a9b-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Interested in",
        "FieldName": "FormCheckboxes_C022",
        "Value": "I would like more information on all treatments",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "00cc7a9b-cae7-6322-9971-ff0000979a84",
        "SiblingId": "a65ddc9a-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 3,
        "Title": "<p class=\"small-print align-center privacy-link\">To view our privacy policy, <a href=\"/customer-services/your-privacy\" target=\"_blank\">click here</a>.</p>",
        "FieldName": null,
        "Value": null,
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "f217189b-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00cc7a9b-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Interested In",
        "FieldName": "FormTextBox",
        "Value": null,
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "32af369b-cae7-6322-9971-ff0000979a84",
        "SiblingId": "f217189b-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "HiddenPracticeId",
        "FieldName": "HiddenPracticeId",
        "Value": "20308=>mydentist, Howard Street, Bedford",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "18eb7a9b-cae7-6322-9971-ff0000979a84",
        "SiblingId": "32af369b-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "GAClientId",
        "FieldName": "GAClientId",
        "Value": "2125672950.1754005843",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "985ddc9a-cae7-6322-9971-ff0000979a84",
        "SiblingId": "18eb7a9b-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Full Name",
        "FieldName": "FormTextBox_C001",
        "Value": null,
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "e143809b-cae7-6322-9971-ff0000979a84",
        "SiblingId": "985ddc9a-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Processed Date",
        "FieldName": "processed_date",
        "Value": null,
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ef43809b-cae7-6322-9971-ff0000979a84",
        "SiblingId": "e143809b-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Error Date",
        "FieldName": "error_date",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (8fb8eb08)
2025-08-01T00:51:54.0758697+01:00 8035b662-0000-af00-b63f-84710c7967bb [INF] Begin QuickContactform insertion Entry Id 57ee5c9e-cae7-6322-9971-ff0200979a84 (7345aae2)
2025-08-01T00:51:54.0760523+01:00 8035b662-0000-af00-b63f-84710c7967bb [INF] The sf_quickcontactpmform JSON is: {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "FormTextBox_C010": "Benjamin",
    "FormTextBox_C011": "Hoskin",
    "FormTextBox_C003": "07488300533",
    "FormTextBox_C004": "<EMAIL>",
    "FormTextBox_C015": "12/01/1979",
    "FormCheckboxes_C022": "I would like more information on all treatments",
    "": null,
    "FormTextBox": null,
    "HiddenPracticeId": "20308=>mydentist, Howard Street, Bedford",
    "GAClientId": "2125672950.1754005843",
    "FormTextBox_C001": null,
    "processed_date": null,
    "error_date": null
  },
  "OriginalEvent": {
    "EntryId": "57ee5c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "485857",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": null,
    "SubmissionTime": "2025-07-31T23:51:53.9778952Z",
    "FormId": "065ddc9a-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_quickcontactpmform",
    "FormTitle": "Quick contact PM form",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "75b17a9b-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "First name",
        "FieldName": "FormTextBox_C010",
        "Value": "Benjamin",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "83b17a9b-cae7-6322-9971-ff0000979a84",
        "SiblingId": "75b17a9b-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Last name",
        "FieldName": "FormTextBox_C011",
        "Value": "Hoskin",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ad5ddc9a-cae7-6322-9971-ff0000979a84",
        "SiblingId": "83b17a9b-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Phone Number",
        "FieldName": "FormTextBox_C003",
        "Value": "07488300533",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "bc5ddc9a-cae7-6322-9971-ff0000979a84",
        "SiblingId": "ad5ddc9a-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Email",
        "FieldName": "FormTextBox_C004",
        "Value": "<EMAIL>",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ebd17a9b-cae7-6322-9971-ff0000979a84",
        "SiblingId": "bc5ddc9a-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Date of birth (dd/mm/yyyy)",
        "FieldName": "FormTextBox_C015",
        "Value": "12/01/1979",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "4690ae9c-cae7-6322-9971-ff0000979a84",
        "SiblingId": "ebd17a9b-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Interested in",
        "FieldName": "FormCheckboxes_C022",
        "Value": "I would like more information on all treatments",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "00cc7a9b-cae7-6322-9971-ff0000979a84",
        "SiblingId": "a65ddc9a-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 3,
        "Title": "<p class=\"small-print align-center privacy-link\">To view our privacy policy, <a href=\"/customer-services/your-privacy\" target=\"_blank\">click here</a>.</p>",
        "FieldName": null,
        "Value": null,
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "f217189b-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00cc7a9b-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Interested In",
        "FieldName": "FormTextBox",
        "Value": null,
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "32af369b-cae7-6322-9971-ff0000979a84",
        "SiblingId": "f217189b-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "HiddenPracticeId",
        "FieldName": "HiddenPracticeId",
        "Value": "20308=>mydentist, Howard Street, Bedford",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "18eb7a9b-cae7-6322-9971-ff0000979a84",
        "SiblingId": "32af369b-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "GAClientId",
        "FieldName": "GAClientId",
        "Value": "2125672950.1754005843",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "985ddc9a-cae7-6322-9971-ff0000979a84",
        "SiblingId": "18eb7a9b-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Full Name",
        "FieldName": "FormTextBox_C001",
        "Value": null,
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "e143809b-cae7-6322-9971-ff0000979a84",
        "SiblingId": "985ddc9a-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Processed Date",
        "FieldName": "processed_date",
        "Value": null,
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ef43809b-cae7-6322-9971-ff0000979a84",
        "SiblingId": "e143809b-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 0,
        "Title": "Error Date",
        "FieldName": "error_date",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (248d7ebf)
2025-08-01T00:51:54.0772392+01:00 8035b662-0000-af00-b63f-84710c7967bb [INF] QuickContact form object created (cf8c95c3)
2025-08-01T00:51:54.1541321+01:00 8035b662-0000-af00-b63f-84710c7967bb [INF] Successfully Saved quickContact entryId 57ee5c9e-cae7-6322-9971-ff0200979a84 (c8a22b45)
