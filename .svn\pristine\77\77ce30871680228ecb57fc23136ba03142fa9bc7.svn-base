﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Data.SqlTypes;

namespace MyDSitefinityAPI.Models
{
    public class CustomerFeedbackViewModel
    {
        [Column(TypeName = "datetime2")]
        public DateTime DateSubmitted { get; set; }
        public string? Email { get; set; }
        public string? FeedbackInformation { get; set; }
        public string? FeedbackTitle { get; set; }
        public string? FeedbackType { get; set; }
        public string? PatientName { get; set; }
        public string? PhoneNumber { get; set; }
        public string? PracticeName { get; set; }
		public string? PmDisplayName { get; set; }
	}
}
