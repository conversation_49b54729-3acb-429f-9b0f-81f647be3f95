﻿using System.Text.Json;
using System.Text.Json.Serialization;

namespace MyDSitefinityAPI.Converters
{
    public class StringOrArrayConverter : JsonConverter<string>
    {
        public override string Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType == JsonTokenType.StartArray)
            {
                var arrayValues = new List<string>();
                while (reader.Read())
                {
                    if (reader.TokenType == JsonTokenType.EndArray)
                        break;

                    if (reader.TokenType == JsonTokenType.String)
                    {
                        arrayValues.Add(reader.GetString());
                    }
                }
                // Combine array values into a single string
                return string.Join(",", arrayValues);
            }

            if (reader.TokenType == JsonTokenType.String)
            {
                return reader.GetString();
            }

            throw new JsonException($"Unexpected token {reader.TokenType} when parsing string.");
        }

        public override void Write(Utf8JsonWriter writer, string value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(value);
        }
    }
}
