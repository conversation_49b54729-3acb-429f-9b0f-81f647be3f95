﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using Mydentist.MyDSitefinityAPI.ImplementationServices;

namespace MyDSitefinityAPI.IntegrationTests.Tests.Services
{
    [TestClass]
    public class RecruitmentWebsiteFormImplementationServiceTests: Test
    {
        [TestMethod]
        public async Task CanProcessARecruitmentWebsiteForm()
        {
            var request = new RecruitmentWebsiteFormRequest()
            {
                ContactNumber = "0132456987",
                DesiredRole = "Associate Dentist",
                DesiredLocation = "Chorlton-cum-Hardy",
                Email = "<EMAIL>",
                FirstName = "Test",
                LastName = "Recruitment Website Form",
                FormName = "Referral",
                GdcNumber = "12365487",
                GAClientId = "012648.456484",
                ReferrerContactNumber = "0987654321",
                ReferrerEmailAddress = "<EMAIL>",
                ReferrerGdcNumber = "654321",
                ReferrerName = "Referrer Person",
                DesiredLocationLatitude = "-2.2640611",
                DesiredLocationLongitude = "53.4323505"
            };

            var recruitmentWebsiteFormModel = await RecruitmentWebsiteFormImplentationService.ProcessRequest(request);

            Assert.AreNotEqual(0, recruitmentWebsiteFormModel.Id);
            Assert.AreEqual(-2.2640611, recruitmentWebsiteFormModel.DesiredWorkingLocationLatitude);
            Assert.AreEqual(53.4323505, recruitmentWebsiteFormModel.DesiredWorkingLocationLongitude);
        }

        [TestMethod]
        public async Task CanProcessARecruitmentReferralWebsiteFormWithOneName()
        {
            var request = new RecruitmentWebsiteFormRequest()
            {
                ContactNumber = "0132456987",
                DesiredRole = "Associate Dentist",
                DesiredLocation = "Chorlton-cum-Hardy",
                Email = "<EMAIL>",
                FirstName = "Test",
                LastName = "Recruitment Website Form",
                FormName = "Referral",
                GdcNumber = "12365487",
                GAClientId = "012648.456484",
                ReferrerContactNumber = "0987654321",
                ReferrerEmailAddress = "<EMAIL>",
                ReferrerGdcNumber = "654321",
                ReferrerName = "Pele",
                DesiredLocationLatitude = "-2.2640611",
                DesiredLocationLongitude = "53.4323505"
            };

            var recruitmentWebsiteFormModel = await RecruitmentWebsiteFormImplentationService.ProcessRequest(request);

            Assert.AreNotEqual(0, recruitmentWebsiteFormModel.Id);
        }

        [TestMethod]
        public async Task CanProcessARecruitmentWebsiteFormWithABadDate()
        {
            var request = new RecruitmentWebsiteFormRequest()
            {
                ContactNumber = "0132456987",
                DesiredRole = "Associate Dentist",
                DesiredLocation = "Chorlton-cum-Hardy",
                Email = "<EMAIL>",
                FirstName = "Test",
                LastName = "Recruitment Website Form",
                FormName = "Referral",
                GdcNumber = "12365487",
                GAClientId = "012648.456484",
                ReferrerContactNumber = "0987654321",
                ReferrerEmailAddress = "<EMAIL>",
                ReferrerGdcNumber = "654321",
                YearQualifiedPostgraduate = "--",//Bad Date
                DesiredLocationLatitude = "-2.2640611",
                DesiredLocationLongitude = "53.4323505"
            };

            var recruitmentWebsiteFormModel = await RecruitmentWebsiteFormImplentationService.ProcessRequest(request);

            Assert.AreNotEqual(0, recruitmentWebsiteFormModel.Id);
        }
    }
}
