using Microsoft.VisualStudio.TestTools.UnitTesting;
using MyDSitefinityAPI.DBContext;
using MyDSitefinityAPI.IntegrationTests.Mock;
using MyDSitefinityAPI.Models;
using MyDSitefinityAPI.Models.PracticeDirectory;
using MyDSitefinityAPI.Services;
using System.Data.Entity;
using IDHGroup.SharedLibraries.SitefinityServiceCaller.Core;
using MyDSitefinityAPI.Interfaces;
using Mydentist.MyDSitefinityAPI.ImplementationServices;
using Mydentist.MyDSitefinityAPI.Persistence;
using AutoMapper;
using Mydentist.MyDSitefinityApi.ClinicianPortalApi;
using System.Reflection;
using Microsoft.Extensions.Options;
using MyDSitefinityAPI.IntegrationTests.Tests.Factories;
using Mydentist.MyDSitefinityAPI.WebConfigApi;
using Mydentist.MyDSitefinityAPI.ImplementationServices.Interfaces;
using Microsoft.Extensions.Configuration;

namespace MyDSitefinityAPI.IntegrationTests.Tests
{
    [TestClass]
    public class Test
    {
        protected GroupWebsitePracticeDataContext GroupWebsitePracticeDataContext { get; }
        protected DBWarehouseContext DbWarehouseContext { get; }


        protected PublicUserAuthenticationService PublicUserAuthenticationService { get; }
        protected PerformerProfileService PerformerProfileService { get; }
        protected WebserviceCaller WebserviceCaller { get; }
        protected RecruitmentWebsiteFormImplentationService RecruitmentWebsiteFormImplentationService { get; }
        protected RecruitmentWebsiteFormService RecruitmentWebsiteFormService { get; }
        protected RecruitmentFormTransformerService RecruitmentFormTransformerService { get; }
        protected SharedListMappingService SharedListMappingService { get; }
        protected SharedListService SharedListService { get; }
        protected ClinicianPortalApiWrapper ClinicianPortalApiWrapper { get; }
        protected WebsiteConfigRoleService WebsiteConfigRoleService { get; }
        protected WebsiteConfigWrapperService WebsiteConfigWrapperService { get; }
        protected DbContextWarehouse DbContextWarehouse { get; }
        protected ILogger Logger { get; }
        protected ILogger<RecruitmentFormTransformerService> RecruitmentFormTransformerLogger { get; }
        protected ILogger<RecruitmentWebsiteFormImplentationService> RecruitmentWebsiteFormImplentationLogger { get; }
        protected ILogger<VacancyDbService> VacancyDbServiceLogger { get; }
        protected VacancyDbService VacancyDbService { get; }
        protected IPracticeManagerServices PracticeManagerServices { get; }
        protected IRecruitmentHubService RecruitmentHubService { get; }
        protected IFindNearestPracticeService FindNearestPracticeService { get; }
        protected IPracticeServices PracticeServices { get; }
        protected IConfiguration Configuration { get; }
        protected SitefinityFormService SitefinityFormService { get; }
        protected PublicUser TestPublicUser { get; }
        protected PerformerProfile TestPerformerProfile { get; }
        protected PerformerImage TestPerformerImage { get; }
        protected PracticeDetails TestPractice { get; }
        protected const string TestUsername = "IDH.AutoRegressionTest";
        protected const string TestPassword = "Hjf9$;3Am+7}1#MP+)$>";

        public Test()
        {
            IEmailSender emailSender = new MockEmailSender();

            GroupWebsitePracticeDataContext = new GroupWebsitePracticeDataContext();
            DbWarehouseContext = new DBWarehouseContext();
            PublicUserAuthenticationService = new PublicUserAuthenticationService(new MockLogger<PublicUserAuthenticationService>(), GroupWebsitePracticeDataContext);
            WebserviceCaller = new WebserviceCaller(new MockLogger<WebserviceCaller>());
            PerformerProfileService = new PerformerProfileService(new MockLogger<PerformerProfileService>(), DbWarehouseContext, emailSender, WebserviceCaller);

            var options = DbContextWarehouseFactory.Get();
            DbContextWarehouse = new DbContextWarehouse(options);
            RecruitmentWebsiteFormService = new RecruitmentWebsiteFormService(DbContextWarehouse);
            var mockMapper = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile(new RecruitmentWebsiteFormProfile()); 
            });
            
            var mapper = mockMapper.CreateMapper();

            IConfigurationRoot configuration = new ConfigurationBuilder()
                    .SetBasePath(GetAssemblyDirectory())
                    .AddJsonFile("appsettings.json")
                    .Build();

            var clinicianPortalSettingOptions = Options.Create(configuration.GetSection("ClinicianPortalSettings").Get<ClinicianPortalSettings>());
            Logger = new MockLogger<ILogger>();
            RecruitmentFormTransformerLogger = new MockLogger<RecruitmentFormTransformerService>();
            RecruitmentWebsiteFormImplentationLogger = new MockLogger<RecruitmentWebsiteFormImplentationService>();
            VacancyDbServiceLogger = new MockLogger<VacancyDbService>();


            ClinicianPortalApiWrapper = new ClinicianPortalApiWrapper(clinicianPortalSettingOptions);
            SharedListService = new SharedListService(ClinicianPortalApiWrapper);
            SharedListMappingService = new SharedListMappingService(SharedListService);
            RecruitmentWebsiteFormService = new RecruitmentWebsiteFormService(DbContextWarehouse);
            WebsiteConfigWrapperService = new WebsiteConfigWrapperService();
            WebsiteConfigRoleService = new WebsiteConfigRoleService(WebsiteConfigWrapperService);
            RecruitmentFormTransformerService = new RecruitmentFormTransformerService(SharedListMappingService, RecruitmentFormTransformerLogger, WebsiteConfigRoleService);
            RecruitmentWebsiteFormImplentationService = new RecruitmentWebsiteFormImplentationService(RecruitmentWebsiteFormService, RecruitmentFormTransformerService, mapper, RecruitmentWebsiteFormImplentationLogger);
            VacancyDbService = new VacancyDbService(VacancyDbServiceLogger);

            // Initialize mock services
            PracticeManagerServices = new MockPracticeManagerServices();
            RecruitmentHubService = new MockRecruitmentHubService();
            FindNearestPracticeService = new MockFindNearestPracticeService();
            PracticeServices = new MockPracticeServices();
            Configuration = new MockConfiguration();

            // Initialize SitefinityFormService with all dependencies
            SitefinityFormService = new SitefinityFormService(
                emailSender,
                Logger,
                PracticeManagerServices,
                Configuration,
                RecruitmentWebsiteFormImplentationService,
                RecruitmentHubService,
                VacancyDbService,
                FindNearestPracticeService,
                PracticeServices);

            TestPublicUser = GenerateTestUser();
            TestPerformerProfile = GenerateTestPerformerProfile();
            TestPerformerImage = TestPerformerProfile.Images.First();
            TestPractice = TestPerformerImage.Practice;
        }

        public string GetAssemblyDirectory()
        {
            string location = Assembly.GetExecutingAssembly().Location;
            return Path.GetDirectoryName(location);
        }

        [TestInitialize]
        public void TestInitialise()
        {
            // In case we exited the tests uncleanly, remove the old data first so we don't try to write duplicate keys
            PublicUser? savedTestUser = GroupWebsitePracticeDataContext.PublicUsers.FirstOrDefault(publicUser => publicUser.UserId == TestPublicUser.UserId);
            if (savedTestUser != null)
            {
                GroupWebsitePracticeDataContext.PublicUsers.Remove(savedTestUser);
            }
            
            GroupWebsitePracticeDataContext.PublicUsers.Add(TestPublicUser);
            GroupWebsitePracticeDataContext.SaveChanges();
        }

        [TestCleanup]
        public void TestCleanup()
        {
            PublicUser savedTestUser = GroupWebsitePracticeDataContext.PublicUsers.First(publicUser => publicUser.UserId == TestPublicUser.UserId);
            GroupWebsitePracticeDataContext.PublicUsers.Remove(savedTestUser);
            GroupWebsitePracticeDataContext.SaveChanges();
        }

        private PublicUser GenerateTestUser()
        {
            string salt = PublicUserAuthenticationService.GenerateSalt(8);
            string password = PublicUserAuthenticationService.ComputeHash(TestPassword, salt);

            PublicUser testPublicUser = new PublicUser
            {
                UserId = TestUsername,
                Salt = salt,
                Password = password
            };

            return testPublicUser;
        }

        private PerformerProfile GenerateTestPerformerProfile()
        {
            PerformerProfile performerProfile = DbWarehouseContext.PerformerImages
                .Include(performerImage => performerImage.Practice)
                .Include(performerImage => performerImage.PerformerProfile)
                .Include(performerImage => performerImage.PerformerProfile.PracticePeople)
                .Include(performerImage => performerImage.PerformerProfile.PracticePeople.Select(practicePerson => practicePerson.PracticePersonLinks))
                .Include(performerImage => performerImage.PerformerProfile.PracticePeople.Select(practicePerson => practicePerson.Role))
                .Include(performerImage => performerImage.PerformerProfile.PracticePeople.Select(practicePerson => practicePerson.Role.RoleType))
                .Include(performerImage => performerImage.PerformerProfile.Performer)
                .First()
                .PerformerProfile;

            return performerProfile;
        }
    }
}
