﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mydentist.MyDSitefinityAPI.ImplementationServices
{
    public class RecruitmentWebsiteFormRequest
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string ContactNumber { get; set; }
        public string Email { get; set; }
        public string DesiredLocation { get; set; }
        public string DesiredLocationLongitude { get; set; }
        public string DesiredLocationLatitude { get; set; }
        public string DesiredRole { get; set; }
        public string GdcNumber { get; set; }
        public string ReferrerName { get; set; }
        public string ReferrerGdcNumber { get; set; }
        public string ReferrerEmailAddress { get; set; }
        public string ReferrerContactNumber { get; set; }
        public string FormName { get; set; }
        public string GAClientId { get; set; }
        public string PostCode { get; set; }
        public string UniversityUndergraduate { get; set; }
        public string UniversityPostgraduate { get; set; }
        public string YearQualifiedUndergraduate { get; set; }
        public string YearQualifiedPostgraduate { get; set; }
        public string CountryOfQualification { get; set; }
        public string PostgraduateQualification { get; set; }
        public string Notes { get; set; }
    }
}
