﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace MyDSitefinityAPI.Models
{
    [Table("Update", Schema = "PatientSupport")]
    public class PatientSupportUpdate
    {
      [Key]
      [Column("Id")]
      public int Id { get; set; }
      public int FeedbackId { get; set; }   
      public int UpdateType { get; set; }   
      public string Information { get; set; }
      public DateTime DateRaised { get; set; }
      public DateTime DateActioned { get; set; }    
      public double RefundedAmount { get; set; }    
      public bool IdhGuidelinesMet { get; set; }
      public DateTime? Deadline { get; set; }         
      public bool Deleted { get; set; } 
      public string LastModifiedBy { get; set; }
      public DateTime LastModifiedOn { get; set; }  
    }
}
