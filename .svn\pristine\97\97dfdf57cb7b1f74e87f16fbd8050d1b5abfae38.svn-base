﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Runtime.Serialization;

namespace Mydentist.MyDSitefinityAPI.Domain
{
    [Table("RecruitmentWebsiteForm", Schema = "Recruitment")]
    public class RecruitmentWebsiteFormModel
    {
        public int Id { get; set; }
        public string FirstName { get; set; }
        public string? LastName { get; set; }
        public string? ContactNumber { get; set; }
        public string Email { get; set; }
        public string? DesiredWorkingLocation { get; set; }
        public double? DesiredWorkingLocationLongitude { get; set; }
        public double? DesiredWorkingLocationLatitude { get; set; }
        public int? DesiredRole { get; set; }
        public string? PostgraduateQualification { get; set; }
        public string? GdcNumber { get; set; }
        public string? FormName { get; set; }
        public int? UniversityUndergraduate { get; set; }
        public int? UniversityPostgraduate { get; set; }
        public DateOnly? YearQualifiedUndergraduate { get; set; }
        public DateOnly? YearQualifiedPostgraduate { get; set; }
        public string? ReferrerName { get; set; }
        public string? ReferrerGdcNumber { get; set; }
        public string? ReferrerEmailAddress { get; set; }
        public string? ReferrerContactNumber { get; set; }
        public int? CountryOfQualification { get; set; }
        public string? PostCode { get; set; }
        public string? GAClientId { get; set; }
        [DatabaseGenerated(DatabaseGeneratedOption.Identity), DataMember]
        public DateTime CreatedDateTime { get; set; } 
        public string? Status { get; set; }
        public string? ErrorMessage { get; set; }
        public DateTime? LastProcessedDateTime { get; set; }
        public string? CRMLeadContactCode { get; set; }
        public string? Notes { get; set; }
        public int? ChannelId { get; set; }
    }
}
