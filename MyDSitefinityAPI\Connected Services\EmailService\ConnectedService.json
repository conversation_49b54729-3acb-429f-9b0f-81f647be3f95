{"ExtendedData": {"inputs": ["http://172.30.1.174/EmailInterfaceServiceV2/Service1.svc"], "collectionTypes": ["System.Collections.Generic.Dictionary`2", "System.Collections.Generic.List`1"], "namespaceMappings": ["*, EmailService"], "references": ["<PERSON>Mapper, {AutoMapper, 12.0.0}", "AutoMapper.Extensions.Microsoft.DependencyInjection, {AutoMapper.Extensions.Microsoft.DependencyInjection, 12.0.0}", "Azure.Core, {Azure.Core, 1.25.0}", "Azure.Identity, {Azure.Identity, 1.7.0}", "C:\\Dev\\MyDSitefinityAPINew\\MyDSitefinityAPI\\obj\\Release\\net6.0\\PubTmp\\Out\\Mydentist.MyDSitefinityApi.ClinicianPortalApi.dll", "C:\\Dev\\MyDSitefinityAPINew\\MyDSitefinityAPI\\obj\\Release\\net6.0\\PubTmp\\Out\\Mydentist.MyDSitefinityAPI.Domain.dll", "C:\\Dev\\MyDSitefinityAPINew\\MyDSitefinityAPI\\obj\\Release\\net6.0\\PubTmp\\Out\\Mydentist.MyDSitefinityAPI.ImplementationServices.dll", "C:\\Dev\\MyDSitefinityAPINew\\MyDSitefinityAPI\\obj\\Release\\net6.0\\PubTmp\\Out\\Mydentist.MyDSitefinityAPI.Persistence.dll", "C:\\Dev\\MyDSitefinityAPINew\\MyDSitefinityAPI\\obj\\Release\\net6.0\\PubTmp\\Out\\Mydentist.MyDSitefinityAPI.WebConfigApi.dll", "C:\\Dev\\MyDSitefinityAPINew\\MyDSitefinityAPI\\obj\\Release\\net6.0\\PubTmp\\Out\\SitefinityServiceCaller.Core.dll", "<PERSON><PERSON><PERSON>Framework, {<PERSON>tityFramework, 6.4.4}", "EntityFramework.SqlServer, {EntityFramework, 6.4.4}", "Microsoft.Bcl.AsyncInterfaces, {Microsoft.Bcl.AsyncInterfaces, 5.0.0}", "Microsoft.Data.SqlClient, {Microsoft.Data.SqlClient, 5.1.0}", "Microsoft.EntityFrameworkCore, {Microsoft.EntityFrameworkCore, 7.0.4}", "Microsoft.EntityFrameworkCore.Abstractions, {Microsoft.EntityFrameworkCore.Abstractions, 7.0.4}", "Microsoft.EntityFrameworkCore.Relational, {Microsoft.EntityFrameworkCore.Relational, 7.0.4}", "Microsoft.EntityFrameworkCore.SqlServer, {Microsoft.EntityFrameworkCore.SqlServer, 7.0.4}", "Microsoft.Extensions.Caching.Abstractions, {Microsoft.Extensions.Caching.Abstractions, 7.0.0}", "Microsoft.Extensions.Caching.Memory, {Microsoft.Extensions.Caching.Memory, 7.0.0}", "Microsoft.Extensions.Configuration.Abstractions, {Microsoft.Extensions.Configuration.Abstractions, 7.0.0}", "Microsoft.Extensions.Configuration.Binder, {Microsoft.Extensions.Configuration.Binder, 6.0.0}", "Microsoft.Extensions.DependencyInjection, {Microsoft.Extensions.DependencyInjection, 7.0.0}", "Microsoft.Extensions.DependencyInjection.Abstractions, {Microsoft.Extensions.DependencyInjection.Abstractions, 7.0.0}", "Microsoft.Extensions.DependencyModel, {Microsoft.Extensions.DependencyModel, 7.0.0}", "Microsoft.Extensions.Logging, {Microsoft.Extensions.Logging, 7.0.0}", "Microsoft.Extensions.Logging.Abstractions, {Microsoft.Extensions.Logging.Abstractions, 7.0.0}", "Microsoft.Extensions.ObjectPool, {Microsoft.Extensions.ObjectPool, 5.0.10}", "Microsoft.Extensions.Options, {Microsoft.Extensions.Options, 7.0.1}", "Microsoft.Extensions.Primitives, {Microsoft.Extensions.Primitives, 7.0.0}", "Microsoft.Identity.Client, {Microsoft.Identity.Client, 4.47.2}", "Microsoft.Identity.Client.Extensions.Msal, {Microsoft.Identity.Client.Extensions.Msal, 2.19.3}", "Microsoft.IdentityModel.Abstractions, {Microsoft.IdentityModel.Abstractions, 6.24.0}", "Microsoft.IdentityModel.JsonWebTokens, {Microsoft.IdentityModel.JsonWebTokens, 6.24.0}", "Microsoft.IdentityModel.Logging, {Microsoft.IdentityModel.Logging, 6.24.0}", "Microsoft.IdentityModel.Protocols, {Microsoft.IdentityModel.Protocols, 6.24.0}", "Microsoft.IdentityModel.Protocols.OpenIdConnect, {Microsoft.IdentityModel.Protocols.OpenIdConnect, 6.24.0}", "Microsoft.IdentityModel.Protocols.WsTrust, {Microsoft.IdentityModel.Protocols.WsTrust, 6.8.0}", "Microsoft.IdentityModel.Tokens, {Microsoft.IdentityModel.Tokens, 6.24.0}", "Microsoft.IdentityModel.Tokens.Saml, {Microsoft.IdentityModel.Tokens.Saml, 6.8.0}", "Microsoft.IdentityModel.Xml, {Microsoft.IdentityModel.Xml, 6.8.0}", "Microsoft.OpenApi, {Microsoft.OpenApi, 1.3.1}", "Microsoft.SqlServer.Server, {Microsoft.SqlServer.Server, 1.0.0}", "Microsoft.Win32.SystemEvents, {Microsoft.Win32.SystemEvents, 7.0.0}", "Newtonsoft.J<PERSON>, {Newtonsoft<PERSON>J<PERSON>, 13.0.3}", "Newtonsoft.Json.<PERSON>, {Newtonsoft.Json.Bson, 1.0.2}", "<PERSON><PERSON><PERSON>, {<PERSON><PERSON><PERSON>, 3.3.0}", "<PERSON><PERSON><PERSON>, {Serilog, 2.10.0}", "Serilog.Extensions.Logging, {Serilog.Extensions.Logging, 3.1.0}", "Serilog.Extensions.Logging.File, {Serilog.Extensions.Logging.File, 3.0.0}", "Serilog.Formatting.Compact, {Serilog.Formatting.Compact, 1.1.0}", "Serilog.Sinks.Async, {Serilog.Sinks.Async, 1.5.0}", "Serilog.Sinks.File, {Serilog.Sinks.File, 3.2.0}", "Serilog.Sinks.RollingFile, {Serilog.Sinks.RollingFile, 3.3.0}", "Swashbuckle.AspNetCore.Filters, {Swashbuckle.AspNetCore.Filters, 7.0.5}", "Swashbuckle.AspNetCore.Filters.Abstractions, {Swashbuckle.AspNetCore.Filters.Abstractions, 7.0.5}", "Swashbuckle.AspNetCore.Swagger, {Swashbuckle.AspNetCore.Swagger, 6.4.0}", "Swashbuckle.AspNetCore.SwaggerGen, {Swashbuckle.AspNetCore.SwaggerGen, 6.2.3}", "Swashbuckle.AspNetCore.SwaggerUI, {Swashbuckle.AspNetCore.SwaggerUI, 6.2.3}", "System.CodeDom, {System.CodeDom, 4.7.0}", "System.ComponentModel.Annotations, {System.ComponentModel.Annotations, 4.7.0}", "System.Configuration.ConfigurationManager, {System.Configuration.ConfigurationManager, 7.0.0}", "System.Data.SqlClient, {System.Data.SqlClient, 4.8.1}", "System.Diagnostics.DiagnosticSource, {System.Diagnostics.DiagnosticSource, 6.0.0}", "System.Drawing.Common, {System.Drawing.Common, 7.0.0}", "System.Formats.Asn1, {System.Formats.Asn1, 6.0.0}", "System.IdentityModel.Tokens.Jwt, {System.IdentityModel.Tokens.Jwt, 6.24.0}", "System.IO, {System.IO, 4.3.0}", "System.IO.FileSystem, {System.IO.FileSystem, 4.3.0}", "System.IO.FileSystem.Primitives, {System.IO.FileSystem.Primitives, 4.3.0}", "System.Memory.Data, {System.Memory.Data, 1.0.2}", "System.Reflection, {System.Reflection, 4.3.0}", "System.Reflection.DispatchProxy, {System.Reflection.DispatchProxy, 4.7.1}", "System.Reflection.Primitives, {System.Reflection.Primitives, 4.3.0}", "System.Runtime, {System.Runtime, 4.3.0}", "System.Runtime.CompilerServices.Unsafe, {System.Runtime.CompilerServices.Unsafe, 6.0.0}", "System.Runtime.Handles, {System.Runtime.Handles, 4.3.0}", "System.Runtime.InteropServices, {System.Runtime.InteropServices, 4.3.0}", "System.Security.AccessControl, {System.Security.AccessControl, 6.0.0}", "System.Security.Cryptography.Cng, {System.Security.Cryptography.Cng, 5.0.0}", "System.Security.Cryptography.Pkcs, {System.Security.Cryptography.Pkcs, 6.0.1}", "System.Security.Cryptography.ProtectedData, {System.Security.Cryptography.ProtectedData, 7.0.0}", "System.Security.Cryptography.Xml, {System.Security.Cryptography.Xml, 6.0.1}", "System.Security.Permissions, {System.Security.Permissions, 7.0.0}", "System.Security.Principal.Windows, {System.Security.Principal.Windows, 5.0.0}", "System.ServiceModel, {System.ServiceModel.Primitives, 4.10.2}", "System.ServiceModel.Duplex, {System.ServiceModel.Duplex, 4.10.2}", "System.ServiceModel.Federation, {System.ServiceModel.Federation, 4.10.2}", "System.ServiceModel.Http, {System.ServiceModel.Http, 4.10.2}", "System.ServiceModel.NetTcp, {System.ServiceModel.NetTcp, 4.10.2}", "System.ServiceModel.Primitives, {System.ServiceModel.Primitives, 4.10.2}", "System.ServiceModel.Security, {System.ServiceModel.Security, 4.10.2}", "System.Text.Encoding, {System.Text.Encoding, 4.3.0}", "System.Text.Encoding.Extensions, {System.Text.Encoding.Extensions, 4.3.0}", "System.Text.Encodings.Web, {System.Text.Encodings.Web, 7.0.0}", "System.<PERSON>.<PERSON>, {System.Text.<PERSON>, 7.0.0}", "System.Threading, {System.Threading, 4.3.0}", "System.Threading.Tasks, {System.Threading.Tasks, 4.3.0}", "System.Threading.Timer, {System.Threading.Timer, 4.0.1}", "System.Windows.Extensions, {System.Windows.Extensions, 7.0.0}", "System.Xml.ReaderWriter, {System.Xml.ReaderWriter, 4.3.0}", "System.Xml.XmlDocument, {System.Xml.XmlDocument, 4.3.0}"], "targetFramework": "net6.0", "typeReuseMode": "All"}}