﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace mydentist
{
    using System.Runtime.Serialization;
    
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Result", Namespace="http://tempuri.org/")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(System.Collections.Generic.List<mydentist.WebOpeningHour>))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(mydentist.WebOpeningHour))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(mydentist.WeekDay))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(System.Collections.Generic.List<mydentist.WebLimitedCompanyFooter>))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(mydentist.WebLimitedCompanyFooter))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(System.Collections.Generic.List<mydentist.WebPartnershipFooter>))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(mydentist.WebPartnershipFooter))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(System.Collections.Generic.List<mydentist.WebPartner>))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(mydentist.WebPartner))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(System.Collections.Generic.List<mydentist.WebReview>))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(mydentist.WebReview))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(mydentist.WebCaseStudy))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(System.Collections.Generic.List<mydentist.WebCaseStudyImage>))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(mydentist.WebCaseStudyImage))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(mydentist.WebFacility))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(mydentist.WebClinician))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(mydentist.WebClinicianImage))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(mydentist.BlogModel))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(mydentist.BlogImageModel))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(mydentist.WebPractice))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(mydentist.PracticeType))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(System.Collections.Generic.List<mydentist.WebFreeAssessmentEvent>))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(mydentist.WebFreeAssessmentEvent))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(System.Collections.Generic.List<mydentist.WebClinician>))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(System.Collections.Generic.List<mydentist.WebTreatment>))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(mydentist.WebTreatment))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(mydentist.WebTreatmentCost))]
    public partial class Result : object
    {
        
        private string ErrorField;
        
        private object ValueField;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string Error
        {
            get
            {
                return this.ErrorField;
            }
            set
            {
                this.ErrorField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public object Value
        {
            get
            {
                return this.ValueField;
            }
            set
            {
                this.ValueField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebOpeningHour", Namespace="http://tempuri.org/")]
    public partial class WebOpeningHour : object
    {
        
        private string EndTimeField;
        
        private bool IsClosedField;
        
        private string StartTimeField;
        
        private mydentist.WeekDay WeekDayField;
        
        private int OrderField;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string EndTime
        {
            get
            {
                return this.EndTimeField;
            }
            set
            {
                this.EndTimeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public bool IsClosed
        {
            get
            {
                return this.IsClosedField;
            }
            set
            {
                this.IsClosedField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string StartTime
        {
            get
            {
                return this.StartTimeField;
            }
            set
            {
                this.StartTimeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public mydentist.WeekDay WeekDay
        {
            get
            {
                return this.WeekDayField;
            }
            set
            {
                this.WeekDayField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=4)]
        public int Order
        {
            get
            {
                return this.OrderField;
            }
            set
            {
                this.OrderField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WeekDay", Namespace="http://tempuri.org/")]
    public enum WeekDay : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        NotFound = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Monday = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Tuesday = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Wednesday = 3,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Thursday = 4,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Friday = 5,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Saturday = 6,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Sunday = 7,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebLimitedCompanyFooter", Namespace="http://tempuri.org/")]
    public partial class WebLimitedCompanyFooter : object
    {
        
        private string LimitedCompanyNameField;
        
        private string RegOfficeAddressField;
        
        private string RegOfficenumberField;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string LimitedCompanyName
        {
            get
            {
                return this.LimitedCompanyNameField;
            }
            set
            {
                this.LimitedCompanyNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string RegOfficeAddress
        {
            get
            {
                return this.RegOfficeAddressField;
            }
            set
            {
                this.RegOfficeAddressField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string RegOfficenumber
        {
            get
            {
                return this.RegOfficenumberField;
            }
            set
            {
                this.RegOfficenumberField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebPartnershipFooter", Namespace="http://tempuri.org/")]
    public partial class WebPartnershipFooter : object
    {
        
        private string DentalPartnershipField;
        
        private System.Collections.Generic.List<mydentist.WebPartner> PartnersField;
        
        private string PartnershipNameField;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string DentalPartnership
        {
            get
            {
                return this.DentalPartnershipField;
            }
            set
            {
                this.DentalPartnershipField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public System.Collections.Generic.List<mydentist.WebPartner> Partners
        {
            get
            {
                return this.PartnersField;
            }
            set
            {
                this.PartnersField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string PartnershipName
        {
            get
            {
                return this.PartnershipNameField;
            }
            set
            {
                this.PartnershipNameField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebPartner", Namespace="http://tempuri.org/")]
    public partial class WebPartner : object
    {
        
        private string NameField;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebReview", Namespace="http://tempuri.org/")]
    public partial class WebReview : object
    {
        
        private string CommentField;
        
        private System.Nullable<int> RatingField;
        
        private System.DateTime ReviewDateField;
        
        private string SourceField;
        
        private string TitleField;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string Comment
        {
            get
            {
                return this.CommentField;
            }
            set
            {
                this.CommentField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public System.Nullable<int> Rating
        {
            get
            {
                return this.RatingField;
            }
            set
            {
                this.RatingField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public System.DateTime ReviewDate
        {
            get
            {
                return this.ReviewDateField;
            }
            set
            {
                this.ReviewDateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string Source
        {
            get
            {
                return this.SourceField;
            }
            set
            {
                this.SourceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string Title
        {
            get
            {
                return this.TitleField;
            }
            set
            {
                this.TitleField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebCaseStudy", Namespace="http://tempuri.org/")]
    public partial class WebCaseStudy : object
    {
        
        private string CasenotesField;
        
        private string CaseStudyTypeField;
        
        private bool ConsentForFacePhotoField;
        
        private bool ConsentForTeethPhotoField;
        
        private bool ConsentForTestimonialField;
        
        private string IdField;
        
        private string PatientNameField;
        
        private string PatientTestimonialField;
        
        private string PracticeIdField;
        
        private string TitleField;
        
        private bool UploadedFaceField;
        
        private bool UploadedTeethField;
        
        private string WebAfterFaceImageIdField;
        
        private string WebAfterFaceThumbnailIdField;
        
        private string WebAfterTeethImageIdField;
        
        private string WebAfterTeethThumbnailIdField;
        
        private string WebBeforeFaceImageIdField;
        
        private string WebBeforeFaceThumbnailIdField;
        
        private string WebBeforeTeethImageIdField;
        
        private string WebBeforeTeethThumbnailIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string Casenotes
        {
            get
            {
                return this.CasenotesField;
            }
            set
            {
                this.CasenotesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string CaseStudyType
        {
            get
            {
                return this.CaseStudyTypeField;
            }
            set
            {
                this.CaseStudyTypeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=2)]
        public bool ConsentForFacePhoto
        {
            get
            {
                return this.ConsentForFacePhotoField;
            }
            set
            {
                this.ConsentForFacePhotoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=3)]
        public bool ConsentForTeethPhoto
        {
            get
            {
                return this.ConsentForTeethPhotoField;
            }
            set
            {
                this.ConsentForTeethPhotoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=4)]
        public bool ConsentForTestimonial
        {
            get
            {
                return this.ConsentForTestimonialField;
            }
            set
            {
                this.ConsentForTestimonialField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public string Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=6)]
        public string PatientName
        {
            get
            {
                return this.PatientNameField;
            }
            set
            {
                this.PatientNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=7)]
        public string PatientTestimonial
        {
            get
            {
                return this.PatientTestimonialField;
            }
            set
            {
                this.PatientTestimonialField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=8)]
        public string PracticeId
        {
            get
            {
                return this.PracticeIdField;
            }
            set
            {
                this.PracticeIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=9)]
        public string Title
        {
            get
            {
                return this.TitleField;
            }
            set
            {
                this.TitleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=10)]
        public bool UploadedFace
        {
            get
            {
                return this.UploadedFaceField;
            }
            set
            {
                this.UploadedFaceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=11)]
        public bool UploadedTeeth
        {
            get
            {
                return this.UploadedTeethField;
            }
            set
            {
                this.UploadedTeethField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false, Order=12)]
        public string WebAfterFaceImageId
        {
            get
            {
                return this.WebAfterFaceImageIdField;
            }
            set
            {
                this.WebAfterFaceImageIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false, Order=13)]
        public string WebAfterFaceThumbnailId
        {
            get
            {
                return this.WebAfterFaceThumbnailIdField;
            }
            set
            {
                this.WebAfterFaceThumbnailIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false, Order=14)]
        public string WebAfterTeethImageId
        {
            get
            {
                return this.WebAfterTeethImageIdField;
            }
            set
            {
                this.WebAfterTeethImageIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false, Order=15)]
        public string WebAfterTeethThumbnailId
        {
            get
            {
                return this.WebAfterTeethThumbnailIdField;
            }
            set
            {
                this.WebAfterTeethThumbnailIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false, Order=16)]
        public string WebBeforeFaceImageId
        {
            get
            {
                return this.WebBeforeFaceImageIdField;
            }
            set
            {
                this.WebBeforeFaceImageIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false, Order=17)]
        public string WebBeforeFaceThumbnailId
        {
            get
            {
                return this.WebBeforeFaceThumbnailIdField;
            }
            set
            {
                this.WebBeforeFaceThumbnailIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false, Order=18)]
        public string WebBeforeTeethImageId
        {
            get
            {
                return this.WebBeforeTeethImageIdField;
            }
            set
            {
                this.WebBeforeTeethImageIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false, Order=19)]
        public string WebBeforeTeethThumbnailId
        {
            get
            {
                return this.WebBeforeTeethThumbnailIdField;
            }
            set
            {
                this.WebBeforeTeethThumbnailIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebCaseStudyImage", Namespace="http://tempuri.org/")]
    public partial class WebCaseStudyImage : object
    {
        
        private string FileextensionField;
        
        private string FieldNameField;
        
        private string FilenameField;
        
        private string FilepathField;
        
        private string ImageGalleryField;
        
        private string ImageIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string Fileextension
        {
            get
            {
                return this.FileextensionField;
            }
            set
            {
                this.FileextensionField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string FieldName
        {
            get
            {
                return this.FieldNameField;
            }
            set
            {
                this.FieldNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string Filename
        {
            get
            {
                return this.FilenameField;
            }
            set
            {
                this.FilenameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public string Filepath
        {
            get
            {
                return this.FilepathField;
            }
            set
            {
                this.FilepathField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public string ImageGallery
        {
            get
            {
                return this.ImageGalleryField;
            }
            set
            {
                this.ImageGalleryField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false, Order=5)]
        public string ImageId
        {
            get
            {
                return this.ImageIdField;
            }
            set
            {
                this.ImageIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebFacility", Namespace="http://tempuri.org/")]
    public partial class WebFacility : object
    {
        
        private bool HasDisabledAccessField;
        
        private bool HasParkingField;
        
        private bool HasWifiField;
        
        private string PracticeFacilitiesIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public bool HasDisabledAccess
        {
            get
            {
                return this.HasDisabledAccessField;
            }
            set
            {
                this.HasDisabledAccessField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public bool HasParking
        {
            get
            {
                return this.HasParkingField;
            }
            set
            {
                this.HasParkingField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public bool HasWifi
        {
            get
            {
                return this.HasWifiField;
            }
            set
            {
                this.HasWifiField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string PracticeFacilitiesId
        {
            get
            {
                return this.PracticeFacilitiesIdField;
            }
            set
            {
                this.PracticeFacilitiesIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebClinician", Namespace="http://tempuri.org/")]
    public partial class WebClinician : object
    {
        
        private string BioField;
        
        private string CountryQualifiedField;
        
        private string DentalSchoolQualifiedField;
        
        private string GdcNoField;
        
        private string GenderField;
        
        private string NameField;
        
        private int PracticeIdField;
        
        private string RoleField;
        
        private string TitleField;
        
        private string YearQualifiedField;
        
        private bool DisplayonSiteField;
        
        private string MediaReferenceIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string Bio
        {
            get
            {
                return this.BioField;
            }
            set
            {
                this.BioField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string CountryQualified
        {
            get
            {
                return this.CountryQualifiedField;
            }
            set
            {
                this.CountryQualifiedField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string DentalSchoolQualified
        {
            get
            {
                return this.DentalSchoolQualifiedField;
            }
            set
            {
                this.DentalSchoolQualifiedField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string GdcNo
        {
            get
            {
                return this.GdcNoField;
            }
            set
            {
                this.GdcNoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string Gender
        {
            get
            {
                return this.GenderField;
            }
            set
            {
                this.GenderField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int PracticeId
        {
            get
            {
                return this.PracticeIdField;
            }
            set
            {
                this.PracticeIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string Role
        {
            get
            {
                return this.RoleField;
            }
            set
            {
                this.RoleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string Title
        {
            get
            {
                return this.TitleField;
            }
            set
            {
                this.TitleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string YearQualified
        {
            get
            {
                return this.YearQualifiedField;
            }
            set
            {
                this.YearQualifiedField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=10)]
        public bool DisplayonSite
        {
            get
            {
                return this.DisplayonSiteField;
            }
            set
            {
                this.DisplayonSiteField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false, Order=11)]
        public string MediaReferenceId
        {
            get
            {
                return this.MediaReferenceIdField;
            }
            set
            {
                this.MediaReferenceIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebClinicianImage", Namespace="http://tempuri.org/")]
    public partial class WebClinicianImage : object
    {
        
        private string FileextensionField;
        
        private string FieldNameField;
        
        private string FilenameField;
        
        private string FilepathField;
        
        private string ImageGalleryField;
        
        private string ImageIdField;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string Fileextension
        {
            get
            {
                return this.FileextensionField;
            }
            set
            {
                this.FileextensionField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string FieldName
        {
            get
            {
                return this.FieldNameField;
            }
            set
            {
                this.FieldNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string Filename
        {
            get
            {
                return this.FilenameField;
            }
            set
            {
                this.FilenameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public string Filepath
        {
            get
            {
                return this.FilepathField;
            }
            set
            {
                this.FilepathField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public string ImageGallery
        {
            get
            {
                return this.ImageGalleryField;
            }
            set
            {
                this.ImageGalleryField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false, Order=5)]
        public string ImageId
        {
            get
            {
                return this.ImageIdField;
            }
            set
            {
                this.ImageIdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="BlogModel", Namespace="http://tempuri.org/")]
    public partial class BlogModel : object
    {
        
        private int BlogIdField;
        
        private string TitleField;
        
        private string SummaryField;
        
        private string ContentField;
        
        private string BlogUrlField;
        
        private string FacebookUrlField;
        
        private string FacebookTypeField;
        
        private string FacebookTitleField;
        
        private string FacebookDescriptionField;
        
        private string FacebookImageField;
        
        private string CreatedByField;
        
        private System.DateTime CreatedDateField;
        
        private bool BlogstatusField;
        
        private mydentist.BlogImageModel blogImageField;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int BlogId
        {
            get
            {
                return this.BlogIdField;
            }
            set
            {
                this.BlogIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string Title
        {
            get
            {
                return this.TitleField;
            }
            set
            {
                this.TitleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string Summary
        {
            get
            {
                return this.SummaryField;
            }
            set
            {
                this.SummaryField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public string Content
        {
            get
            {
                return this.ContentField;
            }
            set
            {
                this.ContentField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public string BlogUrl
        {
            get
            {
                return this.BlogUrlField;
            }
            set
            {
                this.BlogUrlField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public string FacebookUrl
        {
            get
            {
                return this.FacebookUrlField;
            }
            set
            {
                this.FacebookUrlField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=6)]
        public string FacebookType
        {
            get
            {
                return this.FacebookTypeField;
            }
            set
            {
                this.FacebookTypeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=7)]
        public string FacebookTitle
        {
            get
            {
                return this.FacebookTitleField;
            }
            set
            {
                this.FacebookTitleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=8)]
        public string FacebookDescription
        {
            get
            {
                return this.FacebookDescriptionField;
            }
            set
            {
                this.FacebookDescriptionField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=9)]
        public string FacebookImage
        {
            get
            {
                return this.FacebookImageField;
            }
            set
            {
                this.FacebookImageField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=10)]
        public string CreatedBy
        {
            get
            {
                return this.CreatedByField;
            }
            set
            {
                this.CreatedByField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=11)]
        public System.DateTime CreatedDate
        {
            get
            {
                return this.CreatedDateField;
            }
            set
            {
                this.CreatedDateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=12)]
        public bool Blogstatus
        {
            get
            {
                return this.BlogstatusField;
            }
            set
            {
                this.BlogstatusField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=13)]
        public mydentist.BlogImageModel blogImage
        {
            get
            {
                return this.blogImageField;
            }
            set
            {
                this.blogImageField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="BlogImageModel", Namespace="http://tempuri.org/")]
    public partial class BlogImageModel : object
    {
        
        private int BlogIdField;
        
        private string BlogImageIdField;
        
        private string ImageNameField;
        
        private string fileExtensionField;
        
        private string ImagePathField;
        
        private string CreatedByField;
        
        private System.DateTime CreatedDateField;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int BlogId
        {
            get
            {
                return this.BlogIdField;
            }
            set
            {
                this.BlogIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, EmitDefaultValue=false)]
        public string BlogImageId
        {
            get
            {
                return this.BlogImageIdField;
            }
            set
            {
                this.BlogImageIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string ImageName
        {
            get
            {
                return this.ImageNameField;
            }
            set
            {
                this.ImageNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string fileExtension
        {
            get
            {
                return this.fileExtensionField;
            }
            set
            {
                this.fileExtensionField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public string ImagePath
        {
            get
            {
                return this.ImagePathField;
            }
            set
            {
                this.ImagePathField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public string CreatedBy
        {
            get
            {
                return this.CreatedByField;
            }
            set
            {
                this.CreatedByField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=6)]
        public System.DateTime CreatedDate
        {
            get
            {
                return this.CreatedDateField;
            }
            set
            {
                this.CreatedDateField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebPractice", Namespace="http://tempuri.org/")]
    public partial class WebPractice : object
    {
        
        private string AddressLine1Field;
        
        private string AddressLine2Field;
        
        private string AddressLine3Field;
        
        private string CityNameField;
        
        private string ClinicNameField;
        
        private string CountryNameField;
        
        private string EmailAddressField;
        
        private string FriendlyEmailAddressField;
        
        private string GoogleApiCityField;
        
        private bool HasShortTermOrthoPromoField;
        
        private string IdField;
        
        private bool IsAcceptingNHSPatientsField;
        
        private bool IsAcceptingPrivatePatientsField;
        
        private bool IsAcceptNewPatientsField;
        
        private bool IsCerezenField;
        
        private bool IsDentureField;
        
        private bool IsDirectAccessField;
        
        private bool IsDriverField;
        
        private bool IsFacialaestheticsField;
        
        private bool IsFreeAssessmentsField;
        
        private bool IsIdhField;
        
        private bool IsImplantsField;
        
        private bool IsInvisalignField;
        
        private bool IsInvisalignGoField;
        
        private bool IsOnlinebookingAvailableField;
        
        private bool IsOnlineEnabledField;
        
        private bool IsClearCorrectField;
        
        private bool IsQuickStraightTeethField;
        
        private bool IsSaveFaceField;
        
        private bool IsShortTermOrthoField;
        
        private bool IsSixMonthSmilesField;
        
        private bool IsStudentPracticeField;
        
        private bool IsSuperSaturdayField;
        
        private bool IsTeethStraighteningField;
        
        private bool IsZoomField;
        
        private double LatitudeField;
        
        private double LongitudeField;
        
        private string OfficeRecordIdField;
        
        private string PhoneNumberField;
        
        private string PostcodeField;
        
        private mydentist.PracticeType PracticeTypeField;
        
        private string RecEmailAddressField;
        
        private string RegionNameField;
        
        private string TitleField;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string AddressLine1
        {
            get
            {
                return this.AddressLine1Field;
            }
            set
            {
                this.AddressLine1Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string AddressLine2
        {
            get
            {
                return this.AddressLine2Field;
            }
            set
            {
                this.AddressLine2Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string AddressLine3
        {
            get
            {
                return this.AddressLine3Field;
            }
            set
            {
                this.AddressLine3Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string CityName
        {
            get
            {
                return this.CityNameField;
            }
            set
            {
                this.CityNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string ClinicName
        {
            get
            {
                return this.ClinicNameField;
            }
            set
            {
                this.ClinicNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string CountryName
        {
            get
            {
                return this.CountryNameField;
            }
            set
            {
                this.CountryNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string EmailAddress
        {
            get
            {
                return this.EmailAddressField;
            }
            set
            {
                this.EmailAddressField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string FriendlyEmailAddress
        {
            get
            {
                return this.FriendlyEmailAddressField;
            }
            set
            {
                this.FriendlyEmailAddressField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string GoogleApiCity
        {
            get
            {
                return this.GoogleApiCityField;
            }
            set
            {
                this.GoogleApiCityField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public bool HasShortTermOrthoPromo
        {
            get
            {
                return this.HasShortTermOrthoPromoField;
            }
            set
            {
                this.HasShortTermOrthoPromoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public bool IsAcceptingNHSPatients
        {
            get
            {
                return this.IsAcceptingNHSPatientsField;
            }
            set
            {
                this.IsAcceptingNHSPatientsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public bool IsAcceptingPrivatePatients
        {
            get
            {
                return this.IsAcceptingPrivatePatientsField;
            }
            set
            {
                this.IsAcceptingPrivatePatientsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=13)]
        public bool IsAcceptNewPatients
        {
            get
            {
                return this.IsAcceptNewPatientsField;
            }
            set
            {
                this.IsAcceptNewPatientsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=14)]
        public bool IsCerezen
        {
            get
            {
                return this.IsCerezenField;
            }
            set
            {
                this.IsCerezenField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=15)]
        public bool IsDenture
        {
            get
            {
                return this.IsDentureField;
            }
            set
            {
                this.IsDentureField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=16)]
        public bool IsDirectAccess
        {
            get
            {
                return this.IsDirectAccessField;
            }
            set
            {
                this.IsDirectAccessField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=17)]
        public bool IsDriver
        {
            get
            {
                return this.IsDriverField;
            }
            set
            {
                this.IsDriverField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=18)]
        public bool IsFacialaesthetics
        {
            get
            {
                return this.IsFacialaestheticsField;
            }
            set
            {
                this.IsFacialaestheticsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=19)]
        public bool IsFreeAssessments
        {
            get
            {
                return this.IsFreeAssessmentsField;
            }
            set
            {
                this.IsFreeAssessmentsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=20)]
        public bool IsIdh
        {
            get
            {
                return this.IsIdhField;
            }
            set
            {
                this.IsIdhField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=21)]
        public bool IsImplants
        {
            get
            {
                return this.IsImplantsField;
            }
            set
            {
                this.IsImplantsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=22)]
        public bool IsInvisalign
        {
            get
            {
                return this.IsInvisalignField;
            }
            set
            {
                this.IsInvisalignField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=23)]
        public bool IsInvisalignGo
        {
            get
            {
                return this.IsInvisalignGoField;
            }
            set
            {
                this.IsInvisalignGoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=24)]
        public bool IsOnlinebookingAvailable
        {
            get
            {
                return this.IsOnlinebookingAvailableField;
            }
            set
            {
                this.IsOnlinebookingAvailableField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=25)]
        public bool IsOnlineEnabled
        {
            get
            {
                return this.IsOnlineEnabledField;
            }
            set
            {
                this.IsOnlineEnabledField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=26)]
        public bool IsClearCorrect
        {
            get
            {
                return this.IsClearCorrectField;
            }
            set
            {
                this.IsClearCorrectField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=27)]
        public bool IsQuickStraightTeeth
        {
            get
            {
                return this.IsQuickStraightTeethField;
            }
            set
            {
                this.IsQuickStraightTeethField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=28)]
        public bool IsSaveFace
        {
            get
            {
                return this.IsSaveFaceField;
            }
            set
            {
                this.IsSaveFaceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=29)]
        public bool IsShortTermOrtho
        {
            get
            {
                return this.IsShortTermOrthoField;
            }
            set
            {
                this.IsShortTermOrthoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=30)]
        public bool IsSixMonthSmiles
        {
            get
            {
                return this.IsSixMonthSmilesField;
            }
            set
            {
                this.IsSixMonthSmilesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=31)]
        public bool IsStudentPractice
        {
            get
            {
                return this.IsStudentPracticeField;
            }
            set
            {
                this.IsStudentPracticeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=32)]
        public bool IsSuperSaturday
        {
            get
            {
                return this.IsSuperSaturdayField;
            }
            set
            {
                this.IsSuperSaturdayField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=33)]
        public bool IsTeethStraightening
        {
            get
            {
                return this.IsTeethStraighteningField;
            }
            set
            {
                this.IsTeethStraighteningField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=34)]
        public bool IsZoom
        {
            get
            {
                return this.IsZoomField;
            }
            set
            {
                this.IsZoomField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=35)]
        public double Latitude
        {
            get
            {
                return this.LatitudeField;
            }
            set
            {
                this.LatitudeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=36)]
        public double Longitude
        {
            get
            {
                return this.LongitudeField;
            }
            set
            {
                this.LongitudeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=37)]
        public string OfficeRecordId
        {
            get
            {
                return this.OfficeRecordIdField;
            }
            set
            {
                this.OfficeRecordIdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=38)]
        public string PhoneNumber
        {
            get
            {
                return this.PhoneNumberField;
            }
            set
            {
                this.PhoneNumberField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=39)]
        public string Postcode
        {
            get
            {
                return this.PostcodeField;
            }
            set
            {
                this.PostcodeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=40)]
        public mydentist.PracticeType PracticeType
        {
            get
            {
                return this.PracticeTypeField;
            }
            set
            {
                this.PracticeTypeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=41)]
        public string RecEmailAddress
        {
            get
            {
                return this.RecEmailAddressField;
            }
            set
            {
                this.RecEmailAddressField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=42)]
        public string RegionName
        {
            get
            {
                return this.RegionNameField;
            }
            set
            {
                this.RegionNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=43)]
        public string Title
        {
            get
            {
                return this.TitleField;
            }
            set
            {
                this.TitleField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="PracticeType", Namespace="http://tempuri.org/")]
    public enum PracticeType : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        NotFound = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Private = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Ortho = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        NHS = 3,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Mixed = 4,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebFreeAssessmentEvent", Namespace="http://tempuri.org/")]
    public partial class WebFreeAssessmentEvent : object
    {
        
        private System.DateTime DateField;
        
        private string FinishTimeField;
        
        private bool HasFreeDemoField;
        
        private string IdField;
        
        private string StartTimeField;
        
        private string TreatmentsField;
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public System.DateTime Date
        {
            get
            {
                return this.DateField;
            }
            set
            {
                this.DateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string FinishTime
        {
            get
            {
                return this.FinishTimeField;
            }
            set
            {
                this.FinishTimeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public bool HasFreeDemo
        {
            get
            {
                return this.HasFreeDemoField;
            }
            set
            {
                this.HasFreeDemoField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string StartTime
        {
            get
            {
                return this.StartTimeField;
            }
            set
            {
                this.StartTimeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string Treatments
        {
            get
            {
                return this.TreatmentsField;
            }
            set
            {
                this.TreatmentsField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebTreatment", Namespace="http://tempuri.org/")]
    public partial class WebTreatment : object
    {
        
        private mydentist.WebTreatmentCost treatmentCostsField;
        
        private int IdField;
        
        private string TreatmentNameField;
        
        private string TreatmentUrlField;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public mydentist.WebTreatmentCost treatmentCosts
        {
            get
            {
                return this.treatmentCostsField;
            }
            set
            {
                this.treatmentCostsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true, Order=1)]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string TreatmentName
        {
            get
            {
                return this.TreatmentNameField;
            }
            set
            {
                this.TreatmentNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public string TreatmentUrl
        {
            get
            {
                return this.TreatmentUrlField;
            }
            set
            {
                this.TreatmentUrlField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WebTreatmentCost", Namespace="http://tempuri.org/")]
    public partial class WebTreatmentCost : object
    {
        
        private string CategoriesField;
        
        private double FromField;
        
        private double ToField;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false)]
        public string Categories
        {
            get
            {
                return this.CategoriesField;
            }
            set
            {
                this.CategoriesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public double From
        {
            get
            {
                return this.FromField;
            }
            set
            {
                this.FromField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public double To
        {
            get
            {
                return this.ToField;
            }
            set
            {
                this.ToField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ServiceModel.ServiceContractAttribute(ConfigurationName="mydentist.mydentistcloudImportSoap")]
    public interface mydentistcloudImportSoap
    {
        
        // CODEGEN: Generating message contract since element name practiceId from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/DeleteClinician", ReplyAction="*")]
        mydentist.DeleteClinicianResponse DeleteClinician(mydentist.DeleteClinicianRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/DeleteClinician", ReplyAction="*")]
        System.Threading.Tasks.Task<mydentist.DeleteClinicianResponse> DeleteClinicianAsync(mydentist.DeleteClinicianRequest request);
        
        // CODEGEN: Generating message contract since element name imageid from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UploadBlogImageCloud", ReplyAction="*")]
        mydentist.UploadBlogImageCloudResponse UploadBlogImageCloud(mydentist.UploadBlogImageCloudRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UploadBlogImageCloud", ReplyAction="*")]
        System.Threading.Tasks.Task<mydentist.UploadBlogImageCloudResponse> UploadBlogImageCloudAsync(mydentist.UploadBlogImageCloudRequest request);
        
        // CODEGEN: Generating message contract since element name casestudyId from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/DeleteCasestudy", ReplyAction="*")]
        mydentist.DeleteCasestudyResponse DeleteCasestudy(mydentist.DeleteCasestudyRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/DeleteCasestudy", ReplyAction="*")]
        System.Threading.Tasks.Task<mydentist.DeleteCasestudyResponse> DeleteCasestudyAsync(mydentist.DeleteCasestudyRequest request);
        
        // CODEGEN: Generating message contract since element name practiceId from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UpdateOpeninghrsCloud", ReplyAction="*")]
        mydentist.UpdateOpeninghrsCloudResponse UpdateOpeninghrsCloud(mydentist.UpdateOpeninghrsCloudRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UpdateOpeninghrsCloud", ReplyAction="*")]
        System.Threading.Tasks.Task<mydentist.UpdateOpeninghrsCloudResponse> UpdateOpeninghrsCloudAsync(mydentist.UpdateOpeninghrsCloudRequest request);
        
        // CODEGEN: Generating message contract since element name practiceId from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UpdatePracticeCCMCloud", ReplyAction="*")]
        mydentist.UpdatePracticeCCMCloudResponse UpdatePracticeCCMCloud(mydentist.UpdatePracticeCCMCloudRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UpdatePracticeCCMCloud", ReplyAction="*")]
        System.Threading.Tasks.Task<mydentist.UpdatePracticeCCMCloudResponse> UpdatePracticeCCMCloudAsync(mydentist.UpdatePracticeCCMCloudRequest request);
        
        // CODEGEN: Generating message contract since element name practiceId from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UpdateReviewsCloud", ReplyAction="*")]
        mydentist.UpdateReviewsCloudResponse UpdateReviewsCloud(mydentist.UpdateReviewsCloudRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UpdateReviewsCloud", ReplyAction="*")]
        System.Threading.Tasks.Task<mydentist.UpdateReviewsCloudResponse> UpdateReviewsCloudAsync(mydentist.UpdateReviewsCloudRequest request);
        
        // CODEGEN: Generating message contract since element name webCase from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/CreateCasetudyCloud", ReplyAction="*")]
        mydentist.CreateCasetudyCloudResponse CreateCasetudyCloud(mydentist.CreateCasetudyCloudRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/CreateCasetudyCloud", ReplyAction="*")]
        System.Threading.Tasks.Task<mydentist.CreateCasetudyCloudResponse> CreateCasetudyCloudAsync(mydentist.CreateCasetudyCloudRequest request);
        
        // CODEGEN: Generating message contract since element name practiceId from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UpdateFacilitiesCloud", ReplyAction="*")]
        mydentist.UpdateFacilitiesCloudResponse UpdateFacilitiesCloud(mydentist.UpdateFacilitiesCloudRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UpdateFacilitiesCloud", ReplyAction="*")]
        System.Threading.Tasks.Task<mydentist.UpdateFacilitiesCloudResponse> UpdateFacilitiesCloudAsync(mydentist.UpdateFacilitiesCloudRequest request);
        
        // CODEGEN: Generating message contract since element name webClinician from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UpdateClinicianCloud", ReplyAction="*")]
        mydentist.UpdateClinicianCloudResponse UpdateClinicianCloud(mydentist.UpdateClinicianCloudRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UpdateClinicianCloud", ReplyAction="*")]
        System.Threading.Tasks.Task<mydentist.UpdateClinicianCloudResponse> UpdateClinicianCloudAsync(mydentist.UpdateClinicianCloudRequest request);
        
        // CODEGEN: Generating message contract since element name blog from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UnPublishBlogCloud", ReplyAction="*")]
        mydentist.UnPublishBlogCloudResponse UnPublishBlogCloud(mydentist.UnPublishBlogCloudRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UnPublishBlogCloud", ReplyAction="*")]
        System.Threading.Tasks.Task<mydentist.UnPublishBlogCloudResponse> UnPublishBlogCloudAsync(mydentist.UnPublishBlogCloudRequest request);
        
        // CODEGEN: Generating message contract since element name UnPublishBlogwithIDCloudResult from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UnPublishBlogwithIDCloud", ReplyAction="*")]
        mydentist.UnPublishBlogwithIDCloudResponse UnPublishBlogwithIDCloud(mydentist.UnPublishBlogwithIDCloudRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UnPublishBlogwithIDCloud", ReplyAction="*")]
        System.Threading.Tasks.Task<mydentist.UnPublishBlogwithIDCloudResponse> UnPublishBlogwithIDCloudAsync(mydentist.UnPublishBlogwithIDCloudRequest request);
        
        // CODEGEN: Generating message contract since element name practiceId from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/DeletePractice", ReplyAction="*")]
        mydentist.DeletePracticeResponse DeletePractice(mydentist.DeletePracticeRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/DeletePractice", ReplyAction="*")]
        System.Threading.Tasks.Task<mydentist.DeletePracticeResponse> DeletePracticeAsync(mydentist.DeletePracticeRequest request);
        
        // CODEGEN: Generating message contract since element name practiceId from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Aboutuscloud", ReplyAction="*")]
        mydentist.AboutuscloudResponse Aboutuscloud(mydentist.AboutuscloudRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/Aboutuscloud", ReplyAction="*")]
        System.Threading.Tasks.Task<mydentist.AboutuscloudResponse> AboutuscloudAsync(mydentist.AboutuscloudRequest request);
        
        // CODEGEN: Generating message contract since element name webpractice from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/AddPracticeCloud", ReplyAction="*")]
        mydentist.AddPracticeCloudResponse AddPracticeCloud(mydentist.AddPracticeCloudRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/AddPracticeCloud", ReplyAction="*")]
        System.Threading.Tasks.Task<mydentist.AddPracticeCloudResponse> AddPracticeCloudAsync(mydentist.AddPracticeCloudRequest request);
        
        // CODEGEN: Generating message contract since element name imageid from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UploadAlbumImageCloud", ReplyAction="*")]
        mydentist.UploadAlbumImageCloudResponse UploadAlbumImageCloud(mydentist.UploadAlbumImageCloudRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UploadAlbumImageCloud", ReplyAction="*")]
        System.Threading.Tasks.Task<mydentist.UploadAlbumImageCloudResponse> UploadAlbumImageCloudAsync(mydentist.UploadAlbumImageCloudRequest request);
        
        // CODEGEN: Generating message contract since element name practiceId from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/DeleteClinicianImageRelationCloud", ReplyAction="*")]
        mydentist.DeleteClinicianImageRelationCloudResponse DeleteClinicianImageRelationCloud(mydentist.DeleteClinicianImageRelationCloudRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/DeleteClinicianImageRelationCloud", ReplyAction="*")]
        System.Threading.Tasks.Task<mydentist.DeleteClinicianImageRelationCloudResponse> DeleteClinicianImageRelationCloudAsync(mydentist.DeleteClinicianImageRelationCloudRequest request);
        
        // CODEGEN: Generating message contract since element name practiceId from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UpdateCliniciansCloud", ReplyAction="*")]
        mydentist.UpdateCliniciansCloudResponse UpdateCliniciansCloud(mydentist.UpdateCliniciansCloudRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UpdateCliniciansCloud", ReplyAction="*")]
        System.Threading.Tasks.Task<mydentist.UpdateCliniciansCloudResponse> UpdateCliniciansCloudAsync(mydentist.UpdateCliniciansCloudRequest request);
        
        // CODEGEN: Generating message contract since element name practiceId from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/SetAcceptingNewPatientsCloud", ReplyAction="*")]
        mydentist.SetAcceptingNewPatientsCloudResponse SetAcceptingNewPatientsCloud(mydentist.SetAcceptingNewPatientsCloudRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/SetAcceptingNewPatientsCloud", ReplyAction="*")]
        System.Threading.Tasks.Task<mydentist.SetAcceptingNewPatientsCloudResponse> SetAcceptingNewPatientsCloudAsync(mydentist.SetAcceptingNewPatientsCloudRequest request);
        
        // CODEGEN: Generating message contract since element name practiceId from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/SetOnlineBookingEnabledCloud", ReplyAction="*")]
        mydentist.SetOnlineBookingEnabledCloudResponse SetOnlineBookingEnabledCloud(mydentist.SetOnlineBookingEnabledCloudRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/SetOnlineBookingEnabledCloud", ReplyAction="*")]
        System.Threading.Tasks.Task<mydentist.SetOnlineBookingEnabledCloudResponse> SetOnlineBookingEnabledCloudAsync(mydentist.SetOnlineBookingEnabledCloudRequest request);
        
        // CODEGEN: Generating message contract since element name blog from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/PublishBlogCloud", ReplyAction="*")]
        mydentist.PublishBlogCloudResponse PublishBlogCloud(mydentist.PublishBlogCloudRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/PublishBlogCloud", ReplyAction="*")]
        System.Threading.Tasks.Task<mydentist.PublishBlogCloudResponse> PublishBlogCloudAsync(mydentist.PublishBlogCloudRequest request);
        
        // CODEGEN: Generating message contract since element name practiceId from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/SetDirectAccessEnabledCloud", ReplyAction="*")]
        mydentist.SetDirectAccessEnabledCloudResponse SetDirectAccessEnabledCloud(mydentist.SetDirectAccessEnabledCloudRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/SetDirectAccessEnabledCloud", ReplyAction="*")]
        System.Threading.Tasks.Task<mydentist.SetDirectAccessEnabledCloudResponse> SetDirectAccessEnabledCloudAsync(mydentist.SetDirectAccessEnabledCloudRequest request);
        
        // CODEGEN: Generating message contract since element name practiceId from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UpdateTreatmentCostCloud", ReplyAction="*")]
        mydentist.UpdateTreatmentCostCloudResponse UpdateTreatmentCostCloud(mydentist.UpdateTreatmentCostCloudRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UpdateTreatmentCostCloud", ReplyAction="*")]
        System.Threading.Tasks.Task<mydentist.UpdateTreatmentCostCloudResponse> UpdateTreatmentCostCloudAsync(mydentist.UpdateTreatmentCostCloudRequest request);
        
        // CODEGEN: Generating message contract since element name practiceId from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UpdateFreeAssessmentCloud", ReplyAction="*")]
        mydentist.UpdateFreeAssessmentCloudResponse UpdateFreeAssessmentCloud(mydentist.UpdateFreeAssessmentCloudRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UpdateFreeAssessmentCloud", ReplyAction="*")]
        System.Threading.Tasks.Task<mydentist.UpdateFreeAssessmentCloudResponse> UpdateFreeAssessmentCloudAsync(mydentist.UpdateFreeAssessmentCloudRequest request);
        
        // CODEGEN: Generating message contract since element name webpractice from namespace http://tempuri.org/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UpdateTitleCloud", ReplyAction="*")]
        mydentist.UpdateTitleCloudResponse UpdateTitleCloud(mydentist.UpdateTitleCloudRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/UpdateTitleCloud", ReplyAction="*")]
        System.Threading.Tasks.Task<mydentist.UpdateTitleCloudResponse> UpdateTitleCloudAsync(mydentist.UpdateTitleCloudRequest request);
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class DeleteClinicianRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="DeleteClinician", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.DeleteClinicianRequestBody Body;
        
        public DeleteClinicianRequest()
        {
        }
        
        public DeleteClinicianRequest(mydentist.DeleteClinicianRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class DeleteClinicianRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string practiceId;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string gdcNo;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string status;
        
        public DeleteClinicianRequestBody()
        {
        }
        
        public DeleteClinicianRequestBody(string practiceId, string gdcNo, string status)
        {
            this.practiceId = practiceId;
            this.gdcNo = gdcNo;
            this.status = status;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class DeleteClinicianResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="DeleteClinicianResponse", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.DeleteClinicianResponseBody Body;
        
        public DeleteClinicianResponse()
        {
        }
        
        public DeleteClinicianResponse(mydentist.DeleteClinicianResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class DeleteClinicianResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public mydentist.Result DeleteClinicianResult;
        
        public DeleteClinicianResponseBody()
        {
        }
        
        public DeleteClinicianResponseBody(mydentist.Result DeleteClinicianResult)
        {
            this.DeleteClinicianResult = DeleteClinicianResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UploadBlogImageCloudRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UploadBlogImageCloud", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.UploadBlogImageCloudRequestBody Body;
        
        public UploadBlogImageCloudRequest()
        {
        }
        
        public UploadBlogImageCloudRequest(mydentist.UploadBlogImageCloudRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UploadBlogImageCloudRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string imageid;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string filename;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public byte[] imageStream;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public string imageGallery;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public string fileextension;
        
        public UploadBlogImageCloudRequestBody()
        {
        }
        
        public UploadBlogImageCloudRequestBody(string imageid, string filename, byte[] imageStream, string imageGallery, string fileextension)
        {
            this.imageid = imageid;
            this.filename = filename;
            this.imageStream = imageStream;
            this.imageGallery = imageGallery;
            this.fileextension = fileextension;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UploadBlogImageCloudResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UploadBlogImageCloudResponse", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.UploadBlogImageCloudResponseBody Body;
        
        public UploadBlogImageCloudResponse()
        {
        }
        
        public UploadBlogImageCloudResponse(mydentist.UploadBlogImageCloudResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UploadBlogImageCloudResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public mydentist.Result UploadBlogImageCloudResult;
        
        public UploadBlogImageCloudResponseBody()
        {
        }
        
        public UploadBlogImageCloudResponseBody(mydentist.Result UploadBlogImageCloudResult)
        {
            this.UploadBlogImageCloudResult = UploadBlogImageCloudResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class DeleteCasestudyRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="DeleteCasestudy", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.DeleteCasestudyRequestBody Body;
        
        public DeleteCasestudyRequest()
        {
        }
        
        public DeleteCasestudyRequest(mydentist.DeleteCasestudyRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class DeleteCasestudyRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string casestudyId;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string practiceId;
        
        public DeleteCasestudyRequestBody()
        {
        }
        
        public DeleteCasestudyRequestBody(string casestudyId, string practiceId)
        {
            this.casestudyId = casestudyId;
            this.practiceId = practiceId;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class DeleteCasestudyResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="DeleteCasestudyResponse", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.DeleteCasestudyResponseBody Body;
        
        public DeleteCasestudyResponse()
        {
        }
        
        public DeleteCasestudyResponse(mydentist.DeleteCasestudyResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class DeleteCasestudyResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public mydentist.Result DeleteCasestudyResult;
        
        public DeleteCasestudyResponseBody()
        {
        }
        
        public DeleteCasestudyResponseBody(mydentist.Result DeleteCasestudyResult)
        {
            this.DeleteCasestudyResult = DeleteCasestudyResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UpdateOpeninghrsCloudRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UpdateOpeninghrsCloud", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.UpdateOpeninghrsCloudRequestBody Body;
        
        public UpdateOpeninghrsCloudRequest()
        {
        }
        
        public UpdateOpeninghrsCloudRequest(mydentist.UpdateOpeninghrsCloudRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UpdateOpeninghrsCloudRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string practiceId;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public System.Collections.Generic.List<mydentist.WebOpeningHour> webOpeningHours;
        
        public UpdateOpeninghrsCloudRequestBody()
        {
        }
        
        public UpdateOpeninghrsCloudRequestBody(string practiceId, System.Collections.Generic.List<mydentist.WebOpeningHour> webOpeningHours)
        {
            this.practiceId = practiceId;
            this.webOpeningHours = webOpeningHours;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UpdateOpeninghrsCloudResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UpdateOpeninghrsCloudResponse", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.UpdateOpeninghrsCloudResponseBody Body;
        
        public UpdateOpeninghrsCloudResponse()
        {
        }
        
        public UpdateOpeninghrsCloudResponse(mydentist.UpdateOpeninghrsCloudResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UpdateOpeninghrsCloudResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public mydentist.Result UpdateOpeninghrsCloudResult;
        
        public UpdateOpeninghrsCloudResponseBody()
        {
        }
        
        public UpdateOpeninghrsCloudResponseBody(mydentist.Result UpdateOpeninghrsCloudResult)
        {
            this.UpdateOpeninghrsCloudResult = UpdateOpeninghrsCloudResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UpdatePracticeCCMCloudRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UpdatePracticeCCMCloud", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.UpdatePracticeCCMCloudRequestBody Body;
        
        public UpdatePracticeCCMCloudRequest()
        {
        }
        
        public UpdatePracticeCCMCloudRequest(mydentist.UpdatePracticeCCMCloudRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UpdatePracticeCCMCloudRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string practiceId;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public System.Collections.Generic.List<mydentist.WebLimitedCompanyFooter> webLimitedCompanyFooters;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public System.Collections.Generic.List<mydentist.WebPartnershipFooter> webPartnershipFooters;
        
        public UpdatePracticeCCMCloudRequestBody()
        {
        }
        
        public UpdatePracticeCCMCloudRequestBody(string practiceId, System.Collections.Generic.List<mydentist.WebLimitedCompanyFooter> webLimitedCompanyFooters, System.Collections.Generic.List<mydentist.WebPartnershipFooter> webPartnershipFooters)
        {
            this.practiceId = practiceId;
            this.webLimitedCompanyFooters = webLimitedCompanyFooters;
            this.webPartnershipFooters = webPartnershipFooters;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UpdatePracticeCCMCloudResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UpdatePracticeCCMCloudResponse", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.UpdatePracticeCCMCloudResponseBody Body;
        
        public UpdatePracticeCCMCloudResponse()
        {
        }
        
        public UpdatePracticeCCMCloudResponse(mydentist.UpdatePracticeCCMCloudResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UpdatePracticeCCMCloudResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public mydentist.Result UpdatePracticeCCMCloudResult;
        
        public UpdatePracticeCCMCloudResponseBody()
        {
        }
        
        public UpdatePracticeCCMCloudResponseBody(mydentist.Result UpdatePracticeCCMCloudResult)
        {
            this.UpdatePracticeCCMCloudResult = UpdatePracticeCCMCloudResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UpdateReviewsCloudRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UpdateReviewsCloud", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.UpdateReviewsCloudRequestBody Body;
        
        public UpdateReviewsCloudRequest()
        {
        }
        
        public UpdateReviewsCloudRequest(mydentist.UpdateReviewsCloudRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UpdateReviewsCloudRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string practiceId;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public System.Collections.Generic.List<mydentist.WebReview> webReviews;
        
        public UpdateReviewsCloudRequestBody()
        {
        }
        
        public UpdateReviewsCloudRequestBody(string practiceId, System.Collections.Generic.List<mydentist.WebReview> webReviews)
        {
            this.practiceId = practiceId;
            this.webReviews = webReviews;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UpdateReviewsCloudResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UpdateReviewsCloudResponse", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.UpdateReviewsCloudResponseBody Body;
        
        public UpdateReviewsCloudResponse()
        {
        }
        
        public UpdateReviewsCloudResponse(mydentist.UpdateReviewsCloudResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UpdateReviewsCloudResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public mydentist.Result UpdateReviewsCloudResult;
        
        public UpdateReviewsCloudResponseBody()
        {
        }
        
        public UpdateReviewsCloudResponseBody(mydentist.Result UpdateReviewsCloudResult)
        {
            this.UpdateReviewsCloudResult = UpdateReviewsCloudResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class CreateCasetudyCloudRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="CreateCasetudyCloud", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.CreateCasetudyCloudRequestBody Body;
        
        public CreateCasetudyCloudRequest()
        {
        }
        
        public CreateCasetudyCloudRequest(mydentist.CreateCasetudyCloudRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class CreateCasetudyCloudRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public mydentist.WebCaseStudy webCase;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string practiceId;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public System.Collections.Generic.List<mydentist.WebCaseStudyImage> webCaseStudyImages;
        
        public CreateCasetudyCloudRequestBody()
        {
        }
        
        public CreateCasetudyCloudRequestBody(mydentist.WebCaseStudy webCase, string practiceId, System.Collections.Generic.List<mydentist.WebCaseStudyImage> webCaseStudyImages)
        {
            this.webCase = webCase;
            this.practiceId = practiceId;
            this.webCaseStudyImages = webCaseStudyImages;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class CreateCasetudyCloudResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="CreateCasetudyCloudResponse", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.CreateCasetudyCloudResponseBody Body;
        
        public CreateCasetudyCloudResponse()
        {
        }
        
        public CreateCasetudyCloudResponse(mydentist.CreateCasetudyCloudResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class CreateCasetudyCloudResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public mydentist.Result CreateCasetudyCloudResult;
        
        public CreateCasetudyCloudResponseBody()
        {
        }
        
        public CreateCasetudyCloudResponseBody(mydentist.Result CreateCasetudyCloudResult)
        {
            this.CreateCasetudyCloudResult = CreateCasetudyCloudResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UpdateFacilitiesCloudRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UpdateFacilitiesCloud", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.UpdateFacilitiesCloudRequestBody Body;
        
        public UpdateFacilitiesCloudRequest()
        {
        }
        
        public UpdateFacilitiesCloudRequest(mydentist.UpdateFacilitiesCloudRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UpdateFacilitiesCloudRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string practiceId;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public mydentist.WebFacility webFacility;
        
        public UpdateFacilitiesCloudRequestBody()
        {
        }
        
        public UpdateFacilitiesCloudRequestBody(string practiceId, mydentist.WebFacility webFacility)
        {
            this.practiceId = practiceId;
            this.webFacility = webFacility;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UpdateFacilitiesCloudResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UpdateFacilitiesCloudResponse", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.UpdateFacilitiesCloudResponseBody Body;
        
        public UpdateFacilitiesCloudResponse()
        {
        }
        
        public UpdateFacilitiesCloudResponse(mydentist.UpdateFacilitiesCloudResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UpdateFacilitiesCloudResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public mydentist.Result UpdateFacilitiesCloudResult;
        
        public UpdateFacilitiesCloudResponseBody()
        {
        }
        
        public UpdateFacilitiesCloudResponseBody(mydentist.Result UpdateFacilitiesCloudResult)
        {
            this.UpdateFacilitiesCloudResult = UpdateFacilitiesCloudResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UpdateClinicianCloudRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UpdateClinicianCloud", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.UpdateClinicianCloudRequestBody Body;
        
        public UpdateClinicianCloudRequest()
        {
        }
        
        public UpdateClinicianCloudRequest(mydentist.UpdateClinicianCloudRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UpdateClinicianCloudRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public mydentist.WebClinician webClinician;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public mydentist.WebClinicianImage webClinicianImage;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public string status;
        
        public UpdateClinicianCloudRequestBody()
        {
        }
        
        public UpdateClinicianCloudRequestBody(mydentist.WebClinician webClinician, mydentist.WebClinicianImage webClinicianImage, string status)
        {
            this.webClinician = webClinician;
            this.webClinicianImage = webClinicianImage;
            this.status = status;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UpdateClinicianCloudResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UpdateClinicianCloudResponse", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.UpdateClinicianCloudResponseBody Body;
        
        public UpdateClinicianCloudResponse()
        {
        }
        
        public UpdateClinicianCloudResponse(mydentist.UpdateClinicianCloudResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UpdateClinicianCloudResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public mydentist.Result UpdateClinicianCloudResult;
        
        public UpdateClinicianCloudResponseBody()
        {
        }
        
        public UpdateClinicianCloudResponseBody(mydentist.Result UpdateClinicianCloudResult)
        {
            this.UpdateClinicianCloudResult = UpdateClinicianCloudResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UnPublishBlogCloudRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UnPublishBlogCloud", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.UnPublishBlogCloudRequestBody Body;
        
        public UnPublishBlogCloudRequest()
        {
        }
        
        public UnPublishBlogCloudRequest(mydentist.UnPublishBlogCloudRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UnPublishBlogCloudRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public mydentist.BlogModel blog;
        
        public UnPublishBlogCloudRequestBody()
        {
        }
        
        public UnPublishBlogCloudRequestBody(mydentist.BlogModel blog)
        {
            this.blog = blog;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UnPublishBlogCloudResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UnPublishBlogCloudResponse", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.UnPublishBlogCloudResponseBody Body;
        
        public UnPublishBlogCloudResponse()
        {
        }
        
        public UnPublishBlogCloudResponse(mydentist.UnPublishBlogCloudResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UnPublishBlogCloudResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public mydentist.Result UnPublishBlogCloudResult;
        
        public UnPublishBlogCloudResponseBody()
        {
        }
        
        public UnPublishBlogCloudResponseBody(mydentist.Result UnPublishBlogCloudResult)
        {
            this.UnPublishBlogCloudResult = UnPublishBlogCloudResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UnPublishBlogwithIDCloudRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UnPublishBlogwithIDCloud", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.UnPublishBlogwithIDCloudRequestBody Body;
        
        public UnPublishBlogwithIDCloudRequest()
        {
        }
        
        public UnPublishBlogwithIDCloudRequest(mydentist.UnPublishBlogwithIDCloudRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UnPublishBlogwithIDCloudRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=0)]
        public int blogId;
        
        public UnPublishBlogwithIDCloudRequestBody()
        {
        }
        
        public UnPublishBlogwithIDCloudRequestBody(int blogId)
        {
            this.blogId = blogId;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UnPublishBlogwithIDCloudResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UnPublishBlogwithIDCloudResponse", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.UnPublishBlogwithIDCloudResponseBody Body;
        
        public UnPublishBlogwithIDCloudResponse()
        {
        }
        
        public UnPublishBlogwithIDCloudResponse(mydentist.UnPublishBlogwithIDCloudResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UnPublishBlogwithIDCloudResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public mydentist.Result UnPublishBlogwithIDCloudResult;
        
        public UnPublishBlogwithIDCloudResponseBody()
        {
        }
        
        public UnPublishBlogwithIDCloudResponseBody(mydentist.Result UnPublishBlogwithIDCloudResult)
        {
            this.UnPublishBlogwithIDCloudResult = UnPublishBlogwithIDCloudResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class DeletePracticeRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="DeletePractice", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.DeletePracticeRequestBody Body;
        
        public DeletePracticeRequest()
        {
        }
        
        public DeletePracticeRequest(mydentist.DeletePracticeRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class DeletePracticeRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string practiceId;
        
        public DeletePracticeRequestBody()
        {
        }
        
        public DeletePracticeRequestBody(string practiceId)
        {
            this.practiceId = practiceId;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class DeletePracticeResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="DeletePracticeResponse", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.DeletePracticeResponseBody Body;
        
        public DeletePracticeResponse()
        {
        }
        
        public DeletePracticeResponse(mydentist.DeletePracticeResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class DeletePracticeResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public mydentist.Result DeletePracticeResult;
        
        public DeletePracticeResponseBody()
        {
        }
        
        public DeletePracticeResponseBody(mydentist.Result DeletePracticeResult)
        {
            this.DeletePracticeResult = DeletePracticeResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class AboutuscloudRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="Aboutuscloud", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.AboutuscloudRequestBody Body;
        
        public AboutuscloudRequest()
        {
        }
        
        public AboutuscloudRequest(mydentist.AboutuscloudRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class AboutuscloudRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string practiceId;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string aboutus;
        
        public AboutuscloudRequestBody()
        {
        }
        
        public AboutuscloudRequestBody(string practiceId, string aboutus)
        {
            this.practiceId = practiceId;
            this.aboutus = aboutus;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class AboutuscloudResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="AboutuscloudResponse", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.AboutuscloudResponseBody Body;
        
        public AboutuscloudResponse()
        {
        }
        
        public AboutuscloudResponse(mydentist.AboutuscloudResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class AboutuscloudResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public mydentist.Result AboutuscloudResult;
        
        public AboutuscloudResponseBody()
        {
        }
        
        public AboutuscloudResponseBody(mydentist.Result AboutuscloudResult)
        {
            this.AboutuscloudResult = AboutuscloudResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class AddPracticeCloudRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="AddPracticeCloud", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.AddPracticeCloudRequestBody Body;
        
        public AddPracticeCloudRequest()
        {
        }
        
        public AddPracticeCloudRequest(mydentist.AddPracticeCloudRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class AddPracticeCloudRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public mydentist.WebPractice webpractice;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public System.Collections.Generic.List<mydentist.WebOpeningHour> webopeningHours;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public mydentist.WebFacility webFacility;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public System.Collections.Generic.List<mydentist.WebFreeAssessmentEvent> webfreeAssessmentEvents;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public System.Collections.Generic.List<mydentist.WebClinician> webClinicians;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=5)]
        public System.Collections.Generic.List<mydentist.WebTreatment> webtreatments;
        
        public AddPracticeCloudRequestBody()
        {
        }
        
        public AddPracticeCloudRequestBody(mydentist.WebPractice webpractice, System.Collections.Generic.List<mydentist.WebOpeningHour> webopeningHours, mydentist.WebFacility webFacility, System.Collections.Generic.List<mydentist.WebFreeAssessmentEvent> webfreeAssessmentEvents, System.Collections.Generic.List<mydentist.WebClinician> webClinicians, System.Collections.Generic.List<mydentist.WebTreatment> webtreatments)
        {
            this.webpractice = webpractice;
            this.webopeningHours = webopeningHours;
            this.webFacility = webFacility;
            this.webfreeAssessmentEvents = webfreeAssessmentEvents;
            this.webClinicians = webClinicians;
            this.webtreatments = webtreatments;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class AddPracticeCloudResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="AddPracticeCloudResponse", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.AddPracticeCloudResponseBody Body;
        
        public AddPracticeCloudResponse()
        {
        }
        
        public AddPracticeCloudResponse(mydentist.AddPracticeCloudResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class AddPracticeCloudResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public mydentist.Result AddPracticeCloudResult;
        
        public AddPracticeCloudResponseBody()
        {
        }
        
        public AddPracticeCloudResponseBody(mydentist.Result AddPracticeCloudResult)
        {
            this.AddPracticeCloudResult = AddPracticeCloudResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UploadAlbumImageCloudRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UploadAlbumImageCloud", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.UploadAlbumImageCloudRequestBody Body;
        
        public UploadAlbumImageCloudRequest()
        {
        }
        
        public UploadAlbumImageCloudRequest(mydentist.UploadAlbumImageCloudRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UploadAlbumImageCloudRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string imageid;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string filename;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=2)]
        public byte[] imageStream;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=3)]
        public string imageGallery;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=4)]
        public string fileextension;
        
        public UploadAlbumImageCloudRequestBody()
        {
        }
        
        public UploadAlbumImageCloudRequestBody(string imageid, string filename, byte[] imageStream, string imageGallery, string fileextension)
        {
            this.imageid = imageid;
            this.filename = filename;
            this.imageStream = imageStream;
            this.imageGallery = imageGallery;
            this.fileextension = fileextension;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UploadAlbumImageCloudResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UploadAlbumImageCloudResponse", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.UploadAlbumImageCloudResponseBody Body;
        
        public UploadAlbumImageCloudResponse()
        {
        }
        
        public UploadAlbumImageCloudResponse(mydentist.UploadAlbumImageCloudResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UploadAlbumImageCloudResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public mydentist.Result UploadAlbumImageCloudResult;
        
        public UploadAlbumImageCloudResponseBody()
        {
        }
        
        public UploadAlbumImageCloudResponseBody(mydentist.Result UploadAlbumImageCloudResult)
        {
            this.UploadAlbumImageCloudResult = UploadAlbumImageCloudResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class DeleteClinicianImageRelationCloudRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="DeleteClinicianImageRelationCloud", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.DeleteClinicianImageRelationCloudRequestBody Body;
        
        public DeleteClinicianImageRelationCloudRequest()
        {
        }
        
        public DeleteClinicianImageRelationCloudRequest(mydentist.DeleteClinicianImageRelationCloudRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class DeleteClinicianImageRelationCloudRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string practiceId;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public string gdcNo;
        
        public DeleteClinicianImageRelationCloudRequestBody()
        {
        }
        
        public DeleteClinicianImageRelationCloudRequestBody(string practiceId, string gdcNo)
        {
            this.practiceId = practiceId;
            this.gdcNo = gdcNo;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class DeleteClinicianImageRelationCloudResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="DeleteClinicianImageRelationCloudResponse", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.DeleteClinicianImageRelationCloudResponseBody Body;
        
        public DeleteClinicianImageRelationCloudResponse()
        {
        }
        
        public DeleteClinicianImageRelationCloudResponse(mydentist.DeleteClinicianImageRelationCloudResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class DeleteClinicianImageRelationCloudResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public mydentist.Result DeleteClinicianImageRelationCloudResult;
        
        public DeleteClinicianImageRelationCloudResponseBody()
        {
        }
        
        public DeleteClinicianImageRelationCloudResponseBody(mydentist.Result DeleteClinicianImageRelationCloudResult)
        {
            this.DeleteClinicianImageRelationCloudResult = DeleteClinicianImageRelationCloudResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UpdateCliniciansCloudRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UpdateCliniciansCloud", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.UpdateCliniciansCloudRequestBody Body;
        
        public UpdateCliniciansCloudRequest()
        {
        }
        
        public UpdateCliniciansCloudRequest(mydentist.UpdateCliniciansCloudRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UpdateCliniciansCloudRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string practiceId;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public System.Collections.Generic.List<mydentist.WebClinician> webclinicians;
        
        public UpdateCliniciansCloudRequestBody()
        {
        }
        
        public UpdateCliniciansCloudRequestBody(string practiceId, System.Collections.Generic.List<mydentist.WebClinician> webclinicians)
        {
            this.practiceId = practiceId;
            this.webclinicians = webclinicians;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UpdateCliniciansCloudResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UpdateCliniciansCloudResponse", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.UpdateCliniciansCloudResponseBody Body;
        
        public UpdateCliniciansCloudResponse()
        {
        }
        
        public UpdateCliniciansCloudResponse(mydentist.UpdateCliniciansCloudResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UpdateCliniciansCloudResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public mydentist.Result UpdateCliniciansCloudResult;
        
        public UpdateCliniciansCloudResponseBody()
        {
        }
        
        public UpdateCliniciansCloudResponseBody(mydentist.Result UpdateCliniciansCloudResult)
        {
            this.UpdateCliniciansCloudResult = UpdateCliniciansCloudResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class SetAcceptingNewPatientsCloudRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="SetAcceptingNewPatientsCloud", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.SetAcceptingNewPatientsCloudRequestBody Body;
        
        public SetAcceptingNewPatientsCloudRequest()
        {
        }
        
        public SetAcceptingNewPatientsCloudRequest(mydentist.SetAcceptingNewPatientsCloudRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class SetAcceptingNewPatientsCloudRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string practiceId;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=1)]
        public bool isAcceptingNewPatients;
        
        public SetAcceptingNewPatientsCloudRequestBody()
        {
        }
        
        public SetAcceptingNewPatientsCloudRequestBody(string practiceId, bool isAcceptingNewPatients)
        {
            this.practiceId = practiceId;
            this.isAcceptingNewPatients = isAcceptingNewPatients;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class SetAcceptingNewPatientsCloudResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="SetAcceptingNewPatientsCloudResponse", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.SetAcceptingNewPatientsCloudResponseBody Body;
        
        public SetAcceptingNewPatientsCloudResponse()
        {
        }
        
        public SetAcceptingNewPatientsCloudResponse(mydentist.SetAcceptingNewPatientsCloudResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class SetAcceptingNewPatientsCloudResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public mydentist.Result SetAcceptingNewPatientsCloudResult;
        
        public SetAcceptingNewPatientsCloudResponseBody()
        {
        }
        
        public SetAcceptingNewPatientsCloudResponseBody(mydentist.Result SetAcceptingNewPatientsCloudResult)
        {
            this.SetAcceptingNewPatientsCloudResult = SetAcceptingNewPatientsCloudResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class SetOnlineBookingEnabledCloudRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="SetOnlineBookingEnabledCloud", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.SetOnlineBookingEnabledCloudRequestBody Body;
        
        public SetOnlineBookingEnabledCloudRequest()
        {
        }
        
        public SetOnlineBookingEnabledCloudRequest(mydentist.SetOnlineBookingEnabledCloudRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class SetOnlineBookingEnabledCloudRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string practiceId;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=1)]
        public bool isOnlineBookingEnabled;
        
        public SetOnlineBookingEnabledCloudRequestBody()
        {
        }
        
        public SetOnlineBookingEnabledCloudRequestBody(string practiceId, bool isOnlineBookingEnabled)
        {
            this.practiceId = practiceId;
            this.isOnlineBookingEnabled = isOnlineBookingEnabled;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class SetOnlineBookingEnabledCloudResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="SetOnlineBookingEnabledCloudResponse", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.SetOnlineBookingEnabledCloudResponseBody Body;
        
        public SetOnlineBookingEnabledCloudResponse()
        {
        }
        
        public SetOnlineBookingEnabledCloudResponse(mydentist.SetOnlineBookingEnabledCloudResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class SetOnlineBookingEnabledCloudResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public mydentist.Result SetOnlineBookingEnabledCloudResult;
        
        public SetOnlineBookingEnabledCloudResponseBody()
        {
        }
        
        public SetOnlineBookingEnabledCloudResponseBody(mydentist.Result SetOnlineBookingEnabledCloudResult)
        {
            this.SetOnlineBookingEnabledCloudResult = SetOnlineBookingEnabledCloudResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class PublishBlogCloudRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="PublishBlogCloud", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.PublishBlogCloudRequestBody Body;
        
        public PublishBlogCloudRequest()
        {
        }
        
        public PublishBlogCloudRequest(mydentist.PublishBlogCloudRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class PublishBlogCloudRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public mydentist.BlogModel blog;
        
        public PublishBlogCloudRequestBody()
        {
        }
        
        public PublishBlogCloudRequestBody(mydentist.BlogModel blog)
        {
            this.blog = blog;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class PublishBlogCloudResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="PublishBlogCloudResponse", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.PublishBlogCloudResponseBody Body;
        
        public PublishBlogCloudResponse()
        {
        }
        
        public PublishBlogCloudResponse(mydentist.PublishBlogCloudResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class PublishBlogCloudResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public mydentist.Result PublishBlogCloudResult;
        
        public PublishBlogCloudResponseBody()
        {
        }
        
        public PublishBlogCloudResponseBody(mydentist.Result PublishBlogCloudResult)
        {
            this.PublishBlogCloudResult = PublishBlogCloudResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class SetDirectAccessEnabledCloudRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="SetDirectAccessEnabledCloud", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.SetDirectAccessEnabledCloudRequestBody Body;
        
        public SetDirectAccessEnabledCloudRequest()
        {
        }
        
        public SetDirectAccessEnabledCloudRequest(mydentist.SetDirectAccessEnabledCloudRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class SetDirectAccessEnabledCloudRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string practiceId;
        
        [System.Runtime.Serialization.DataMemberAttribute(Order=1)]
        public bool isDirectAccessEnabled;
        
        public SetDirectAccessEnabledCloudRequestBody()
        {
        }
        
        public SetDirectAccessEnabledCloudRequestBody(string practiceId, bool isDirectAccessEnabled)
        {
            this.practiceId = practiceId;
            this.isDirectAccessEnabled = isDirectAccessEnabled;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class SetDirectAccessEnabledCloudResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="SetDirectAccessEnabledCloudResponse", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.SetDirectAccessEnabledCloudResponseBody Body;
        
        public SetDirectAccessEnabledCloudResponse()
        {
        }
        
        public SetDirectAccessEnabledCloudResponse(mydentist.SetDirectAccessEnabledCloudResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class SetDirectAccessEnabledCloudResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public mydentist.Result SetDirectAccessEnabledCloudResult;
        
        public SetDirectAccessEnabledCloudResponseBody()
        {
        }
        
        public SetDirectAccessEnabledCloudResponseBody(mydentist.Result SetDirectAccessEnabledCloudResult)
        {
            this.SetDirectAccessEnabledCloudResult = SetDirectAccessEnabledCloudResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UpdateTreatmentCostCloudRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UpdateTreatmentCostCloud", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.UpdateTreatmentCostCloudRequestBody Body;
        
        public UpdateTreatmentCostCloudRequest()
        {
        }
        
        public UpdateTreatmentCostCloudRequest(mydentist.UpdateTreatmentCostCloudRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UpdateTreatmentCostCloudRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string practiceId;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public System.Collections.Generic.List<mydentist.WebTreatment> webtreatments;
        
        public UpdateTreatmentCostCloudRequestBody()
        {
        }
        
        public UpdateTreatmentCostCloudRequestBody(string practiceId, System.Collections.Generic.List<mydentist.WebTreatment> webtreatments)
        {
            this.practiceId = practiceId;
            this.webtreatments = webtreatments;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UpdateTreatmentCostCloudResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UpdateTreatmentCostCloudResponse", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.UpdateTreatmentCostCloudResponseBody Body;
        
        public UpdateTreatmentCostCloudResponse()
        {
        }
        
        public UpdateTreatmentCostCloudResponse(mydentist.UpdateTreatmentCostCloudResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UpdateTreatmentCostCloudResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public mydentist.Result UpdateTreatmentCostCloudResult;
        
        public UpdateTreatmentCostCloudResponseBody()
        {
        }
        
        public UpdateTreatmentCostCloudResponseBody(mydentist.Result UpdateTreatmentCostCloudResult)
        {
            this.UpdateTreatmentCostCloudResult = UpdateTreatmentCostCloudResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UpdateFreeAssessmentCloudRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UpdateFreeAssessmentCloud", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.UpdateFreeAssessmentCloudRequestBody Body;
        
        public UpdateFreeAssessmentCloudRequest()
        {
        }
        
        public UpdateFreeAssessmentCloudRequest(mydentist.UpdateFreeAssessmentCloudRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UpdateFreeAssessmentCloudRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string practiceId;
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=1)]
        public System.Collections.Generic.List<mydentist.WebFreeAssessmentEvent> WebfreeAssessments;
        
        public UpdateFreeAssessmentCloudRequestBody()
        {
        }
        
        public UpdateFreeAssessmentCloudRequestBody(string practiceId, System.Collections.Generic.List<mydentist.WebFreeAssessmentEvent> WebfreeAssessments)
        {
            this.practiceId = practiceId;
            this.WebfreeAssessments = WebfreeAssessments;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UpdateFreeAssessmentCloudResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UpdateFreeAssessmentCloudResponse", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.UpdateFreeAssessmentCloudResponseBody Body;
        
        public UpdateFreeAssessmentCloudResponse()
        {
        }
        
        public UpdateFreeAssessmentCloudResponse(mydentist.UpdateFreeAssessmentCloudResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UpdateFreeAssessmentCloudResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public mydentist.Result UpdateFreeAssessmentCloudResult;
        
        public UpdateFreeAssessmentCloudResponseBody()
        {
        }
        
        public UpdateFreeAssessmentCloudResponseBody(mydentist.Result UpdateFreeAssessmentCloudResult)
        {
            this.UpdateFreeAssessmentCloudResult = UpdateFreeAssessmentCloudResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UpdateTitleCloudRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UpdateTitleCloud", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.UpdateTitleCloudRequestBody Body;
        
        public UpdateTitleCloudRequest()
        {
        }
        
        public UpdateTitleCloudRequest(mydentist.UpdateTitleCloudRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UpdateTitleCloudRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public mydentist.WebPractice webpractice;
        
        public UpdateTitleCloudRequestBody()
        {
        }
        
        public UpdateTitleCloudRequestBody(mydentist.WebPractice webpractice)
        {
            this.webpractice = webpractice;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class UpdateTitleCloudResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="UpdateTitleCloudResponse", Namespace="http://tempuri.org/", Order=0)]
        public mydentist.UpdateTitleCloudResponseBody Body;
        
        public UpdateTitleCloudResponse()
        {
        }
        
        public UpdateTitleCloudResponse(mydentist.UpdateTitleCloudResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://tempuri.org/")]
    public partial class UpdateTitleCloudResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public mydentist.Result UpdateTitleCloudResult;
        
        public UpdateTitleCloudResponseBody()
        {
        }
        
        public UpdateTitleCloudResponseBody(mydentist.Result UpdateTitleCloudResult)
        {
            this.UpdateTitleCloudResult = UpdateTitleCloudResult;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    public interface mydentistcloudImportSoapChannel : mydentist.mydentistcloudImportSoap, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    public partial class mydentistcloudImportSoapClient : System.ServiceModel.ClientBase<mydentist.mydentistcloudImportSoap>, mydentist.mydentistcloudImportSoap
    {
        
        /// <summary>
        /// Implement this partial method to configure the service endpoint.
        /// </summary>
        /// <param name="serviceEndpoint">The endpoint to configure</param>
        /// <param name="clientCredentials">The client credentials</param>
        static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);
        
        public mydentistcloudImportSoapClient(EndpointConfiguration endpointConfiguration) : 
                base(mydentistcloudImportSoapClient.GetBindingForEndpoint(endpointConfiguration), mydentistcloudImportSoapClient.GetEndpointAddress(endpointConfiguration))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public mydentistcloudImportSoapClient(EndpointConfiguration endpointConfiguration, string remoteAddress) : 
                base(mydentistcloudImportSoapClient.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public mydentistcloudImportSoapClient(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(mydentistcloudImportSoapClient.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public mydentistcloudImportSoapClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        mydentist.DeleteClinicianResponse mydentist.mydentistcloudImportSoap.DeleteClinician(mydentist.DeleteClinicianRequest request)
        {
            return base.Channel.DeleteClinician(request);
        }
        
        public mydentist.Result DeleteClinician(string practiceId, string gdcNo, string status)
        {
            mydentist.DeleteClinicianRequest inValue = new mydentist.DeleteClinicianRequest();
            inValue.Body = new mydentist.DeleteClinicianRequestBody();
            inValue.Body.practiceId = practiceId;
            inValue.Body.gdcNo = gdcNo;
            inValue.Body.status = status;
            mydentist.DeleteClinicianResponse retVal = ((mydentist.mydentistcloudImportSoap)(this)).DeleteClinician(inValue);
            return retVal.Body.DeleteClinicianResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<mydentist.DeleteClinicianResponse> mydentist.mydentistcloudImportSoap.DeleteClinicianAsync(mydentist.DeleteClinicianRequest request)
        {
            return base.Channel.DeleteClinicianAsync(request);
        }
        
        public System.Threading.Tasks.Task<mydentist.DeleteClinicianResponse> DeleteClinicianAsync(string practiceId, string gdcNo, string status)
        {
            mydentist.DeleteClinicianRequest inValue = new mydentist.DeleteClinicianRequest();
            inValue.Body = new mydentist.DeleteClinicianRequestBody();
            inValue.Body.practiceId = practiceId;
            inValue.Body.gdcNo = gdcNo;
            inValue.Body.status = status;
            return ((mydentist.mydentistcloudImportSoap)(this)).DeleteClinicianAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        mydentist.UploadBlogImageCloudResponse mydentist.mydentistcloudImportSoap.UploadBlogImageCloud(mydentist.UploadBlogImageCloudRequest request)
        {
            return base.Channel.UploadBlogImageCloud(request);
        }
        
        public mydentist.Result UploadBlogImageCloud(string imageid, string filename, byte[] imageStream, string imageGallery, string fileextension)
        {
            mydentist.UploadBlogImageCloudRequest inValue = new mydentist.UploadBlogImageCloudRequest();
            inValue.Body = new mydentist.UploadBlogImageCloudRequestBody();
            inValue.Body.imageid = imageid;
            inValue.Body.filename = filename;
            inValue.Body.imageStream = imageStream;
            inValue.Body.imageGallery = imageGallery;
            inValue.Body.fileextension = fileextension;
            mydentist.UploadBlogImageCloudResponse retVal = ((mydentist.mydentistcloudImportSoap)(this)).UploadBlogImageCloud(inValue);
            return retVal.Body.UploadBlogImageCloudResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<mydentist.UploadBlogImageCloudResponse> mydentist.mydentistcloudImportSoap.UploadBlogImageCloudAsync(mydentist.UploadBlogImageCloudRequest request)
        {
            return base.Channel.UploadBlogImageCloudAsync(request);
        }
        
        public System.Threading.Tasks.Task<mydentist.UploadBlogImageCloudResponse> UploadBlogImageCloudAsync(string imageid, string filename, byte[] imageStream, string imageGallery, string fileextension)
        {
            mydentist.UploadBlogImageCloudRequest inValue = new mydentist.UploadBlogImageCloudRequest();
            inValue.Body = new mydentist.UploadBlogImageCloudRequestBody();
            inValue.Body.imageid = imageid;
            inValue.Body.filename = filename;
            inValue.Body.imageStream = imageStream;
            inValue.Body.imageGallery = imageGallery;
            inValue.Body.fileextension = fileextension;
            return ((mydentist.mydentistcloudImportSoap)(this)).UploadBlogImageCloudAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        mydentist.DeleteCasestudyResponse mydentist.mydentistcloudImportSoap.DeleteCasestudy(mydentist.DeleteCasestudyRequest request)
        {
            return base.Channel.DeleteCasestudy(request);
        }
        
        public mydentist.Result DeleteCasestudy(string casestudyId, string practiceId)
        {
            mydentist.DeleteCasestudyRequest inValue = new mydentist.DeleteCasestudyRequest();
            inValue.Body = new mydentist.DeleteCasestudyRequestBody();
            inValue.Body.casestudyId = casestudyId;
            inValue.Body.practiceId = practiceId;
            mydentist.DeleteCasestudyResponse retVal = ((mydentist.mydentistcloudImportSoap)(this)).DeleteCasestudy(inValue);
            return retVal.Body.DeleteCasestudyResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<mydentist.DeleteCasestudyResponse> mydentist.mydentistcloudImportSoap.DeleteCasestudyAsync(mydentist.DeleteCasestudyRequest request)
        {
            return base.Channel.DeleteCasestudyAsync(request);
        }
        
        public System.Threading.Tasks.Task<mydentist.DeleteCasestudyResponse> DeleteCasestudyAsync(string casestudyId, string practiceId)
        {
            mydentist.DeleteCasestudyRequest inValue = new mydentist.DeleteCasestudyRequest();
            inValue.Body = new mydentist.DeleteCasestudyRequestBody();
            inValue.Body.casestudyId = casestudyId;
            inValue.Body.practiceId = practiceId;
            return ((mydentist.mydentistcloudImportSoap)(this)).DeleteCasestudyAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        mydentist.UpdateOpeninghrsCloudResponse mydentist.mydentistcloudImportSoap.UpdateOpeninghrsCloud(mydentist.UpdateOpeninghrsCloudRequest request)
        {
            return base.Channel.UpdateOpeninghrsCloud(request);
        }
        
        public mydentist.Result UpdateOpeninghrsCloud(string practiceId, System.Collections.Generic.List<mydentist.WebOpeningHour> webOpeningHours)
        {
            mydentist.UpdateOpeninghrsCloudRequest inValue = new mydentist.UpdateOpeninghrsCloudRequest();
            inValue.Body = new mydentist.UpdateOpeninghrsCloudRequestBody();
            inValue.Body.practiceId = practiceId;
            inValue.Body.webOpeningHours = webOpeningHours;
            mydentist.UpdateOpeninghrsCloudResponse retVal = ((mydentist.mydentistcloudImportSoap)(this)).UpdateOpeninghrsCloud(inValue);
            return retVal.Body.UpdateOpeninghrsCloudResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<mydentist.UpdateOpeninghrsCloudResponse> mydentist.mydentistcloudImportSoap.UpdateOpeninghrsCloudAsync(mydentist.UpdateOpeninghrsCloudRequest request)
        {
            return base.Channel.UpdateOpeninghrsCloudAsync(request);
        }
        
        public System.Threading.Tasks.Task<mydentist.UpdateOpeninghrsCloudResponse> UpdateOpeninghrsCloudAsync(string practiceId, System.Collections.Generic.List<mydentist.WebOpeningHour> webOpeningHours)
        {
            mydentist.UpdateOpeninghrsCloudRequest inValue = new mydentist.UpdateOpeninghrsCloudRequest();
            inValue.Body = new mydentist.UpdateOpeninghrsCloudRequestBody();
            inValue.Body.practiceId = practiceId;
            inValue.Body.webOpeningHours = webOpeningHours;
            return ((mydentist.mydentistcloudImportSoap)(this)).UpdateOpeninghrsCloudAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        mydentist.UpdatePracticeCCMCloudResponse mydentist.mydentistcloudImportSoap.UpdatePracticeCCMCloud(mydentist.UpdatePracticeCCMCloudRequest request)
        {
            return base.Channel.UpdatePracticeCCMCloud(request);
        }
        
        public mydentist.Result UpdatePracticeCCMCloud(string practiceId, System.Collections.Generic.List<mydentist.WebLimitedCompanyFooter> webLimitedCompanyFooters, System.Collections.Generic.List<mydentist.WebPartnershipFooter> webPartnershipFooters)
        {
            mydentist.UpdatePracticeCCMCloudRequest inValue = new mydentist.UpdatePracticeCCMCloudRequest();
            inValue.Body = new mydentist.UpdatePracticeCCMCloudRequestBody();
            inValue.Body.practiceId = practiceId;
            inValue.Body.webLimitedCompanyFooters = webLimitedCompanyFooters;
            inValue.Body.webPartnershipFooters = webPartnershipFooters;
            mydentist.UpdatePracticeCCMCloudResponse retVal = ((mydentist.mydentistcloudImportSoap)(this)).UpdatePracticeCCMCloud(inValue);
            return retVal.Body.UpdatePracticeCCMCloudResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<mydentist.UpdatePracticeCCMCloudResponse> mydentist.mydentistcloudImportSoap.UpdatePracticeCCMCloudAsync(mydentist.UpdatePracticeCCMCloudRequest request)
        {
            return base.Channel.UpdatePracticeCCMCloudAsync(request);
        }
        
        public System.Threading.Tasks.Task<mydentist.UpdatePracticeCCMCloudResponse> UpdatePracticeCCMCloudAsync(string practiceId, System.Collections.Generic.List<mydentist.WebLimitedCompanyFooter> webLimitedCompanyFooters, System.Collections.Generic.List<mydentist.WebPartnershipFooter> webPartnershipFooters)
        {
            mydentist.UpdatePracticeCCMCloudRequest inValue = new mydentist.UpdatePracticeCCMCloudRequest();
            inValue.Body = new mydentist.UpdatePracticeCCMCloudRequestBody();
            inValue.Body.practiceId = practiceId;
            inValue.Body.webLimitedCompanyFooters = webLimitedCompanyFooters;
            inValue.Body.webPartnershipFooters = webPartnershipFooters;
            return ((mydentist.mydentistcloudImportSoap)(this)).UpdatePracticeCCMCloudAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        mydentist.UpdateReviewsCloudResponse mydentist.mydentistcloudImportSoap.UpdateReviewsCloud(mydentist.UpdateReviewsCloudRequest request)
        {
            return base.Channel.UpdateReviewsCloud(request);
        }
        
        public mydentist.Result UpdateReviewsCloud(string practiceId, System.Collections.Generic.List<mydentist.WebReview> webReviews)
        {
            mydentist.UpdateReviewsCloudRequest inValue = new mydentist.UpdateReviewsCloudRequest();
            inValue.Body = new mydentist.UpdateReviewsCloudRequestBody();
            inValue.Body.practiceId = practiceId;
            inValue.Body.webReviews = webReviews;
            mydentist.UpdateReviewsCloudResponse retVal = ((mydentist.mydentistcloudImportSoap)(this)).UpdateReviewsCloud(inValue);
            return retVal.Body.UpdateReviewsCloudResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<mydentist.UpdateReviewsCloudResponse> mydentist.mydentistcloudImportSoap.UpdateReviewsCloudAsync(mydentist.UpdateReviewsCloudRequest request)
        {
            return base.Channel.UpdateReviewsCloudAsync(request);
        }
        
        public System.Threading.Tasks.Task<mydentist.UpdateReviewsCloudResponse> UpdateReviewsCloudAsync(string practiceId, System.Collections.Generic.List<mydentist.WebReview> webReviews)
        {
            mydentist.UpdateReviewsCloudRequest inValue = new mydentist.UpdateReviewsCloudRequest();
            inValue.Body = new mydentist.UpdateReviewsCloudRequestBody();
            inValue.Body.practiceId = practiceId;
            inValue.Body.webReviews = webReviews;
            return ((mydentist.mydentistcloudImportSoap)(this)).UpdateReviewsCloudAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        mydentist.CreateCasetudyCloudResponse mydentist.mydentistcloudImportSoap.CreateCasetudyCloud(mydentist.CreateCasetudyCloudRequest request)
        {
            return base.Channel.CreateCasetudyCloud(request);
        }
        
        public mydentist.Result CreateCasetudyCloud(mydentist.WebCaseStudy webCase, string practiceId, System.Collections.Generic.List<mydentist.WebCaseStudyImage> webCaseStudyImages)
        {
            mydentist.CreateCasetudyCloudRequest inValue = new mydentist.CreateCasetudyCloudRequest();
            inValue.Body = new mydentist.CreateCasetudyCloudRequestBody();
            inValue.Body.webCase = webCase;
            inValue.Body.practiceId = practiceId;
            inValue.Body.webCaseStudyImages = webCaseStudyImages;
            mydentist.CreateCasetudyCloudResponse retVal = ((mydentist.mydentistcloudImportSoap)(this)).CreateCasetudyCloud(inValue);
            return retVal.Body.CreateCasetudyCloudResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<mydentist.CreateCasetudyCloudResponse> mydentist.mydentistcloudImportSoap.CreateCasetudyCloudAsync(mydentist.CreateCasetudyCloudRequest request)
        {
            return base.Channel.CreateCasetudyCloudAsync(request);
        }
        
        public System.Threading.Tasks.Task<mydentist.CreateCasetudyCloudResponse> CreateCasetudyCloudAsync(mydentist.WebCaseStudy webCase, string practiceId, System.Collections.Generic.List<mydentist.WebCaseStudyImage> webCaseStudyImages)
        {
            mydentist.CreateCasetudyCloudRequest inValue = new mydentist.CreateCasetudyCloudRequest();
            inValue.Body = new mydentist.CreateCasetudyCloudRequestBody();
            inValue.Body.webCase = webCase;
            inValue.Body.practiceId = practiceId;
            inValue.Body.webCaseStudyImages = webCaseStudyImages;
            return ((mydentist.mydentistcloudImportSoap)(this)).CreateCasetudyCloudAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        mydentist.UpdateFacilitiesCloudResponse mydentist.mydentistcloudImportSoap.UpdateFacilitiesCloud(mydentist.UpdateFacilitiesCloudRequest request)
        {
            return base.Channel.UpdateFacilitiesCloud(request);
        }
        
        public mydentist.Result UpdateFacilitiesCloud(string practiceId, mydentist.WebFacility webFacility)
        {
            mydentist.UpdateFacilitiesCloudRequest inValue = new mydentist.UpdateFacilitiesCloudRequest();
            inValue.Body = new mydentist.UpdateFacilitiesCloudRequestBody();
            inValue.Body.practiceId = practiceId;
            inValue.Body.webFacility = webFacility;
            mydentist.UpdateFacilitiesCloudResponse retVal = ((mydentist.mydentistcloudImportSoap)(this)).UpdateFacilitiesCloud(inValue);
            return retVal.Body.UpdateFacilitiesCloudResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<mydentist.UpdateFacilitiesCloudResponse> mydentist.mydentistcloudImportSoap.UpdateFacilitiesCloudAsync(mydentist.UpdateFacilitiesCloudRequest request)
        {
            return base.Channel.UpdateFacilitiesCloudAsync(request);
        }
        
        public System.Threading.Tasks.Task<mydentist.UpdateFacilitiesCloudResponse> UpdateFacilitiesCloudAsync(string practiceId, mydentist.WebFacility webFacility)
        {
            mydentist.UpdateFacilitiesCloudRequest inValue = new mydentist.UpdateFacilitiesCloudRequest();
            inValue.Body = new mydentist.UpdateFacilitiesCloudRequestBody();
            inValue.Body.practiceId = practiceId;
            inValue.Body.webFacility = webFacility;
            return ((mydentist.mydentistcloudImportSoap)(this)).UpdateFacilitiesCloudAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        mydentist.UpdateClinicianCloudResponse mydentist.mydentistcloudImportSoap.UpdateClinicianCloud(mydentist.UpdateClinicianCloudRequest request)
        {
            return base.Channel.UpdateClinicianCloud(request);
        }
        
        public mydentist.Result UpdateClinicianCloud(mydentist.WebClinician webClinician, mydentist.WebClinicianImage webClinicianImage, string status)
        {
            mydentist.UpdateClinicianCloudRequest inValue = new mydentist.UpdateClinicianCloudRequest();
            inValue.Body = new mydentist.UpdateClinicianCloudRequestBody();
            inValue.Body.webClinician = webClinician;
            inValue.Body.webClinicianImage = webClinicianImage;
            inValue.Body.status = status;
            mydentist.UpdateClinicianCloudResponse retVal = ((mydentist.mydentistcloudImportSoap)(this)).UpdateClinicianCloud(inValue);
            return retVal.Body.UpdateClinicianCloudResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<mydentist.UpdateClinicianCloudResponse> mydentist.mydentistcloudImportSoap.UpdateClinicianCloudAsync(mydentist.UpdateClinicianCloudRequest request)
        {
            return base.Channel.UpdateClinicianCloudAsync(request);
        }
        
        public System.Threading.Tasks.Task<mydentist.UpdateClinicianCloudResponse> UpdateClinicianCloudAsync(mydentist.WebClinician webClinician, mydentist.WebClinicianImage webClinicianImage, string status)
        {
            mydentist.UpdateClinicianCloudRequest inValue = new mydentist.UpdateClinicianCloudRequest();
            inValue.Body = new mydentist.UpdateClinicianCloudRequestBody();
            inValue.Body.webClinician = webClinician;
            inValue.Body.webClinicianImage = webClinicianImage;
            inValue.Body.status = status;
            return ((mydentist.mydentistcloudImportSoap)(this)).UpdateClinicianCloudAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        mydentist.UnPublishBlogCloudResponse mydentist.mydentistcloudImportSoap.UnPublishBlogCloud(mydentist.UnPublishBlogCloudRequest request)
        {
            return base.Channel.UnPublishBlogCloud(request);
        }
        
        public mydentist.Result UnPublishBlogCloud(mydentist.BlogModel blog)
        {
            mydentist.UnPublishBlogCloudRequest inValue = new mydentist.UnPublishBlogCloudRequest();
            inValue.Body = new mydentist.UnPublishBlogCloudRequestBody();
            inValue.Body.blog = blog;
            mydentist.UnPublishBlogCloudResponse retVal = ((mydentist.mydentistcloudImportSoap)(this)).UnPublishBlogCloud(inValue);
            return retVal.Body.UnPublishBlogCloudResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<mydentist.UnPublishBlogCloudResponse> mydentist.mydentistcloudImportSoap.UnPublishBlogCloudAsync(mydentist.UnPublishBlogCloudRequest request)
        {
            return base.Channel.UnPublishBlogCloudAsync(request);
        }
        
        public System.Threading.Tasks.Task<mydentist.UnPublishBlogCloudResponse> UnPublishBlogCloudAsync(mydentist.BlogModel blog)
        {
            mydentist.UnPublishBlogCloudRequest inValue = new mydentist.UnPublishBlogCloudRequest();
            inValue.Body = new mydentist.UnPublishBlogCloudRequestBody();
            inValue.Body.blog = blog;
            return ((mydentist.mydentistcloudImportSoap)(this)).UnPublishBlogCloudAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        mydentist.UnPublishBlogwithIDCloudResponse mydentist.mydentistcloudImportSoap.UnPublishBlogwithIDCloud(mydentist.UnPublishBlogwithIDCloudRequest request)
        {
            return base.Channel.UnPublishBlogwithIDCloud(request);
        }
        
        public mydentist.Result UnPublishBlogwithIDCloud(int blogId)
        {
            mydentist.UnPublishBlogwithIDCloudRequest inValue = new mydentist.UnPublishBlogwithIDCloudRequest();
            inValue.Body = new mydentist.UnPublishBlogwithIDCloudRequestBody();
            inValue.Body.blogId = blogId;
            mydentist.UnPublishBlogwithIDCloudResponse retVal = ((mydentist.mydentistcloudImportSoap)(this)).UnPublishBlogwithIDCloud(inValue);
            return retVal.Body.UnPublishBlogwithIDCloudResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<mydentist.UnPublishBlogwithIDCloudResponse> mydentist.mydentistcloudImportSoap.UnPublishBlogwithIDCloudAsync(mydentist.UnPublishBlogwithIDCloudRequest request)
        {
            return base.Channel.UnPublishBlogwithIDCloudAsync(request);
        }
        
        public System.Threading.Tasks.Task<mydentist.UnPublishBlogwithIDCloudResponse> UnPublishBlogwithIDCloudAsync(int blogId)
        {
            mydentist.UnPublishBlogwithIDCloudRequest inValue = new mydentist.UnPublishBlogwithIDCloudRequest();
            inValue.Body = new mydentist.UnPublishBlogwithIDCloudRequestBody();
            inValue.Body.blogId = blogId;
            return ((mydentist.mydentistcloudImportSoap)(this)).UnPublishBlogwithIDCloudAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        mydentist.DeletePracticeResponse mydentist.mydentistcloudImportSoap.DeletePractice(mydentist.DeletePracticeRequest request)
        {
            return base.Channel.DeletePractice(request);
        }
        
        public mydentist.Result DeletePractice(string practiceId)
        {
            mydentist.DeletePracticeRequest inValue = new mydentist.DeletePracticeRequest();
            inValue.Body = new mydentist.DeletePracticeRequestBody();
            inValue.Body.practiceId = practiceId;
            mydentist.DeletePracticeResponse retVal = ((mydentist.mydentistcloudImportSoap)(this)).DeletePractice(inValue);
            return retVal.Body.DeletePracticeResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<mydentist.DeletePracticeResponse> mydentist.mydentistcloudImportSoap.DeletePracticeAsync(mydentist.DeletePracticeRequest request)
        {
            return base.Channel.DeletePracticeAsync(request);
        }
        
        public System.Threading.Tasks.Task<mydentist.DeletePracticeResponse> DeletePracticeAsync(string practiceId)
        {
            mydentist.DeletePracticeRequest inValue = new mydentist.DeletePracticeRequest();
            inValue.Body = new mydentist.DeletePracticeRequestBody();
            inValue.Body.practiceId = practiceId;
            return ((mydentist.mydentistcloudImportSoap)(this)).DeletePracticeAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        mydentist.AboutuscloudResponse mydentist.mydentistcloudImportSoap.Aboutuscloud(mydentist.AboutuscloudRequest request)
        {
            return base.Channel.Aboutuscloud(request);
        }
        
        public mydentist.Result Aboutuscloud(string practiceId, string aboutus)
        {
            mydentist.AboutuscloudRequest inValue = new mydentist.AboutuscloudRequest();
            inValue.Body = new mydentist.AboutuscloudRequestBody();
            inValue.Body.practiceId = practiceId;
            inValue.Body.aboutus = aboutus;
            mydentist.AboutuscloudResponse retVal = ((mydentist.mydentistcloudImportSoap)(this)).Aboutuscloud(inValue);
            return retVal.Body.AboutuscloudResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<mydentist.AboutuscloudResponse> mydentist.mydentistcloudImportSoap.AboutuscloudAsync(mydentist.AboutuscloudRequest request)
        {
            return base.Channel.AboutuscloudAsync(request);
        }
        
        public System.Threading.Tasks.Task<mydentist.AboutuscloudResponse> AboutuscloudAsync(string practiceId, string aboutus)
        {
            mydentist.AboutuscloudRequest inValue = new mydentist.AboutuscloudRequest();
            inValue.Body = new mydentist.AboutuscloudRequestBody();
            inValue.Body.practiceId = practiceId;
            inValue.Body.aboutus = aboutus;
            return ((mydentist.mydentistcloudImportSoap)(this)).AboutuscloudAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        mydentist.AddPracticeCloudResponse mydentist.mydentistcloudImportSoap.AddPracticeCloud(mydentist.AddPracticeCloudRequest request)
        {
            return base.Channel.AddPracticeCloud(request);
        }
        
        public mydentist.Result AddPracticeCloud(mydentist.WebPractice webpractice, System.Collections.Generic.List<mydentist.WebOpeningHour> webopeningHours, mydentist.WebFacility webFacility, System.Collections.Generic.List<mydentist.WebFreeAssessmentEvent> webfreeAssessmentEvents, System.Collections.Generic.List<mydentist.WebClinician> webClinicians, System.Collections.Generic.List<mydentist.WebTreatment> webtreatments)
        {
            mydentist.AddPracticeCloudRequest inValue = new mydentist.AddPracticeCloudRequest();
            inValue.Body = new mydentist.AddPracticeCloudRequestBody();
            inValue.Body.webpractice = webpractice;
            inValue.Body.webopeningHours = webopeningHours;
            inValue.Body.webFacility = webFacility;
            inValue.Body.webfreeAssessmentEvents = webfreeAssessmentEvents;
            inValue.Body.webClinicians = webClinicians;
            inValue.Body.webtreatments = webtreatments;
            mydentist.AddPracticeCloudResponse retVal = ((mydentist.mydentistcloudImportSoap)(this)).AddPracticeCloud(inValue);
            return retVal.Body.AddPracticeCloudResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<mydentist.AddPracticeCloudResponse> mydentist.mydentistcloudImportSoap.AddPracticeCloudAsync(mydentist.AddPracticeCloudRequest request)
        {
            return base.Channel.AddPracticeCloudAsync(request);
        }
        
        public System.Threading.Tasks.Task<mydentist.AddPracticeCloudResponse> AddPracticeCloudAsync(mydentist.WebPractice webpractice, System.Collections.Generic.List<mydentist.WebOpeningHour> webopeningHours, mydentist.WebFacility webFacility, System.Collections.Generic.List<mydentist.WebFreeAssessmentEvent> webfreeAssessmentEvents, System.Collections.Generic.List<mydentist.WebClinician> webClinicians, System.Collections.Generic.List<mydentist.WebTreatment> webtreatments)
        {
            mydentist.AddPracticeCloudRequest inValue = new mydentist.AddPracticeCloudRequest();
            inValue.Body = new mydentist.AddPracticeCloudRequestBody();
            inValue.Body.webpractice = webpractice;
            inValue.Body.webopeningHours = webopeningHours;
            inValue.Body.webFacility = webFacility;
            inValue.Body.webfreeAssessmentEvents = webfreeAssessmentEvents;
            inValue.Body.webClinicians = webClinicians;
            inValue.Body.webtreatments = webtreatments;
            return ((mydentist.mydentistcloudImportSoap)(this)).AddPracticeCloudAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        mydentist.UploadAlbumImageCloudResponse mydentist.mydentistcloudImportSoap.UploadAlbumImageCloud(mydentist.UploadAlbumImageCloudRequest request)
        {
            return base.Channel.UploadAlbumImageCloud(request);
        }
        
        public mydentist.Result UploadAlbumImageCloud(string imageid, string filename, byte[] imageStream, string imageGallery, string fileextension)
        {
            mydentist.UploadAlbumImageCloudRequest inValue = new mydentist.UploadAlbumImageCloudRequest();
            inValue.Body = new mydentist.UploadAlbumImageCloudRequestBody();
            inValue.Body.imageid = imageid;
            inValue.Body.filename = filename;
            inValue.Body.imageStream = imageStream;
            inValue.Body.imageGallery = imageGallery;
            inValue.Body.fileextension = fileextension;
            mydentist.UploadAlbumImageCloudResponse retVal = ((mydentist.mydentistcloudImportSoap)(this)).UploadAlbumImageCloud(inValue);
            return retVal.Body.UploadAlbumImageCloudResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<mydentist.UploadAlbumImageCloudResponse> mydentist.mydentistcloudImportSoap.UploadAlbumImageCloudAsync(mydentist.UploadAlbumImageCloudRequest request)
        {
            return base.Channel.UploadAlbumImageCloudAsync(request);
        }
        
        public System.Threading.Tasks.Task<mydentist.UploadAlbumImageCloudResponse> UploadAlbumImageCloudAsync(string imageid, string filename, byte[] imageStream, string imageGallery, string fileextension)
        {
            mydentist.UploadAlbumImageCloudRequest inValue = new mydentist.UploadAlbumImageCloudRequest();
            inValue.Body = new mydentist.UploadAlbumImageCloudRequestBody();
            inValue.Body.imageid = imageid;
            inValue.Body.filename = filename;
            inValue.Body.imageStream = imageStream;
            inValue.Body.imageGallery = imageGallery;
            inValue.Body.fileextension = fileextension;
            return ((mydentist.mydentistcloudImportSoap)(this)).UploadAlbumImageCloudAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        mydentist.DeleteClinicianImageRelationCloudResponse mydentist.mydentistcloudImportSoap.DeleteClinicianImageRelationCloud(mydentist.DeleteClinicianImageRelationCloudRequest request)
        {
            return base.Channel.DeleteClinicianImageRelationCloud(request);
        }
        
        public mydentist.Result DeleteClinicianImageRelationCloud(string practiceId, string gdcNo)
        {
            mydentist.DeleteClinicianImageRelationCloudRequest inValue = new mydentist.DeleteClinicianImageRelationCloudRequest();
            inValue.Body = new mydentist.DeleteClinicianImageRelationCloudRequestBody();
            inValue.Body.practiceId = practiceId;
            inValue.Body.gdcNo = gdcNo;
            mydentist.DeleteClinicianImageRelationCloudResponse retVal = ((mydentist.mydentistcloudImportSoap)(this)).DeleteClinicianImageRelationCloud(inValue);
            return retVal.Body.DeleteClinicianImageRelationCloudResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<mydentist.DeleteClinicianImageRelationCloudResponse> mydentist.mydentistcloudImportSoap.DeleteClinicianImageRelationCloudAsync(mydentist.DeleteClinicianImageRelationCloudRequest request)
        {
            return base.Channel.DeleteClinicianImageRelationCloudAsync(request);
        }
        
        public System.Threading.Tasks.Task<mydentist.DeleteClinicianImageRelationCloudResponse> DeleteClinicianImageRelationCloudAsync(string practiceId, string gdcNo)
        {
            mydentist.DeleteClinicianImageRelationCloudRequest inValue = new mydentist.DeleteClinicianImageRelationCloudRequest();
            inValue.Body = new mydentist.DeleteClinicianImageRelationCloudRequestBody();
            inValue.Body.practiceId = practiceId;
            inValue.Body.gdcNo = gdcNo;
            return ((mydentist.mydentistcloudImportSoap)(this)).DeleteClinicianImageRelationCloudAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        mydentist.UpdateCliniciansCloudResponse mydentist.mydentistcloudImportSoap.UpdateCliniciansCloud(mydentist.UpdateCliniciansCloudRequest request)
        {
            return base.Channel.UpdateCliniciansCloud(request);
        }
        
        public mydentist.Result UpdateCliniciansCloud(string practiceId, System.Collections.Generic.List<mydentist.WebClinician> webclinicians)
        {
            mydentist.UpdateCliniciansCloudRequest inValue = new mydentist.UpdateCliniciansCloudRequest();
            inValue.Body = new mydentist.UpdateCliniciansCloudRequestBody();
            inValue.Body.practiceId = practiceId;
            inValue.Body.webclinicians = webclinicians;
            mydentist.UpdateCliniciansCloudResponse retVal = ((mydentist.mydentistcloudImportSoap)(this)).UpdateCliniciansCloud(inValue);
            return retVal.Body.UpdateCliniciansCloudResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<mydentist.UpdateCliniciansCloudResponse> mydentist.mydentistcloudImportSoap.UpdateCliniciansCloudAsync(mydentist.UpdateCliniciansCloudRequest request)
        {
            return base.Channel.UpdateCliniciansCloudAsync(request);
        }
        
        public System.Threading.Tasks.Task<mydentist.UpdateCliniciansCloudResponse> UpdateCliniciansCloudAsync(string practiceId, System.Collections.Generic.List<mydentist.WebClinician> webclinicians)
        {
            mydentist.UpdateCliniciansCloudRequest inValue = new mydentist.UpdateCliniciansCloudRequest();
            inValue.Body = new mydentist.UpdateCliniciansCloudRequestBody();
            inValue.Body.practiceId = practiceId;
            inValue.Body.webclinicians = webclinicians;
            return ((mydentist.mydentistcloudImportSoap)(this)).UpdateCliniciansCloudAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        mydentist.SetAcceptingNewPatientsCloudResponse mydentist.mydentistcloudImportSoap.SetAcceptingNewPatientsCloud(mydentist.SetAcceptingNewPatientsCloudRequest request)
        {
            return base.Channel.SetAcceptingNewPatientsCloud(request);
        }
        
        public mydentist.Result SetAcceptingNewPatientsCloud(string practiceId, bool isAcceptingNewPatients)
        {
            mydentist.SetAcceptingNewPatientsCloudRequest inValue = new mydentist.SetAcceptingNewPatientsCloudRequest();
            inValue.Body = new mydentist.SetAcceptingNewPatientsCloudRequestBody();
            inValue.Body.practiceId = practiceId;
            inValue.Body.isAcceptingNewPatients = isAcceptingNewPatients;
            mydentist.SetAcceptingNewPatientsCloudResponse retVal = ((mydentist.mydentistcloudImportSoap)(this)).SetAcceptingNewPatientsCloud(inValue);
            return retVal.Body.SetAcceptingNewPatientsCloudResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<mydentist.SetAcceptingNewPatientsCloudResponse> mydentist.mydentistcloudImportSoap.SetAcceptingNewPatientsCloudAsync(mydentist.SetAcceptingNewPatientsCloudRequest request)
        {
            return base.Channel.SetAcceptingNewPatientsCloudAsync(request);
        }
        
        public System.Threading.Tasks.Task<mydentist.SetAcceptingNewPatientsCloudResponse> SetAcceptingNewPatientsCloudAsync(string practiceId, bool isAcceptingNewPatients)
        {
            mydentist.SetAcceptingNewPatientsCloudRequest inValue = new mydentist.SetAcceptingNewPatientsCloudRequest();
            inValue.Body = new mydentist.SetAcceptingNewPatientsCloudRequestBody();
            inValue.Body.practiceId = practiceId;
            inValue.Body.isAcceptingNewPatients = isAcceptingNewPatients;
            return ((mydentist.mydentistcloudImportSoap)(this)).SetAcceptingNewPatientsCloudAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        mydentist.SetOnlineBookingEnabledCloudResponse mydentist.mydentistcloudImportSoap.SetOnlineBookingEnabledCloud(mydentist.SetOnlineBookingEnabledCloudRequest request)
        {
            return base.Channel.SetOnlineBookingEnabledCloud(request);
        }
        
        public mydentist.Result SetOnlineBookingEnabledCloud(string practiceId, bool isOnlineBookingEnabled)
        {
            mydentist.SetOnlineBookingEnabledCloudRequest inValue = new mydentist.SetOnlineBookingEnabledCloudRequest();
            inValue.Body = new mydentist.SetOnlineBookingEnabledCloudRequestBody();
            inValue.Body.practiceId = practiceId;
            inValue.Body.isOnlineBookingEnabled = isOnlineBookingEnabled;
            mydentist.SetOnlineBookingEnabledCloudResponse retVal = ((mydentist.mydentistcloudImportSoap)(this)).SetOnlineBookingEnabledCloud(inValue);
            return retVal.Body.SetOnlineBookingEnabledCloudResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<mydentist.SetOnlineBookingEnabledCloudResponse> mydentist.mydentistcloudImportSoap.SetOnlineBookingEnabledCloudAsync(mydentist.SetOnlineBookingEnabledCloudRequest request)
        {
            return base.Channel.SetOnlineBookingEnabledCloudAsync(request);
        }
        
        public System.Threading.Tasks.Task<mydentist.SetOnlineBookingEnabledCloudResponse> SetOnlineBookingEnabledCloudAsync(string practiceId, bool isOnlineBookingEnabled)
        {
            mydentist.SetOnlineBookingEnabledCloudRequest inValue = new mydentist.SetOnlineBookingEnabledCloudRequest();
            inValue.Body = new mydentist.SetOnlineBookingEnabledCloudRequestBody();
            inValue.Body.practiceId = practiceId;
            inValue.Body.isOnlineBookingEnabled = isOnlineBookingEnabled;
            return ((mydentist.mydentistcloudImportSoap)(this)).SetOnlineBookingEnabledCloudAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        mydentist.PublishBlogCloudResponse mydentist.mydentistcloudImportSoap.PublishBlogCloud(mydentist.PublishBlogCloudRequest request)
        {
            return base.Channel.PublishBlogCloud(request);
        }
        
        public mydentist.Result PublishBlogCloud(mydentist.BlogModel blog)
        {
            mydentist.PublishBlogCloudRequest inValue = new mydentist.PublishBlogCloudRequest();
            inValue.Body = new mydentist.PublishBlogCloudRequestBody();
            inValue.Body.blog = blog;
            mydentist.PublishBlogCloudResponse retVal = ((mydentist.mydentistcloudImportSoap)(this)).PublishBlogCloud(inValue);
            return retVal.Body.PublishBlogCloudResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<mydentist.PublishBlogCloudResponse> mydentist.mydentistcloudImportSoap.PublishBlogCloudAsync(mydentist.PublishBlogCloudRequest request)
        {
            return base.Channel.PublishBlogCloudAsync(request);
        }
        
        public System.Threading.Tasks.Task<mydentist.PublishBlogCloudResponse> PublishBlogCloudAsync(mydentist.BlogModel blog)
        {
            mydentist.PublishBlogCloudRequest inValue = new mydentist.PublishBlogCloudRequest();
            inValue.Body = new mydentist.PublishBlogCloudRequestBody();
            inValue.Body.blog = blog;
            return ((mydentist.mydentistcloudImportSoap)(this)).PublishBlogCloudAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        mydentist.SetDirectAccessEnabledCloudResponse mydentist.mydentistcloudImportSoap.SetDirectAccessEnabledCloud(mydentist.SetDirectAccessEnabledCloudRequest request)
        {
            return base.Channel.SetDirectAccessEnabledCloud(request);
        }
        
        public mydentist.Result SetDirectAccessEnabledCloud(string practiceId, bool isDirectAccessEnabled)
        {
            mydentist.SetDirectAccessEnabledCloudRequest inValue = new mydentist.SetDirectAccessEnabledCloudRequest();
            inValue.Body = new mydentist.SetDirectAccessEnabledCloudRequestBody();
            inValue.Body.practiceId = practiceId;
            inValue.Body.isDirectAccessEnabled = isDirectAccessEnabled;
            mydentist.SetDirectAccessEnabledCloudResponse retVal = ((mydentist.mydentistcloudImportSoap)(this)).SetDirectAccessEnabledCloud(inValue);
            return retVal.Body.SetDirectAccessEnabledCloudResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<mydentist.SetDirectAccessEnabledCloudResponse> mydentist.mydentistcloudImportSoap.SetDirectAccessEnabledCloudAsync(mydentist.SetDirectAccessEnabledCloudRequest request)
        {
            return base.Channel.SetDirectAccessEnabledCloudAsync(request);
        }
        
        public System.Threading.Tasks.Task<mydentist.SetDirectAccessEnabledCloudResponse> SetDirectAccessEnabledCloudAsync(string practiceId, bool isDirectAccessEnabled)
        {
            mydentist.SetDirectAccessEnabledCloudRequest inValue = new mydentist.SetDirectAccessEnabledCloudRequest();
            inValue.Body = new mydentist.SetDirectAccessEnabledCloudRequestBody();
            inValue.Body.practiceId = practiceId;
            inValue.Body.isDirectAccessEnabled = isDirectAccessEnabled;
            return ((mydentist.mydentistcloudImportSoap)(this)).SetDirectAccessEnabledCloudAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        mydentist.UpdateTreatmentCostCloudResponse mydentist.mydentistcloudImportSoap.UpdateTreatmentCostCloud(mydentist.UpdateTreatmentCostCloudRequest request)
        {
            return base.Channel.UpdateTreatmentCostCloud(request);
        }
        
        public mydentist.Result UpdateTreatmentCostCloud(string practiceId, System.Collections.Generic.List<mydentist.WebTreatment> webtreatments)
        {
            mydentist.UpdateTreatmentCostCloudRequest inValue = new mydentist.UpdateTreatmentCostCloudRequest();
            inValue.Body = new mydentist.UpdateTreatmentCostCloudRequestBody();
            inValue.Body.practiceId = practiceId;
            inValue.Body.webtreatments = webtreatments;
            mydentist.UpdateTreatmentCostCloudResponse retVal = ((mydentist.mydentistcloudImportSoap)(this)).UpdateTreatmentCostCloud(inValue);
            return retVal.Body.UpdateTreatmentCostCloudResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<mydentist.UpdateTreatmentCostCloudResponse> mydentist.mydentistcloudImportSoap.UpdateTreatmentCostCloudAsync(mydentist.UpdateTreatmentCostCloudRequest request)
        {
            return base.Channel.UpdateTreatmentCostCloudAsync(request);
        }
        
        public System.Threading.Tasks.Task<mydentist.UpdateTreatmentCostCloudResponse> UpdateTreatmentCostCloudAsync(string practiceId, System.Collections.Generic.List<mydentist.WebTreatment> webtreatments)
        {
            mydentist.UpdateTreatmentCostCloudRequest inValue = new mydentist.UpdateTreatmentCostCloudRequest();
            inValue.Body = new mydentist.UpdateTreatmentCostCloudRequestBody();
            inValue.Body.practiceId = practiceId;
            inValue.Body.webtreatments = webtreatments;
            return ((mydentist.mydentistcloudImportSoap)(this)).UpdateTreatmentCostCloudAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        mydentist.UpdateFreeAssessmentCloudResponse mydentist.mydentistcloudImportSoap.UpdateFreeAssessmentCloud(mydentist.UpdateFreeAssessmentCloudRequest request)
        {
            return base.Channel.UpdateFreeAssessmentCloud(request);
        }
        
        public mydentist.Result UpdateFreeAssessmentCloud(string practiceId, System.Collections.Generic.List<mydentist.WebFreeAssessmentEvent> WebfreeAssessments)
        {
            mydentist.UpdateFreeAssessmentCloudRequest inValue = new mydentist.UpdateFreeAssessmentCloudRequest();
            inValue.Body = new mydentist.UpdateFreeAssessmentCloudRequestBody();
            inValue.Body.practiceId = practiceId;
            inValue.Body.WebfreeAssessments = WebfreeAssessments;
            mydentist.UpdateFreeAssessmentCloudResponse retVal = ((mydentist.mydentistcloudImportSoap)(this)).UpdateFreeAssessmentCloud(inValue);
            return retVal.Body.UpdateFreeAssessmentCloudResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<mydentist.UpdateFreeAssessmentCloudResponse> mydentist.mydentistcloudImportSoap.UpdateFreeAssessmentCloudAsync(mydentist.UpdateFreeAssessmentCloudRequest request)
        {
            return base.Channel.UpdateFreeAssessmentCloudAsync(request);
        }
        
        public System.Threading.Tasks.Task<mydentist.UpdateFreeAssessmentCloudResponse> UpdateFreeAssessmentCloudAsync(string practiceId, System.Collections.Generic.List<mydentist.WebFreeAssessmentEvent> WebfreeAssessments)
        {
            mydentist.UpdateFreeAssessmentCloudRequest inValue = new mydentist.UpdateFreeAssessmentCloudRequest();
            inValue.Body = new mydentist.UpdateFreeAssessmentCloudRequestBody();
            inValue.Body.practiceId = practiceId;
            inValue.Body.WebfreeAssessments = WebfreeAssessments;
            return ((mydentist.mydentistcloudImportSoap)(this)).UpdateFreeAssessmentCloudAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        mydentist.UpdateTitleCloudResponse mydentist.mydentistcloudImportSoap.UpdateTitleCloud(mydentist.UpdateTitleCloudRequest request)
        {
            return base.Channel.UpdateTitleCloud(request);
        }
        
        public mydentist.Result UpdateTitleCloud(mydentist.WebPractice webpractice)
        {
            mydentist.UpdateTitleCloudRequest inValue = new mydentist.UpdateTitleCloudRequest();
            inValue.Body = new mydentist.UpdateTitleCloudRequestBody();
            inValue.Body.webpractice = webpractice;
            mydentist.UpdateTitleCloudResponse retVal = ((mydentist.mydentistcloudImportSoap)(this)).UpdateTitleCloud(inValue);
            return retVal.Body.UpdateTitleCloudResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<mydentist.UpdateTitleCloudResponse> mydentist.mydentistcloudImportSoap.UpdateTitleCloudAsync(mydentist.UpdateTitleCloudRequest request)
        {
            return base.Channel.UpdateTitleCloudAsync(request);
        }
        
        public System.Threading.Tasks.Task<mydentist.UpdateTitleCloudResponse> UpdateTitleCloudAsync(mydentist.WebPractice webpractice)
        {
            mydentist.UpdateTitleCloudRequest inValue = new mydentist.UpdateTitleCloudRequest();
            inValue.Body = new mydentist.UpdateTitleCloudRequestBody();
            inValue.Body.webpractice = webpractice;
            return ((mydentist.mydentistcloudImportSoap)(this)).UpdateTitleCloudAsync(inValue);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
        
        private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.mydentistcloudImportSoap))
            {
                System.ServiceModel.BasicHttpBinding result = new System.ServiceModel.BasicHttpBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                result.Security.Mode = System.ServiceModel.BasicHttpSecurityMode.Transport;
                return result;
            }
            if ((endpointConfiguration == EndpointConfiguration.mydentistcloudImportSoap12))
            {
                System.ServiceModel.Channels.CustomBinding result = new System.ServiceModel.Channels.CustomBinding();
                System.ServiceModel.Channels.TextMessageEncodingBindingElement textBindingElement = new System.ServiceModel.Channels.TextMessageEncodingBindingElement();
                textBindingElement.MessageVersion = System.ServiceModel.Channels.MessageVersion.CreateVersion(System.ServiceModel.EnvelopeVersion.Soap12, System.ServiceModel.Channels.AddressingVersion.None);
                result.Elements.Add(textBindingElement);
                System.ServiceModel.Channels.HttpsTransportBindingElement httpsBindingElement = new System.ServiceModel.Channels.HttpsTransportBindingElement();
                httpsBindingElement.AllowCookies = true;
                httpsBindingElement.MaxBufferSize = int.MaxValue;
                httpsBindingElement.MaxReceivedMessageSize = int.MaxValue;
                result.Elements.Add(httpsBindingElement);
                return result;
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.mydentistcloudImportSoap))
            {
                return new System.ServiceModel.EndpointAddress("https://mydentist-stg.sitefinity.cloud/import/mydentistcloudImport.asmx");
            }
            if ((endpointConfiguration == EndpointConfiguration.mydentistcloudImportSoap12))
            {
                return new System.ServiceModel.EndpointAddress("https://mydentist-stg.sitefinity.cloud/import/mydentistcloudImport.asmx");
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        public enum EndpointConfiguration
        {
            
            mydentistcloudImportSoap,
            
            mydentistcloudImportSoap12,
        }
    }
}
