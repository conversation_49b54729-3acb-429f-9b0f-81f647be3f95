﻿using AutoMapper;
using Mydentist.MyDSitefinityAPI.Domain;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mydentist.MyDSitefinityAPI.ImplementationServices
{
    public class RecruitmentWebsiteFormProfile: Profile
    {
        public RecruitmentWebsiteFormProfile()
        {

            CreateMap<RecruitmentWebsiteFormRequest, RecruitmentWebsiteFormModel>()
                        .ForMember(r => r.DesiredRole, opt => opt.Ignore())
                        .ForMember(r => r.DesiredWorkingLocation, opt => opt.MapFrom(src => src.DesiredLocation))
                        .ForMember(r => r.UniversityPostgraduate, opt => opt.Ignore())
                        .ForMember(r => r.UniversityUndergraduate, opt => opt.Ignore())
                        .ForMember(r => r.CountryOfQualification, opt => opt.Ignore())
                        .ForMember(r => r.DesiredWorkingLocationLatitude, opt => opt.MapFrom(src => src.DesiredLocationLatitude))
                        .ForMember(r => r.DesiredWorkingLocationLongitude, opt => opt.MapFrom(src => src.DesiredLocationLongitude))
                        .ForMember(s => s.YearQualifiedUndergraduate, opt => opt.ResolveUsing(d =>
                        {
                            if (d == null || string.IsNullOrEmpty(d.YearQualifiedUndergraduate) || d.YearQualifiedUndergraduate == "--" || d.YearQualifiedUndergraduate == "0000" || d.YearQualifiedUndergraduate == "0")
                                return (DateOnly?)null;

                            if (!string.IsNullOrEmpty(d.YearQualifiedUndergraduate))
                                return new DateOnly(int.Parse(d.YearQualifiedUndergraduate), 7, 1);

                            return (DateOnly?)null;
                        }))
                        .ForMember(s => s.YearQualifiedPostgraduate, opt => opt.ResolveUsing(d =>
                        {
                            if (d == null || string.IsNullOrEmpty(d.YearQualifiedPostgraduate) || d.YearQualifiedPostgraduate == "--" || d.YearQualifiedUndergraduate == "0000" || d.YearQualifiedUndergraduate == "0")
                                return (DateOnly?)null;

                            if (!string.IsNullOrEmpty(d.YearQualifiedPostgraduate))
                                return new DateOnly(int.Parse(d.YearQualifiedPostgraduate), 7, 1);

                            return (DateOnly?)null;
                        }));

            CreateMap<RecruitmentWebsiteFormModel, RecruitmentWebsiteFormModel>()
                         .ForAllMembers(o => o.Condition((source, destination, member) => member != null));
            
        }    
    }

    public static class AutoMapperCompatibilityExtensions
    {
        public static void ResolveUsing<TSource, TDestination, TMember, TResult>(this IMemberConfigurationExpression<TSource, TDestination, TMember> member, Func<TSource, TResult> resolver) => member.MapFrom((Func<TSource, TDestination, TResult>)((src, dest) => resolver(src)));
    }
}
