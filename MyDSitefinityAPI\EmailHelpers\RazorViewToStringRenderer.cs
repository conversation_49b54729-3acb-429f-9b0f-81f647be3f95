﻿using Microsoft.AspNetCore.Mvc.Abstractions;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.Razor;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc.ViewEngines;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.AspNetCore.Mvc;
using MyDSitefinityAPI.Interfaces;

namespace MyDSitefinityAPI.EmailHelpers
{
    public class RazorViewToStringRenderer : IViewRender
    {
        public RazorViewToStringRenderer(
        IRazorViewEngine viewEngine,
        ITempDataProvider tempDataProvider,
        IServiceProvider serviceProvider
        )
        {
            _viewEngine = viewEngine;
            _tempDataProvider = tempDataProvider;
            _serviceProvider = serviceProvider;
        }

        public async Task<string> RenderViewToString<TModel>(string name, TModel model)
        {
            ActionContext actionContext = GetActionContext();

            ViewEngineResult viewEngineResult = _viewEngine.FindView(actionContext, name, false);

            if (!viewEngineResult.Success)
            {
                throw new InvalidOperationException(string.Format("Couldn't find view '{0}'", name));
            }

            IView view = viewEngineResult.View;

            using (StringWriter textWriter = new StringWriter())
            {
                ViewDataDictionary viewData = new ViewDataDictionary<TModel>(
                    new EmptyModelMetadataProvider(),
                    new ModelStateDictionary()
                )
                {
                    Model = model
                };

                TempDataDictionary tempData = new TempDataDictionary(
                    actionContext.HttpContext,
                    _tempDataProvider
                );

                ViewContext viewContext = new ViewContext(
                    actionContext,
                    view,
                    viewData,
                    tempData,
                    textWriter,
                    new HtmlHelperOptions()
                );

                await view.RenderAsync(viewContext);
                
                return textWriter.ToString();
            }
        }

        private ActionContext GetActionContext()
        {
            DefaultHttpContext httpContext = new DefaultHttpContext
            {
                RequestServices = _serviceProvider
            };

            return new ActionContext(httpContext, new RouteData(), new ActionDescriptor());
        }

        private readonly IServiceProvider _serviceProvider;
        private readonly ITempDataProvider _tempDataProvider;
        private readonly IRazorViewEngine _viewEngine;

    }
}
