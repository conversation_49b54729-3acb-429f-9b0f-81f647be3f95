﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Mydentist.MyDSitefinityAPI.Domain;
using Mydentist.MyDSitefinityAPI.ImplementationServices.Constants;
using Mydentist.MyDSitefinityAPI.ImplementationServices.Dtos;
using Mydentist.MyDSitefinityAPI.ImplementationServices.Interfaces;
using Mydentist.MyDSitefinityAPI.Persistence;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;

namespace Mydentist.MyDSitefinityAPI.ImplementationServices.Services
{
    public class PracticeManagerService : IPracticeManagerServices
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly DbContextWarehouse _dbContextWarehouse;
        private readonly IConfiguration _configuration;
        private readonly ILogger _logger;

        public PracticeManagerService(
            IHttpClientFactory httpClientFactory,
            DbContextWarehouse dbContextWarehouse,
            IConfiguration configuration,
            ILogger logger)
        {
            _httpClientFactory = httpClientFactory;
            _dbContextWarehouse = dbContextWarehouse;
            _configuration = configuration;
            _logger = logger;
        }

        public string ComposePracticeManagerMail(string practiceManagerEmail, Referrals referral, string template)
        {
            string emailContent = template
                .Replace("{dateOfSubmission}", DateTime.Now.ToLongDateString())
                .Replace("{patientTitle}", referral.PatientTitle)
                .Replace("{patientFirstName}", referral.PatientFirstName)
                .Replace("{patientLastName}", referral.PatientLastName)
                .Replace("{patientDobYear}", referral.PatientDateOfBirth)
                .Replace("{patientNhsNumber}", referral.PatientNhsNumber)
                .Replace("{patientContactNumber}", referral.PatientContactNumber)
                .Replace("{patientEmail}", referral.PatientEmail)
                .Replace("{patientAddress1}", referral.PatientAddressLine1)
                .Replace("{patientAddress2}", referral.PatientAddressLine2)
                .Replace("{patientTown}", referral.PatientTown)
                .Replace("{patientCounty}", referral.PatientCounty)
                .Replace("{patientPostcode}", referral.PatientPostcode)
                .Replace("{patientInterest}", referral.PatientInterest)

                .Replace("{clinicianGdcNumber}", referral.ClinicianGdcNumber)
                .Replace("{clinicianFirstName}", referral.ClinicianFirstName)
                .Replace("{clinicianLastName}", referral.ClinicianLastName)
                .Replace("{practiceName}", referral.PracticeName)
                .Replace("{isClinicianReferralUrgent}", referral.IsClinicianReferralUrgent)
                .Replace("{clinicianAddress1}", referral.ClinicianAddressLine1)
                .Replace("{clinicianAddress2}", referral.ClinicianAddressLine2)
                .Replace("{clinicianTown}", referral.ClinicianTown)
                .Replace("{clinicianCounty}", referral.ClinicianCounty)
                .Replace("{clinicianPostcode}", referral.ClinicianPostcode)

                .Replace("{isNhsOrPrivate}", referral.IsNhsOrPrivate)
                .Replace("{clinicianReasonForReferral}", referral.ClinicianReasonForReferral)
                .Replace("{wasDptRadioTakenLastYear}", referral.WasDptRadioTakenLastYear)
                .Replace("{clinicianToSee}", referral.ClinicianToSee);
            return emailContent;
        }

        public async Task<PracticeLocationDto> FindNearestPracticeByPostCode(string postCode)
        {
            try
            {
                var httpClient = _httpClientFactory.CreateClient(StaticValues.PracticeLocationApi);
                var response = await httpClient.GetFromJsonAsync<PracticeLocationDto>($"{_configuration.GetSection("NearestPractice:NearestPostCodeEndPoint").Value}{postCode}");
                return response ?? new PracticeLocationDto();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, nameof(FindNearestPracticeByPostCode));
                return new PracticeLocationDto();
            }
        }

        public async Task<PracticeDetailsDto> GetPracticeDetailsById(int id)
        {
            //todo add PracticeDetails to this data context
            return new PracticeDetailsDto();

            //var details = await _dbContextWarehouse.PracticeDetails.FirstOrDefaultAsync(x => x.PracticeID == id);
            //if (details != null)
            //{
            //    return new PracticeDetailsDto
            //    {
            //        PracticeID = details.PracticeID,
            //        PMEmail = details.PMEmail,
            //    };
            //}
            //else
            //{
            //    return new PracticeDetailsDto();
            //}
        }
    }

}
