﻿using MyDSitefinityAPI.DBContext;
using MyDSitefinityAPI.Interfaces;
using MyDSitefinityAPI.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Text.Json;

namespace MyDSitefinityAPI.Services
{
    public class VacancyDbService : IVacancyDbService
    {
        private DBWarehouseContext db { get; set; }
        private ClinicianIntegrationMultiRoleContext clintDb { get; set; }
        private ILogger _logger { get; set; }

        public VacancyDbService(ILogger logger)
        {
            _logger = logger;
            clintDb = new ClinicianIntegrationMultiRoleContext();
            db = new DBWarehouseContext();
        }

        public int? GetSharedListId(string? value, string source = "source")
        {
            if (source == "CurrentCountry" || source == "GdcReason")
            {
                if (value == "I don't have a GDC Number")
                {
                    value = "I don’t have a GDC Number";
                }

                if (value == "United Kingdom")
                {
                    return clintDb.sharedList.Where(x => x.DisplayValue == "Great Britain").FirstOrDefault()?.Id;
                }

                if (source == "CurrentCountry" && (string.IsNullOrEmpty(value) || value == "Other" || value == "--"))
                {
                    return clintDb.sharedList.Where(x => x.DisplayValue == "Other" && x.Name == "Country").FirstOrDefault()?.Id;
                }

                if (!string.IsNullOrEmpty(value))
                {
                    return clintDb.sharedList.Where(x => x.DisplayValue.ToLower() == value.ToLower()).FirstOrDefault()?.Id;
                }
            }

            if (source == "CountryOfQualification" && (string.IsNullOrEmpty(value) || value == "Other" || value == "--"))
            {
                return clintDb.sharedList.Where(x => x.DisplayValue == "Other" && x.Name == "Country").FirstOrDefault()?.Id;
            }

            if (source == "University" && (string.IsNullOrEmpty(value) || value == "Other" || value == "--"))
            {
                return clintDb.sharedList.Where(x => x.DisplayValue == "Other" && x.Name == "University").FirstOrDefault()?.Id;

            }

            if (source== "Salutation" && (string.IsNullOrEmpty(value) || value == "Other" || value == "--"))
            {
                return clintDb.sharedList.Where(x => x.DisplayValue == "Other" && x.Name == "Salutation").FirstOrDefault()?.Id;
            }

            return clintDb.sharedList.Where(x => x.DisplayValue == value).FirstOrDefault()?.Id;
        }

        public string GetCVUrl(SitefinityFormV2? formSubmission)
        {
            var fileField = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "FileFieldController").FirstOrDefault()?.Value;
            string link = string.Empty;

            if (fileField != null)
            {
                dynamic jsonData = JsonConvert.DeserializeObject<dynamic>(fileField.ToString());
                JValue value = jsonData[0].ChildItemAdditionalInfo;
                link = value.ToString().Split(new char[] { '|' })[1];
                return link;
            }
            else
            {
                return string.Empty;
            }
        }

        public async Task SaveApplicationLog(SitefinityFormV2? formSubmission, JsonElement rawJson, string appicationJsonString)
        {
            string link = GetCVUrl(formSubmission);
            if (link == string.Empty)
            {
                _logger.LogInformation($"(Vacancy Apply) Vacancy Application {formSubmission.OriginalEvent.EntryId} did not contain a file field");
            }

            dynamic application = JsonConvert.DeserializeObject<dynamic>(appicationJsonString.ToString());
            _logger.LogInformation($"(Vacancy Apply) Created applicationLog Item");

            ApplicationLog appLog = new ApplicationLog()
            {
                ContactNumber = application.applicant.contactNumber,
                EmailAddress = application.applicant.emailAddress,
                FirstName = application.applicant.firstName,
                LastName = application.applicant.lastName,
                GDCNumber = application.applicant.gdcNumber,
                PostCode = application.applicant.postCode.ToString(),
                Title = application.applicant.titleId.ToString(),
                Address = "",
                CVLink = application.cvUrl,
                DateCreated = DateTime.UtcNow,
                RawJson = rawJson.ToString(),
                VacancyId = application.vacancyId
            };
            
            _logger.LogInformation($"(Vacancy Apply) Now saving to applicationLog Table");
            db.ApplicationLog.Add(appLog);
            await db.SaveChangesAsync();
        }

    }
}
