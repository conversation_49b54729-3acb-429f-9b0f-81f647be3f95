﻿using Swashbuckle.AspNetCore.Filters;

namespace MyDSitefinityAPI.Models
{
    public class SampleVacancyApplication : IExamplesProvider<VacancyApplication>
    {
        public VacancyApplication GetExamples()
        {

            return new VacancyApplication()
            {
                applicant = new Applicant()
                {
                    title = "Test",
                    firstName = "myd Developer",
                    lastName = "Dev Team",
                    gdcNumber = "1009872",
                    emailAddress = "<EMAIL>",
                    contactNumber = "07373543765",
                    address = "TestAddress sample",
                    postCode = "amazing postcode"
                },
                vacancyId = 3069,
                googleClientId = "784494",
                preferredRadius = 5
            };
            
        }

    }
}
