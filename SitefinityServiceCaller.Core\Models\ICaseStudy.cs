﻿namespace IDHGroup.SharedLibraries.SitefinityServiceCaller.Core.Models
{
  public  interface ICaseStudy
    {
        /// <summary>
        ///     The unique identifier belonging to the found case study
        /// </summary>
         string Casenotes { get; set; }
         string CaseStudyType { get; set; }
         bool ConsentForFacePhoto { get; set; }
         bool ConsentForTeethPhoto { get; set; }
         bool ConsentForTestimonial { get; set; }
         string Id { get; set; }
         string PatientName { get; set; }
         string PatientTestimonial { get; set; }
         string PracticeId { get; set; }
         string Title { get; set; }
         bool UploadedFace { get; set; }
         bool UploadedTeeth { get; set; }
         Guid WebAfterFaceImageId { get; set; }
         Guid WebAfterFaceThumbnailId { get; set; }
         Guid WebAfterTeethImageId { get; set; }
         Guid WebAfterTeethThumbnailId { get; set; }
         Guid WebBeforeFaceImageId { get; set; }
         Guid WebBeforeFaceThumbnailId { get; set; }
         Guid WebBeforeTeethImageId { get; set; }
         Guid WebBeforeTeethThumbnailId { get; set; }
    }
}