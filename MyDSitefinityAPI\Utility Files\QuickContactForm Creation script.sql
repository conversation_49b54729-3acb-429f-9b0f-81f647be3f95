
CREATE TABLE [dbo].[sf_quickcontactpmform](
	[Id] [uniqueidentifier] NOT NULL,
	[Form_Text_Box__C004] [nvarchar](255) NULL,
	[Form_Text_Box__C003] [nvarchar](255) NULL,
	[Form_Text_Box__C001] [nvarchar](255) NULL,
	[Form_Text_Box] [nvarchar](255) NULL,
	[Hidden_Practice_Id] [nvarchar](255) NULL,
	[Form_Text_Box__C011] [nvarchar](255) NULL,
	[Form_Text_Box__C010] [nvarchar](255) NULL,
	[Form_Text_Box__C015] [nvarchar](255) NULL,
	[g_a_client_id] [nvarchar](255) NULL,
	[Processed_Date] [nvarchar](255) NULL,
	[Error_Date] [nvarchar](255) NULL,
	[RawJson] [varchar](max) NULL,
	[CreatedDate] [datetime2](7) NULL,
	[TreatmentOptions] [nvarchar](2000) NULL,
 CONSTRAINT [pk_sf_quickcontactpmform] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[sf_quickcontactpmform] ADD  CONSTRAINT [DF_sf_quickcontactpmform_createdDate]  DEFAULT (getdate()) FOR [CreatedDate]
GO