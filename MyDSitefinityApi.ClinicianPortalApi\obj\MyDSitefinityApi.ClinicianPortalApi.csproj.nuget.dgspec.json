{"format": 1, "restore": {"C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityApi.ClinicianPortalApi\\MyDSitefinityApi.ClinicianPortalApi.csproj": {}}, "projects": {"C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityApi.ClinicianPortalApi\\MyDSitefinityApi.ClinicianPortalApi.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityApi.ClinicianPortalApi\\MyDSitefinityApi.ClinicianPortalApi.csproj", "projectName": "Mydentist.MyDSitefinityApi.ClinicianPortalApi", "projectPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityApi.ClinicianPortalApi\\MyDSitefinityApi.ClinicianPortalApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityApi.ClinicianPortalApi\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Extensions.Options": {"target": "Package", "version": "[7.0.1, )"}, "Newtonsoft.Json.Bson": {"target": "Package", "version": "[1.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}