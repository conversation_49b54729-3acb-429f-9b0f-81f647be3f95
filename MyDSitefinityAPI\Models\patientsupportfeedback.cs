﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace MyDSitefinityAPI.Models
{
    [Table("Feedback", Schema = "PatientSupport")]
    public class PatientSupportFeedback
    {

      [Key]
      [Column("Id")]
      public int  Id { get; set; } 
      public int FeedbackType { get; set; } 
      public int PracticeId { get; set; }   
      public int Category { get; set; } 
      public int SubCategory { get; set; }  
      public string FeedbackInformation { get; set; }   
      public string Patient { get; set; }   
      public string Complainant { get; set; }   
      public int PatientType { get; set; }  
      public string RaisedBy { get; set; }
      public DateTime RaisedDate  { get; set; }
      public int Status { get; set; }   
      public bool Deleted { get; set; } 
      public string BriefDescription { get; set; }
      public string LastModifiedBy { get; set; }
      public DateTime LastModifiedOn { get; set; }
    }
}
