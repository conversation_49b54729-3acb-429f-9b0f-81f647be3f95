﻿using Mydentist.MyDSitefinityApi.ClinicianPortalApi;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mydentist.MyDSitefinityAPI.ImplementationServices
{
    public class SharedListMappingService: ISharedListMappingService
    {
        private readonly ISharedListService _sharedListService;

        public SharedListMappingService(ISharedListService sharedListService)
        {
            _sharedListService = sharedListService;
        }

        public async Task<IEnumerable<SharedListItemViewModel>> GetList(string listName)
        {
            return await _sharedListService.GetByListName(listName);
        }
 
    }
}
