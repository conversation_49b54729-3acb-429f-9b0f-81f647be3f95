﻿using Mydentist.MyDSitefinityAPI.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data;
using System.Data.SqlTypes;
using Microsoft.EntityFrameworkCore;
using Microsoft.Data.SqlClient;

namespace Mydentist.MyDSitefinityAPI.Persistence
{
    public class RecruitmentWebsiteFormService: IRecruitmentWebsiteFormService
    {
        private readonly DbContextWarehouse _dbcontextWarehouse;

        public RecruitmentWebsiteFormService(DbContextWarehouse dbcontextWarehouse)
        {
            _dbcontextWarehouse = dbcontextWarehouse;
        }

        public async Task<int> Add(RecruitmentWebsiteFormModel recruitmentWebsiteFormModel)
        {
            recruitmentWebsiteFormModel.CreatedDateTime = DateTime.Now;
            _dbcontextWarehouse.RecruitmentWebsiteForms.Add(recruitmentWebsiteFormModel);
            await _dbcontextWarehouse.SaveChangesAsync();

            return recruitmentWebsiteFormModel.Id; 
        }


    }
}
