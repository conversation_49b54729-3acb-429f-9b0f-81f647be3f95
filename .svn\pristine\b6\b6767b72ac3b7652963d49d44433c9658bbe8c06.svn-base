﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using MyDSitefinityAPI.Models;

namespace MyDSitefinityAPI.IntegrationTests.Tests.Services
{
    [TestClass]
    public class PublicUserAuthenticationServiceTest : Test
    {
        [TestMethod]
        public void Authenticate()
        {
            bool isAuthenticated = PublicUserAuthenticationService.Authenticate(TestPublicUser, TestPassword);

            Assert.IsTrue(isAuthenticated, "User was not authenticated");
        }

        [TestMethod]
        public void GetSaltedUser()
        {
            string testUserHash = PublicUserAuthenticationService.ComputeHash(TestPublicUser.UserId, TestPublicUser.Salt);

            PublicUser? saltedUser = PublicUserAuthenticationService.GetSaltedUser(testUserHash);

            Assert.IsNotNull(saltedUser, "Salted user was null");
            Assert.AreEqual(TestPublicUser.UserId, saltedUser.UserId);
            Assert.AreEqual(TestPublicUser.Salt, saltedUser.Salt);
            Assert.AreEqual(TestPublicUser.Password, saltedUser.Password);
        }

        [TestMethod]
        public void ComputeHash()
        {
            string hash = PublicUserAuthenticationService.ComputeHash(TestPublicUser.UserId, TestPublicUser.Salt);
            Assert.IsNotNull(hash, "Hash is null");
        }

        [TestMethod]
        public void GenerateSalt()
        {
            string salt = PublicUserAuthenticationService.GenerateSalt(8);
            Assert.IsNotNull(salt, "Salt is null");
        }
    }
}
