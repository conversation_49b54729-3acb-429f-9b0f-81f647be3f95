﻿namespace MyDSitefinity
{
    /// <summary>
    ///     A result which is passed as a response which represents whether the request succeeded or failed
    /// </summary>
    public class ResultViewModel
    {
        /// <summary>
        ///     A success message or other object which indicates success
        /// </summary>
        public object? Value { get; set; }

        /// <summary>
        ///     An error message which represents what went wrong
        /// </summary>
        public string? Error { get; set; }

        /// <summary>
        ///     Returns a result which represents a successful request
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static ResultViewModel Success(object value)
        {
            return new ResultViewModel
            {
                Value = value
            };
        }

        /// <summary>
        ///     Returns a result which represents a failed request
        /// </summary>
        /// <param name="error"></param>
        /// <returns></returns>
        public static ResultViewModel Failure(string error)
        {
            return new ResultViewModel
            {
                Error = error
            };
        }
    }
}