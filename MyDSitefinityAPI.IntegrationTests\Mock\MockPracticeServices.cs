using MyDSitefinityAPI.Interfaces;
using MyDSitefinityAPI.Models;

namespace MyDSitefinityAPI.IntegrationTests.Mock
{
    public class MockPracticeServices : IPracticeServices
    {
        public async Task<PracticeEmail> GetPracticeEmailAsync(string practiceId, string type)
        {
            // Return a mock practice email for testing
            return await Task.FromResult(new PracticeEmail
            {
                Email = "<EMAIL>"
            });
        }
    }
}
