﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MyDSitefinityAPI.Models.PracticeDirectory
{
    [Table("v-PerformerAtPracticeDetails", Schema = "dbo")]
    public class Performer
    {
        [Key]
        [Column("GdcNo")]
        public string GdcNumber { get; set; }

        [Column("PerformerName")]
        public string Name { get; set; }

        /// <summary>
        /// A lazy loaded public profile for the current performer
        /// </summary>
        [ForeignKey("GdcNumber")]
        public virtual PerformerProfile PerformerProfile { get; set; }
    }
}
