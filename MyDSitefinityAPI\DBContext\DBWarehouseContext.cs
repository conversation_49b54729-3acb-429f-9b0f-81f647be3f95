﻿using Mydentist.MyDSitefinityAPI.Domain;
using MyDSitefinityAPI.DbConfigurations;
using MyDSitefinityAPI.Interfaces;
using MyDSitefinityAPI.Models;
using MyDSitefinityAPI.Models.PracticeDirectory;
using System.Data.Entity;
using System.Data.Entity.Infrastructure;
using System.Data.Entity.SqlServer;

namespace MyDSitefinityAPI.DBContext
{
    [DbConfigurationType(typeof(DataContextConfiguration))]
    public class DBWarehouseContext : DbContext, IDBWarehouseContext
    {
        public DBWarehouseContext() : base("name=Warehouse")
        {
        }

        public DbSet<sf_quickcontactpmform> sf_quickcontactpmform { get; set; }
        public DbSet<sf_feedbackform> sf_feedbackform { get; set; }
        public DbSet<sf_Emergencydentalappointment> sf_Emergencydentalappointment { get; set; }
        public DbSet<sf_GeneralEnquiry> sf_generalEnquiry { get; set; }

        public DbSet<PatientSupportUpdate> patientsupportupdate { get; set; }
        public DbSet<PatientSupportFeedback> patientsupportfeedback { get; set; }
        public DbSet<PracticeDetails> practiceDetails { get; set; }
        public DbSet<PerformerImage> PerformerImages { get; set; }
        public DbSet<PerformerProfile> PerformerProfiles { get; set; }
        public DbSet<ApplicationLog> ApplicationLog { get; set; }
        public DbSet<WifiForm> WifiForms { get; set; }
        public DbSet<ReferralForm507> ReferralForms507 { get; set; }
        public DbSet<MarketingConsentForm> MarketingConsentForms { get; set; }
        public DbSet<HomePageMarketingConsentForm> HomePageMarketingConsentForms { get; set; }
        public DbSet<Referrals> Referrals { get; set; }

        public DbSet<sf_WebJobApplication> webJobApplications { get; set; }

        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            modelBuilder.Properties<DateTime>()
               .Configure(c => c.HasColumnType("datetime2"));

            modelBuilder.Entity<PracticeDetails>().HasMany(practice => practice.PerformerImages).WithRequired(performerImage => performerImage.Practice);
            modelBuilder.Entity<PerformerImage>().HasRequired(performerImage => performerImage.PerformerProfile).WithMany(performerProfile => performerProfile.Images);
            modelBuilder.Entity<PerformerProfile>().HasRequired(performerProfile => performerProfile.Performer).WithOptional(performer => performer.PerformerProfile);
            modelBuilder.Entity<PracticePerson>().HasOptional(practicePerson => practicePerson.PerformerProfile).WithMany(performerProfile => performerProfile.PracticePeople);
            modelBuilder.Entity<PracticePerson>().HasMany(practicePerson => practicePerson.PracticePersonLinks).WithRequired(practicePersonLink => practicePersonLink.PracticePerson);
            modelBuilder.Entity<PracticePerson>().HasRequired(practicePerson => practicePerson.Role).WithMany(role => role.PracticePeople);
            modelBuilder.Entity<PracticePerson>().HasRequired(practicePerson => practicePerson.Practice).WithMany(practice => practice.PracticePeople);
            modelBuilder.Entity<Role>().HasRequired(role => role.RoleType).WithMany(roleType => roleType.Roles);
        }
    }
}
