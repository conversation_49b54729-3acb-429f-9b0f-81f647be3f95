{"format": 1, "restore": {"C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.IntegrationTests\\MyDSitefinityAPI.IntegrationTests.csproj": {}}, "projects": {"C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityApi.ClinicianPortalApi\\MyDSitefinityApi.ClinicianPortalApi.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityApi.ClinicianPortalApi\\MyDSitefinityApi.ClinicianPortalApi.csproj", "projectName": "Mydentist.MyDSitefinityApi.ClinicianPortalApi", "projectPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityApi.ClinicianPortalApi\\MyDSitefinityApi.ClinicianPortalApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityApi.ClinicianPortalApi\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Extensions.Options": {"target": "Package", "version": "[7.0.1, )"}, "Newtonsoft.Json.Bson": {"target": "Package", "version": "[1.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Domain\\MyDSitefinityAPI.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Domain\\MyDSitefinityAPI.Domain.csproj", "projectName": "Mydentist.MyDSitefinityAPI.Domain", "projectPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Domain\\MyDSitefinityAPI.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.ImplementationServices\\MyDSitefinityAPI.ImplementationServices.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.ImplementationServices\\MyDSitefinityAPI.ImplementationServices.csproj", "projectName": "Mydentist.MyDSitefinityAPI.ImplementationServices", "projectPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.ImplementationServices\\MyDSitefinityAPI.ImplementationServices.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.ImplementationServices\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityApi.ClinicianPortalApi\\MyDSitefinityApi.ClinicianPortalApi.csproj": {"projectPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityApi.ClinicianPortalApi\\MyDSitefinityApi.ClinicianPortalApi.csproj"}, "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Domain\\MyDSitefinityAPI.Domain.csproj": {"projectPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Domain\\MyDSitefinityAPI.Domain.csproj"}, "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Persistence\\MyDSitefinityAPI.Persistence.csproj": {"projectPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Persistence\\MyDSitefinityAPI.Persistence.csproj"}, "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.WebConfigApi\\MyDSitefinityAPI.WebConfigApi.csproj": {"projectPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.WebConfigApi\\MyDSitefinityAPI.WebConfigApi.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.0, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.IntegrationTests\\MyDSitefinityAPI.IntegrationTests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.IntegrationTests\\MyDSitefinityAPI.IntegrationTests.csproj", "projectName": "MyDSitefinityAPI.IntegrationTests", "projectPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.IntegrationTests\\MyDSitefinityAPI.IntegrationTests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.IntegrationTests\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityApi.ClinicianPortalApi\\MyDSitefinityApi.ClinicianPortalApi.csproj": {"projectPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityApi.ClinicianPortalApi\\MyDSitefinityApi.ClinicianPortalApi.csproj"}, "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.ImplementationServices\\MyDSitefinityAPI.ImplementationServices.csproj": {"projectPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.ImplementationServices\\MyDSitefinityAPI.ImplementationServices.csproj"}, "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI\\MyDSitefinityAPI.csproj": {"projectPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI\\MyDSitefinityAPI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"MSTest.TestAdapter": {"target": "Package", "version": "[2.2.10, )"}, "MSTest.TestFramework": {"target": "Package", "version": "[2.2.10, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.4.0, )"}, "Moq": {"target": "Package", "version": "[4.18.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Persistence\\MyDSitefinityAPI.Persistence.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Persistence\\MyDSitefinityAPI.Persistence.csproj", "projectName": "Mydentist.MyDSitefinityAPI.Persistence", "projectPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Persistence\\MyDSitefinityAPI.Persistence.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Persistence\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Domain\\MyDSitefinityAPI.Domain.csproj": {"projectPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Domain\\MyDSitefinityAPI.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"EntityFramework": {"target": "Package", "version": "[6.4.4, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.1.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[7.0.4, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[7.0.3, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[7.0.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.WebConfigApi\\MyDSitefinityAPI.WebConfigApi.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.WebConfigApi\\MyDSitefinityAPI.WebConfigApi.csproj", "projectName": "Mydentist.MyDSitefinityAPI.WebConfigApi", "projectPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.WebConfigApi\\MyDSitefinityAPI.WebConfigApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.WebConfigApi\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Domain\\MyDSitefinityAPI.Domain.csproj": {"projectPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Domain\\MyDSitefinityAPI.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.1.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[7.0.4, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[7.0.4, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Configuration.ConfigurationManager": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI\\MyDSitefinityAPI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI\\MyDSitefinityAPI.csproj", "projectName": "MyDSitefinityAPI", "projectPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI\\MyDSitefinityAPI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.ImplementationServices\\MyDSitefinityAPI.ImplementationServices.csproj": {"projectPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.ImplementationServices\\MyDSitefinityAPI.ImplementationServices.csproj"}, "C:\\Dev\\MyDSitefinityAPI\\SitefinityServiceCaller.Core\\SitefinityServiceCaller.Core.csproj": {"projectPath": "C:\\Dev\\MyDSitefinityAPI\\SitefinityServiceCaller.Core\\SitefinityServiceCaller.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"EntityFramework": {"target": "Package", "version": "[6.4.4, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[7.0.3, )"}, "NLog.Web.AspNetCore": {"target": "Package", "version": "[6.0.2, )"}, "Newtonsoft.Json.Bson": {"target": "Package", "version": "[1.0.2, )"}, "Serilog.Extensions.Logging.File": {"target": "Package", "version": "[3.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.2.3, )"}, "Swashbuckle.AspNetCore.Filters": {"target": "Package", "version": "[7.0.5, )"}, "Swashbuckle.AspNetCore.Swagger": {"target": "Package", "version": "[6.4.0, )"}, "System.ServiceModel.Duplex": {"target": "Package", "version": "[4.10.*, )"}, "System.ServiceModel.Federation": {"target": "Package", "version": "[4.10.*, )"}, "System.ServiceModel.Http": {"target": "Package", "version": "[4.10.*, )"}, "System.ServiceModel.NetTcp": {"target": "Package", "version": "[4.10.*, )"}, "System.ServiceModel.Security": {"target": "Package", "version": "[4.10.*, )"}, "System.Text.Json": {"target": "Package", "version": "[7.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Dev\\MyDSitefinityAPI\\SitefinityServiceCaller.Core\\SitefinityServiceCaller.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\MyDSitefinityAPI\\SitefinityServiceCaller.Core\\SitefinityServiceCaller.Core.csproj", "projectName": "SitefinityServiceCaller.Core", "projectPath": "C:\\Dev\\MyDSitefinityAPI\\SitefinityServiceCaller.Core\\SitefinityServiceCaller.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\MyDSitefinityAPI\\SitefinityServiceCaller.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"System.Configuration.ConfigurationManager": {"target": "Package", "version": "[7.0.0, )"}, "System.ServiceModel.Duplex": {"target": "Package", "version": "[4.8.*, )"}, "System.ServiceModel.Federation": {"target": "Package", "version": "[4.8.*, )"}, "System.ServiceModel.Http": {"target": "Package", "version": "[4.8.*, )"}, "System.ServiceModel.NetTcp": {"target": "Package", "version": "[4.8.*, )"}, "System.ServiceModel.Security": {"target": "Package", "version": "[4.8.*, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}