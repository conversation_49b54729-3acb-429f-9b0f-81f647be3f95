﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MyDSitefinityAPI.Models
{
    [Table("SharedList", Schema = "dbo")]

    public class SharedList
    {
        [Key]
        [Column("Id")]
        public int Id { get; set; }
        public string Name { get; set; }
        public string DisplayValue { get; set; }
        public int? DisplayOrder { get; set; }
        public string ForeignId { get; set; }
        public bool IsActive { get; set; }
    }
}
