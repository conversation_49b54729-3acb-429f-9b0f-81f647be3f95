﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Mydentist.MyDSitefinityAPI.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AdditionalRecruitmentFormFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "FormName",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AddColumn<string>(
                name: "Location",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Role",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "University",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "YearQualified",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                type: "int",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Location",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm");

            migrationBuilder.DropColumn(
                name: "Role",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm");

            migrationBuilder.DropColumn(
                name: "University",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm");

            migrationBuilder.DropColumn(
                name: "YearQualified",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm");

            migrationBuilder.AlterColumn<string>(
                name: "FormName",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);
        }
    }
}
