﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using MyDSitefinityAPI.Models.PracticeDirectory;

namespace MyDSitefinityAPI.Models
{
    [Table("dbo.v-Wholetooth-practiceDetailsIncludingDisposal")]
    public class PracticeDetails
    {
        [Key]
        [Column("Practice Id")]
        public int PracticeID { get; set; }

        [Column("Practice Short Name")]
        public string PracticeName { get; set; }

        [Column("PMEmail")]
        public string PMEmail { get; set; }

        [<PERSON><PERSON><PERSON>("Practice Manager")]
        public string PMName { get; set; }

		[Column("PM Firstname")]
		public string PMFirstname { get; set; }

		[Column("PM Surname")]
		public string PMSurname { get; set; }

		/// <summary>
		/// A lazy-loaded collection of performer images which originated from the current practice
		/// </summary>
		[ForeignKey("PracticeId")]
        public virtual List<PerformerImage> PerformerImages { get; set; }

        [ForeignKey("PracticeId")]
        public virtual List<PracticePerson> PracticePeople { get; set; }
    }
}
