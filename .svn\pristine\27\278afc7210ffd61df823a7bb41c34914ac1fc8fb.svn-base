﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mydentist.MyDSitefinityAPI.Domain
{
    [Table("DesiredRole", Schema = "dbo")]

    public class DesiredRole
    {
        public int DesiredRoleId { get; set; }
        public string Roles { get; set; }
        public int? ChannelId { get; set; }
    }
}
