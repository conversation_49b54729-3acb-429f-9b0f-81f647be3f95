﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Mydentist.MyDSitefinityAPI.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class LongAndLatForRecruitment : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "DesiredWorkingLocation",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AddColumn<float>(
                name: "DesiredWorkingLocationLatitude",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                type: "Decimal(8,7)",
                nullable: true);

            migrationBuilder.AddColumn<float>(
                name: "DesiredWorkingLocationLongitude",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                type: "Decimal(9,7)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DesiredWorkingLocationLatitude",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm");

            migrationBuilder.DropColumn(
                name: "DesiredWorkingLocationLongitude",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm");

            migrationBuilder.AlterColumn<int>(
                name: "DesiredWorkingLocation",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                type: "int",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);
        }
    }
}
