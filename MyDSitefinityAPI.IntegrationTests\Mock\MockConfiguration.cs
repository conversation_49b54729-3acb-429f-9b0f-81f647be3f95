using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Primitives;

namespace MyDSitefinityAPI.IntegrationTests.Mock
{
    public class MockConfiguration : IConfiguration
    {
        private readonly Dictionary<string, string> _data = new Dictionary<string, string>();

        public MockConfiguration()
        {
            // Add some default mock configuration values
            _data["RawJsonLogging"] = "no";
            _data["VacancyApiKey"] = "mock-api-key";
            _data["VacancyApplyApi"] = "https://mock-api.com/apply";
            _data["NearestPractice:BaseUrl"] = "https://mock-practice-api.com";
            _data["NearestPractice:PracticeLocationApiKey"] = "mock-practice-key";
            _data["RecruitmentHub:ApiKey"] = "mock-recruitment-key";
            _data["RecruitmentHub:WebApply"] = "https://mock-recruitment.com/apply";
        }

        public string this[string key] 
        { 
            get => _data.TryGetValue(key, out var value) ? value : null;
            set => _data[key] = value;
        }

        public IEnumerable<IConfigurationSection> GetChildren()
        {
            return new List<IConfigurationSection>();
        }

        public IChangeToken GetReloadToken()
        {
            return new MockChangeToken();
        }

        public IConfigurationSection GetSection(string key)
        {
            return new MockConfigurationSection(key, _data);
        }
    }

    public class MockConfigurationSection : IConfigurationSection
    {
        private readonly string _key;
        private readonly Dictionary<string, string> _data;

        public MockConfigurationSection(string key, Dictionary<string, string> data)
        {
            _key = key;
            _data = data;
        }

        public string this[string key] 
        { 
            get => _data.TryGetValue($"{_key}:{key}", out var value) ? value : _data.TryGetValue(key, out value) ? value : null;
            set => _data[$"{_key}:{key}"] = value;
        }

        public string Key => _key;
        public string Path => _key;
        public string Value 
        { 
            get => _data.TryGetValue(_key, out var value) ? value : null;
            set => _data[_key] = value;
        }

        public IEnumerable<IConfigurationSection> GetChildren()
        {
            return new List<IConfigurationSection>();
        }

        public IChangeToken GetReloadToken()
        {
            return new MockChangeToken();
        }

        public IConfigurationSection GetSection(string key)
        {
            return new MockConfigurationSection($"{_key}:{key}", _data);
        }
    }

    public class MockChangeToken : IChangeToken
    {
        public bool HasChanged => false;
        public bool ActiveChangeCallbacks => false;
        public IDisposable RegisterChangeCallback(Action<object> callback, object state) => null;
    }
}
