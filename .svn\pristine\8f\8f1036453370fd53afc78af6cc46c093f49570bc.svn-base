﻿using MyDSitefinityAPI.ConfigHelpers;

namespace MyDSitefinityAPI.Middleware
{
    public class APIKeyMiddleware
    {

        private readonly RequestDelegate _next;
        const string apiKeyHeaderName = "X-ApiKey";

        public APIKeyMiddleware(RequestDelegate next)
        {
            _next = next;
        }
        public async Task InvokeAsync(HttpContext context)
        {
            var apiKey = ConfigurationHelper.GetValue("APIKey");

            if (!context.Request.Headers.TryGetValue(apiKeyHeaderName, out
                    var extractedApiKey))
            {
                context.Response.StatusCode = 401;
                await context.Response.WriteAsync("Api Key was not provided ");
                return;
            }
            var appSettings = context.RequestServices.GetRequiredService<IConfiguration>();
            if (!apiKey.Equals(extractedApiKey))
            {
                context.Response.StatusCode = 401;
                await context.Response.WriteAsync("Unauthorized client");
                return;
            }
            await _next(context);
        }
    }
}
