﻿using mydentist;
using MyDSitefinityAPI.ConfigHelpers;
using MyDSitefinityAPI.DBContext;
using MyDSitefinityAPI.Interfaces;
using MyDSitefinityAPI.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Net;
using System.Net.Mail;
using System.Text;
using System.Text.Json;
using static System.Net.Mime.MediaTypeNames;


namespace MyDSitefinityAPI.Services
{
    public class VacancyService : IVacancyService
    {
        private ILogger _logger { get; set; }
        private IVacancyDbService _vacancyDbService { get; set; }

        public VacancyService(ILogger logger, IVacancyDbService vacancyDbService)
        {
            _logger = logger;
            _vacancyDbService = vacancyDbService;
        }


        public int IntParser(object? control)
        {
            if(control == null || control?.ToString() == "")
            {
                return 1;
            }
            else
            {
               return int.Parse(control?.ToString());
            }
           
        }

        public string GetRawUsedItems(SitefinityFormV2? formSubmission)
        {
            string gdcNumber = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "TextFieldController_9").FirstOrDefault()?.Value?.ToString();
            string? nogdcReasonText = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "MultipleChoiceFieldController_2").FirstOrDefault()?.Value?.ToString() ?? formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "DonthaveGDCNurse").FirstOrDefault()?.Value?.ToString();
            string? googleClientId = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "GAClientId").FirstOrDefault()?.Value?.ToString();
            string? vacancyId = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "VacancyID").FirstOrDefault()?.Value?.ToString();


         string? countryOfQualificationId = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "VacancyCountryController").FirstOrDefault()?.Value?.ToString();
         string? hasUkDrivingLicense = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "MultipleChoiceFieldController_0").FirstOrDefault()?.Value?.ToString();
         string? currentCountryId = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "CurrentCountry").FirstOrDefault()?.Value?.ToString();
         string? eligibleToWorkInUk = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "MultipleChoiceFieldController").FirstOrDefault()?.Value?.ToString();
         string? visaStatusId = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "VacancyVisaController").FirstOrDefault()?.Value?.ToString();
         string? yearOfQualification = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "TextFieldController_15").FirstOrDefault()?.Value?.ToString();
         string? universityOfQualificationId = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "VacancyUniversityController").FirstOrDefault()?.Value?.ToString();

         string? ableToCommitTo12To18MonthsTrainingProgram = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "MultipleChoiceFieldController_8").FirstOrDefault()?.Value?.ToString();
         string? comfortableAssistingWithDentalProcedures = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "MultipleChoiceFieldController_6").FirstOrDefault()?.Value?.ToString();
         string? comfortableWearingPPE = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "MultipleChoiceFieldController_9").FirstOrDefault()?.Value?.ToString();
         string? comfortableWorkingInaClinicalHealthCareSetting = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "MultipleChoiceFieldController_5").FirstOrDefault()?.Value?.ToString();
         string? comfortableWorkingWithNervousPatients = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "MultipleChoiceFieldController_7").FirstOrDefault()?.Value?.ToString();
         string? hasGradeCOr4InGcseEnglish = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "MultipleChoiceFieldController_4").FirstOrDefault()?.Value?.ToString();
         string? reasonForNoGdcNumberId = nogdcReasonText;
         int? gdcReason = _vacancyDbService.GetSharedListId(nogdcReasonText, "GdcReason");
            return $"\r\nGdcNumber: {gdcNumber}\r\n" +
                   $"googlClientId: {googleClientId}\r\n" +
                   $"gdcReasonId: {gdcReason}\r\n" +
                   $"vacancyId: {vacancyId}\r\n" +
                   $"countryofQualification: {countryOfQualificationId}\r\n" +
                   $"hasUkDrivingLicense: {hasUkDrivingLicense}\r\n" +
                   $"currentCountryId: {currentCountryId}\r\n" +
                   $"eligibletoWorkUk: {eligibleToWorkInUk}\r\n" +
                   $"visaStatus: {visaStatusId}\r\n" +
                   $"yearofQualification: {yearOfQualification}\r\n" +
                   $"universityOfQualification: {universityOfQualificationId}\r\n" +
                   $"abletoCommit12Months: {ableToCommitTo12To18MonthsTrainingProgram}\r\n" +
                   $"comfortableDentalProcedures: {comfortableAssistingWithDentalProcedures}\r\n" +
                   $"comfortablePPE: {comfortableWearingPPE}\r\n" +
                   $"comfortableClinicalSetting: {comfortableWorkingInaClinicalHealthCareSetting}\r\n" +
                   $"comfortableNervousPatient: {comfortableWorkingWithNervousPatients}\r\n" +
                   $"hasGradeCGCSEEnglish: {hasGradeCOr4InGcseEnglish}\r\n" +
                   $"noGdcReason: {nogdcReasonText}\r\n";
        }

        public async Task<HttpStatusCode> ApplyForVacancy(JsonElement json)
        {
            if (ConfigurationHelper.GetValue("RawJsonLogging") == "yes")
            {
                _logger.LogInformation($"(Vacancy Apply) Begin Apply api call with the request body: {json}");
            }
            else
            {
                _logger.LogInformation($"(Vacancy Apply) Begin Apply api call");
            }

            JsonSerializerOptions? options = new JsonSerializerOptions() { PropertyNameCaseInsensitive = true };
            SitefinityFormV2? formSubmission = System.Text.Json.JsonSerializer.Deserialize<SitefinityFormV2>(json, options);

            string? applicationJson = string.Empty;
            string? gdcNumber = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "TextFieldController_9").FirstOrDefault()?.Value?.ToString();
            string? nogdcReasonText = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "MultipleChoiceFieldController_2").FirstOrDefault()?.Value?.ToString()  ?? formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "DonthaveGDCNurse").FirstOrDefault()?.Value?.ToString();
            bool qualified = nogdcReasonText == "I have submitted an application to the GDC" || nogdcReasonText == "I am studying for the ORE" || nogdcReasonText == "I'd like help with the process" || nogdcReasonText == "I am an EEA graduate";
            bool trainee = nogdcReasonText == "I am currently training to be a dental nurse" || nogdcReasonText == "Neither of the above apply to me" || nogdcReasonText == "My GDC number is pending and I have passed my course";

#pragma warning disable CS8601 // Possible null reference assignment.
            VacancyApplicationV2 application = new VacancyApplicationV2()
            {
                googleClientId = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "GAClientId").FirstOrDefault()?.Value?.ToString(),
                vacancyId = int.Parse(formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "VacancyID").FirstOrDefault()?.Value?.ToString()),
                preferredRadius = 25,
                cvUrl =  _vacancyDbService.GetCVUrl(formSubmission),
                whereDidYouHearAboutThisVacancy = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "TextFieldController_8").FirstOrDefault()?.Value?.ToString(),
                applicant = new ApplicantV2()
                {
                    contactNumber = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "TextFieldController_4").FirstOrDefault()?.Value?.ToString(),
                    emailAddress = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "TextFieldController_5").FirstOrDefault()?.Value?.ToString(),
                    firstName = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "TextFieldController_2").FirstOrDefault()?.Value?.ToString(),
                    lastName = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "TextFieldController_3").FirstOrDefault()?.Value?.ToString(),
                    gdcNumber = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "TextFieldController_9").FirstOrDefault()?.Value?.ToString(),
                    postCode = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "TextFieldController_6").FirstOrDefault()?.Value?.ToString(),
                    titleId = null,
                    address = "",
                    countryOfQualificationId = (string.IsNullOrEmpty(gdcNumber) && qualified == true) ? ( _vacancyDbService.GetSharedListId(formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "VacancyCountryController").FirstOrDefault()?.Value?.ToString(), "CountryOfQualification")) : null,
                    hasUkDrivingLicense = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "MultipleChoiceFieldController_0").FirstOrDefault()?.Value?.ToString() == "Yes" ? true : (formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "MultipleChoiceFieldController_0").FirstOrDefault()?.Value == null) ? null : false,
                    currentCountryId = (string.IsNullOrEmpty(gdcNumber) && qualified == true) ? (_vacancyDbService.GetSharedListId(formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "CurrentCountry").FirstOrDefault()?.Value?.ToString(), "CurrentCountry")) : null,
                    eligibleToWorkInUk = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "MultipleChoiceFieldController").FirstOrDefault()?.Value?.ToString() == "Yes" ? true : formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "MultipleChoiceFieldController").FirstOrDefault()?.Value == null ? null : false,
                    linkedInAccount = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "TextFieldController_7").FirstOrDefault()?.Value?.ToString(),
                    visaStatusId = (string.IsNullOrEmpty(gdcNumber) && qualified == true) ? (_vacancyDbService.GetSharedListId(formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "VacancyVisaController").FirstOrDefault()?.Value?.ToString())) : null,
                    yearOfQualification = (string.IsNullOrEmpty(gdcNumber) && qualified == true) ? IntParser(formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "TextFieldController_15").FirstOrDefault()?.Value) : null ,
                    universityOfQualificationId = (string.IsNullOrEmpty(gdcNumber) && qualified == true) ? (_vacancyDbService.GetSharedListId(formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "VacancyUniversityController").FirstOrDefault()?.Value?.ToString(), "University")) : null

                },
                nonGDCQuestionsViewModel = (string.IsNullOrEmpty(gdcNumber) && trainee == true )? new NonGDCQuestionsViewModel()
                {
                    ableToCommitTo12To18MonthsTrainingProgram = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "MultipleChoiceFieldController_8").FirstOrDefault()?.Value?.ToString() == "Yes" ? true : false,
                    comfortableAssistingWithDentalProcedures = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "MultipleChoiceFieldController_6").FirstOrDefault()?.Value?.ToString() == "Yes" ? true : false,
                    comfortableWearingPPE = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "MultipleChoiceFieldController_9").FirstOrDefault()?.Value?.ToString() == "Yes" ? true : false,
                    comfortableWorkingInaClinicalHealthCareSetting = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "MultipleChoiceFieldController_5").FirstOrDefault()?.Value?.ToString() == "Yes" ? true : false ,
                    comfortableWorkingWithNervousPatients = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "MultipleChoiceFieldController_7").FirstOrDefault()?.Value?.ToString() == "Yes" ? true : false,
                    hasGradeCOr4InGcseEnglish = formSubmission.OriginalEvent.Controls.Where(x => x.FieldName == "MultipleChoiceFieldController_4").FirstOrDefault()?.Value?.ToString() == "Yes" ? true : false,
                    reasonForNoGdcNumberId = (_vacancyDbService.GetSharedListId(nogdcReasonText, "GdcReason")) ?? null
                } : (string.IsNullOrEmpty(gdcNumber) && qualified == true)?
                    new NonGDCQuestionsViewModel()
                    {
                        ableToCommitTo12To18MonthsTrainingProgram = null,
                        comfortableAssistingWithDentalProcedures = null,
                        comfortableWearingPPE = null,
                        comfortableWorkingInaClinicalHealthCareSetting = null,
                        comfortableWorkingWithNervousPatients = null,
                        hasGradeCOr4InGcseEnglish = null,
                        reasonForNoGdcNumberId = (_vacancyDbService.GetSharedListId(nogdcReasonText, "GdcReason")) ?? null
                    }
                : null
                    
            };
            applicationJson = JsonConvert.SerializeObject(application, Formatting.Indented);
            
#pragma warning restore CS8601 // Possible null reference assignment.
            if (ConfigurationHelper.GetValue("MapLogging") == "yes")
            {
                try
                {
                    _logger.LogInformation($"(Vacancy Apply) the raw used values are {GetRawUsedItems(formSubmission)}");

                }catch(Exception e)
                {
                    _logger.LogInformation($"(Vacancy Apply) Exception on writing map Log");
                }
                _logger.LogInformation($"(Vacancy Apply) the mapped data to be sent is {applicationJson}");
            }


            HttpClient client = new HttpClient();
            client.DefaultRequestHeaders.Add("xApiKey", ConfigurationHelper.GetValue("VacancyApiKey") );
            var endpoint = ConfigurationHelper.GetValue("VacancyApplyApi");
            HttpContent content = new StringContent(applicationJson.ToString(),Encoding.UTF8,"application/json");
            try
            {
               HttpResponseMessage result = await client.PostAsync(endpoint, content);
               _logger.LogInformation($"(Vacancy Apply) End Apply api call, status code: {result.StatusCode}");
                if(result.StatusCode == HttpStatusCode.OK)
                {
                    try
                    {
                        string data = await result.Content.ReadAsStringAsync();
                        VacancyApplyResponse responsObject = JsonConvert.DeserializeObject<VacancyApplyResponse>(data);
                        _logger.LogInformation($"(Vacancy Apply) Created VacancyResponse object");
                        if (responsObject.Exception != null)
                        {
                            _logger.LogInformation($"(Vacancy Apply) Application was sent successfully but there was an issue with the VacancyApply subprocesses - {responsObject.Exception?.Message}");
                        }
                    }catch(Exception e)
                    {
                        _logger.LogInformation($"(Vacancy Apply) ERROR on reading OK response object: {e.Message}");
                    }

                    await _vacancyDbService.SaveApplicationLog(formSubmission, json,  applicationJson);
                    _logger.LogInformation($"(Vacancy Apply) Saved Application Log");
                }
                else
                {
                    _logger.LogInformation($"(Vacancy Apply) Application Log Not Saved");
                }

               return result.StatusCode;

            }catch(Exception ex) {
                _logger.LogInformation($"(Vacancy Apply) ERROR on Apply api call: {ex.Message}");
            }

            return HttpStatusCode.InternalServerError;
        }
    }
}
