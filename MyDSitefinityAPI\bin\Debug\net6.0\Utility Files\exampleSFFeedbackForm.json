{"Name": "Form is submitted", "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49", "Item": null, "OriginalEvent": {"EntryId": "eea6af9b-cae7-6322-9971-ff0000979a84", "ReferralCode": "155", "UserId": "00000000-0000-0000-0000-000000000000", "Username": "", "IpAddress": null, "SubmissionTime": "2022-10-26T14:27:11.9495433Z", "FormId": "0c62139a-cae7-6322-9971-ff0000979a84", "FormName": "sf_feedback", "FormTitle": "<PERSON><PERSON><PERSON>", "FormSubscriptionListId": "3863139a-cae7-6322-9971-ff0000979a84", "SendConfirmationEmail": false, "Controls": [{"FieldControlName": null, "Id": "be4b9c9a-cae7-6322-9971-ff0000979a84", "SiblingId": "00000000-0000-0000-0000-000000000000", "Text": null, "Type": 2, "Title": "Compliments / complaints form", "FieldName": null, "Value": null, "OldValue": null}, {"FieldControlName": null, "Id": "cb4b9c9a-cae7-6322-9971-ff0000979a84", "SiblingId": "be4b9c9a-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Would you like to leave a compliment or complaint?", "FieldName": "FormMultipleChoice_C011", "Value": "Compliment", "OldValue": null}, {"FieldControlName": null, "Id": "0b66049b-cae7-6322-9971-ff0000979a84", "SiblingId": "cb4b9c9a-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "The practice you visited", "FieldName": "PracticeName", "Value": "446=>{my}dentist, Church Street, Eccles", "OldValue": null}, {"FieldControlName": null, "Id": "a34b9c9a-cae7-6322-9971-ff0000979a84", "SiblingId": "00000000-0000-0000-0000-000000000000", "Text": null, "Type": 0, "Title": "Your first name", "FieldName": "FormTextBox_C007", "Value": "test", "OldValue": null}, {"FieldControlName": null, "Id": "c18afe9a-cae7-6322-9971-ff0000979a84", "SiblingId": "00000000-0000-0000-0000-000000000000", "Text": null, "Type": 0, "Title": "Your last name", "FieldName": "FormTextBox_C007_0", "Value": "tset", "OldValue": null}, {"FieldControlName": null, "Id": "b04b9c9a-cae7-6322-9971-ff0000979a84", "SiblingId": "00000000-0000-0000-0000-000000000000", "Text": null, "Type": 0, "Title": "Your email address", "FieldName": "FormTextBox_C008", "Value": "<EMAIL>", "OldValue": null}, {"FieldControlName": null, "Id": "e44b9c9a-cae7-6322-9971-ff0000979a84", "SiblingId": "00000000-0000-0000-0000-000000000000", "Text": null, "Type": 0, "Title": "Your phone number", "FieldName": "FormTextBox_C014", "Value": "07554143790", "OldValue": null}, {"FieldControlName": null, "Id": "ca58139b-cae7-6322-9971-ff0000979a84", "SiblingId": "de4b9c9a-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Title of your feedback", "FieldName": "FormTextBox", "Value": "test", "OldValue": null}, {"FieldControlName": null, "Id": "814b9c9a-cae7-6322-9971-ff0000979a84", "SiblingId": "ca58139b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Your feedback", "FieldName": "FormParagraphTextBox_C004", "Value": "this is my compliment comment", "OldValue": null}], "Origin": null}, "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"}