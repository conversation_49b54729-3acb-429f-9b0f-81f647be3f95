﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AssemblyName>Mydentist.$(MSBuildProjectName)</AssemblyName>
    <RootNamespace>Mydentist.$(MSBuildProjectName.Replace(" ", "_"))</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\MyDSitefinityApi.ClinicianPortalApi\MyDSitefinityApi.ClinicianPortalApi.csproj" />
    <ProjectReference Include="..\MyDSitefinityAPI.Domain\MyDSitefinityAPI.Domain.csproj" />
    <ProjectReference Include="..\MyDSitefinityAPI.Persistence\MyDSitefinityAPI.Persistence.csproj" />
    <ProjectReference Include="..\MyDSitefinityAPI.WebConfigApi\MyDSitefinityAPI.WebConfigApi.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
  </ItemGroup>

</Project>
