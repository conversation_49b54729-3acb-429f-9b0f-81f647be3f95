﻿using Swashbuckle.AspNetCore.Filters;

namespace MyDSitefinityAPI.Models
{
    public class SampleSitefinityForm : IExamplesProvider<SitefinityForm>
    {
        public SitefinityForm GetExamples()
        {

            return new SitefinityForm()
            {
                Name = "mydestist Developer",
                Item = null,
                SiteId = "23",
                OriginalEventType = "Testing",
                OriginalEvent = new FormEvent()
                {
                    EntryId = "cbc0d3b7-1018-475f-be5c-896d5b925c5d",
                    ReferralCode = "2",
                    UserId = "c824f8d5-83fe-478e-8ed0-ade5c30bc884",
                    Username = "mydentistadmin",
                    IpAddress = "127.0.0.1",
                    SubmissionTime = DateTime.Now,
                    FormId = "9289ac17-1a2f-4251-9c11-0ec298b8c45e",
                    FormName = "sf_quickcontactpmform",
                    FormTitle = "TestForm",
                    FormSubscriptionListId = "00000000-0000-0000-0000-000000000000",
                    SendConfirmationEmail = false,
                    Controls = new Control[]
                    {
                        new Control()
                        {
                            FieldControlName = "something",
                            Id = "ab1b06cc-1d4d-40fd-8244-503db911153f",
                            SiblingId = "00000000-0000-0000-0000-000000000000",
                            Text = "Something",
                            Type = 0,
                            Title = "Random",
                            FieldName = "FormTextBox_C004",
                            Value = "<EMAIL>",
                            OldValue = "something"
                        },
                        new Control()
                        {
                            FieldControlName = null,
                            Id = "ab1b06cc-1d4d-40fd-8244-503db911153f",
                            SiblingId = "00000000-0000-0000-0000-000000000000",
                            Text = null,
                            Type = 0,
                            Title = "Random",
                            FieldName = "FormTextBox_C003",
                            Value = "07737477179",
                            OldValue = null
                        },
                        new Control()
                        {
                            FieldControlName = null,
                            Id = "ab1b06cc-1d4d-40fd-8244-503db911153f",
                            SiblingId = "00000000-0000-0000-0000-000000000000",
                            Text = null,
                            Type = 0,
                            Title = "Random",
                            FieldName = "FormTextBox_C001",
                            Value = null,
                            OldValue = null
                        },
                        new Control()
                        {
                            FieldControlName = null,
                            Id = "ab1b06cc-1d4d-40fd-8244-503db911153f",
                            SiblingId = "00000000-0000-0000-0000-000000000000",
                            Text = null,
                            Type = 0,
                            Title = "Random",
                            FieldName = "FormTextBox",
                            Value = "General_Enquiry",
                            OldValue = null

                        },
                        new Control()
                        {
                            FieldControlName = null,
                            Id = "ab1b06cc-1d4d-40fd-8244-503db911153f",
                            SiblingId = "00000000-0000-0000-0000-000000000000",
                            Text = null,
                            Type = 0,
                            Title = "Random",
                            FieldName = "HiddenPracticeId",
                            Value = "240=>{my}dentist, Queensway, Bognor Regis",
                            OldValue = null
                        },
                        new Control()
                        {
                            FieldControlName = null,
                            Id = "ab1b06cc-1d4d-40fd-8244-503db911153f",
                            SiblingId = "00000000-0000-0000-0000-000000000000",
                            Text = null,
                            Type = 0,
                            Title = "Random",
                            FieldName = "FormTextBox_C011",
                            Value = "Tambo 2",
                            OldValue = null
                        },
                        new Control()
                        {
                            FieldControlName= null,
                            Id= "ab1b06cc-1d4d-40fd-8244-503db911153f",
                            SiblingId= "00000000-0000-0000-0000-000000000000",
                            Text= null,
                            Type= 0,
                            Title= "Random",
                            FieldName= "FormTextBox_C010",
                            Value= "Another test",
                            OldValue= null
                        },
                        new Control()
                        {
                            FieldControlName= null,
                            Id= "ab1b06cc-1d4d-40fd-8244-503db911153f",
                            SiblingId= "00000000-0000-0000-0000-000000000000",
                            Text= null,
                            Type= 0,
                            Title= "Random",
                            FieldName= "FormTextBox_C015",
                            Value= "20/07/2003",
                            OldValue= null
                        },
                        new Control()
                        {
                            FieldControlName= null,
                            Id= "ab1b06cc-1d4d-40fd-8244-503db911153f",
                            SiblingId= "00000000-0000-0000-0000-000000000000",
                            Text= null,
                            Type= 0,
                            Title= "Random",
                            FieldName= "GAClientId",
                            Value= "**********.**********",
                            OldValue= null
                        }
                    }

                }

            };
        }

    }

    public class RecruitmentSampleSitefinityForm : IExamplesProvider<SitefinityForm>
    {
        public SitefinityForm GetExamples()
        {
            return new SitefinityForm()
            {
                Name = "mydestist Developer",
                Item = null,
                SiteId = "23",
                OriginalEventType = "Testing",
                OriginalEvent = new FormEvent()
                {
                    EntryId = "cbc0d3b7-1018-475f-be5c-896d5b925c5d",
                    ReferralCode = "2",
                    UserId = "c824f8d5-83fe-478e-8ed0-ade5c30bc884",
                    Username = "mydentistadmin",
                    IpAddress = "127.0.0.1",
                    SubmissionTime = DateTime.Now,
                    FormId = "9289ac17-1a2f-4251-9c11-0ec298b8c45e",
                    FormName = "sf_recruitmentforms",
                    FormTitle = "TestForm",
                    FormSubscriptionListId = "00000000-0000-0000-0000-000000000000",
                    SendConfirmationEmail = false,
                    Controls = new Control[]
                    {
                        new Control()
                        {
                            FieldControlName = "something",
                            Id = "ab1b06cc-1d4d-40fd-8244-503db911153f",
                            SiblingId = "00000000-0000-0000-0000-000000000000",
                            Text = "Something",
                            Type = 0,
                            Title = "Email",
                            FieldName = "FormTextBox_C004",
                            Value = "<EMAIL>",
                            OldValue = "something"
                        },
                        new Control()
                        {
                            FieldControlName = null,
                            Id = "ab1b06cc-1d4d-40fd-8244-503db911153f",
                            SiblingId = "00000000-0000-0000-0000-000000000000",
                            Text = null,
                            Type = 0,
                            Title = "Their Contact Number(s)",
                            FieldName = "FormTextBox_C003",
                            Value = "07737477179",
                            OldValue = null
                        },
                        new Control()
                        {
                            FieldControlName = null,
                            Id = "ab1b06cc-1d4d-40fd-8244-503db911153f",
                            SiblingId = "00000000-0000-0000-0000-000000000000",
                            Text = null,
                            Type = 0,
                            Title = "First Name",
                            FieldName = "FormTextBox_C001",
                            Value = "First Name",
                            OldValue = null
                        },
                        new Control()
                        {
                            FieldControlName = null,
                            Id = "ab1b06cc-1d4d-40fd-8244-503db911153f",
                            SiblingId = "00000000-0000-0000-0000-000000000000",
                            Text = null,
                            Type = 0,
                            Title = "Last Name",
                            FieldName = "FormTextBox",
                            Value = "Last Name",
                            OldValue = null

                        },
                        new Control()
                        {
                            FieldControlName = null,
                            Id = "ab1b06cc-1d4d-40fd-8244-503db911153f",
                            SiblingId = "00000000-0000-0000-0000-000000000000",
                            Text = null,
                            Type = 0,
                            Title = "Form Name",
                            FieldName = "HiddenPracticeId",
                            Value = "Resourcing - Australia competition",
                            OldValue = null
                        },
                        new Control()
                        {
                            FieldControlName = null,
                            Id = "ab1b06cc-1d4d-40fd-8244-503db911153f",
                            SiblingId = "00000000-0000-0000-0000-000000000000",
                            Text = null,
                            Type = 0,
                            Title = "Post code",
                            FieldName = "FormTextBox_C011",
                            Value = "M26 1GG",
                            OldValue = null
                        },
                        new Control()
                        {
                            FieldControlName= null,
                            Id= "ab1b06cc-1d4d-40fd-8244-503db911153f",
                            SiblingId= "00000000-0000-0000-0000-000000000000",
                            Text= null,
                            Type= 0,
                            Title= "University",
                            FieldName= "FormTextBox_C010",
                            Value= "University of Manchester",
                            OldValue= null
                        },
                        new Control()
                        {
                            FieldControlName= null,
                            Id= "ab1b06cc-1d4d-40fd-8244-503db911153f",
                            SiblingId= "00000000-0000-0000-0000-000000000000",
                            Text= null,
                            Type= 0,
                            Title= "Graduation / expected graduation year",
                            FieldName= "FormTextBox_C015",
                            Value= "20/07/2003",
                            OldValue= null
                        },
                        new Control()
                        {
                            FieldControlName= null,
                            Id= "ab1b06cc-1d4d-40fd-8244-503db911153f",
                            SiblingId= "00000000-0000-0000-0000-000000000000",
                            Text= null,
                            Type= 0,
                            Title= "GAClientId",
                            FieldName= "GAClientId",
                            Value= "**********.**********",
                            OldValue= null
                        },
                        new Control()
                        {
                            FieldControlName= null,
                            Id= "ab1b06cc-1d4d-40fd-8244-503db911153f",
                            SiblingId= "00000000-0000-0000-0000-000000000000",
                            Text= null,
                            Type= 0,
                            Title= "Their desired working location in the UK",
                            FieldName= "FormTextBox_C015",
                            Value= "England North West",
                            OldValue= null
                        },
                        new Control()
                        {
                            FieldControlName= null,
                            Id= "ab1b06cc-1d4d-40fd-8244-503db911153f",
                            SiblingId= "00000000-0000-0000-0000-000000000000",
                            Text= null,
                            Type= 0,
                            Title= "Job role interested in?",
                            FieldName= "FormTextBox_C015",
                            Value= "Dentist",
                            OldValue= null
                        },
                        new Control()
                        {
                            FieldControlName= null,
                            Id= "ab1b06cc-1d4d-40fd-8244-503db911153f",
                            SiblingId= "00000000-0000-0000-0000-000000000000",
                            Text= null,
                            Type= 0,
                            Title= "Gdc Number",
                            FieldName= "FormTextBox_C015",
                            Value= "123654",
                            OldValue= null
                        },
                        new Control()
                        {
                            FieldControlName= null,
                            Id= "ab1b06cc-1d4d-40fd-8244-503db911153f",
                            SiblingId= "00000000-0000-0000-0000-000000000000",
                            Text= null,
                            Type= 0,
                            Title= "Contact Number",
                            FieldName= "FormTextBox_C015",
                            Value= "07777777777",
                            OldValue= null
                        },
                        new Control()
                        {
                            FieldControlName= null,
                            Id= "ab1b06cc-1d4d-40fd-8244-503db911153f",
                            SiblingId= "00000000-0000-0000-0000-000000000000",
                            Text= null,
                            Type= 0,
                            Title= "Who are you referring? (Full name)",
                            FieldName= "FormTextBox_C015",
                            Value= "Person Referred",
                            OldValue= null
                        },
                        new Control()
                        {
                            FieldControlName= null,
                            Id= "ab1b06cc-1d4d-40fd-8244-503db911153f",
                            SiblingId= "00000000-0000-0000-0000-000000000000",
                            Text= null,
                            Type= 0,
                            Title= "Their GDC Number",
                            FieldName= "FormTextBox_C015",
                            Value= "00123654",
                            OldValue= null
                        },
                        new Control()
                        {
                            FieldControlName= null,
                            Id= "ab1b06cc-1d4d-40fd-8244-503db911153f",
                            SiblingId= "00000000-0000-0000-0000-000000000000",
                            Text= null,
                            Type= 0,
                            Title= "Their Email Address:",
                            FieldName= "FormTextBox_C015",
                            Value= "<EMAIL>",
                            OldValue= null
                        }
                    }
                }
            };
        }

    }
}

