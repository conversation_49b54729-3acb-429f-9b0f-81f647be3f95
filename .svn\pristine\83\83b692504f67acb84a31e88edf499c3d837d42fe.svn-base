﻿namespace MyDSitefinityAPI.Models
{
    public class GeneralInquiryEmailDataModel
    {
        public string FirstName { get; set; }   
        public string LastName { get; set; }    
        public string PhoneNumber { get; set; }
        public string DOB {  get; set; }
        public string Email { get; set; }
        public string PatientPostcode { get; set; }
        public string PracticeId { get; set; }

        public string ClosestPractice { get; set; }
        public List<string> TreamentsInterestedIn {  get; set; }

    }
}
