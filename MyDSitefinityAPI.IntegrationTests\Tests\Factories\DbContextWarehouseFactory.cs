﻿using Microsoft.EntityFrameworkCore;
using Mydentist.MyDSitefinityAPI.Persistence;

namespace MyDSitefinityAPI.IntegrationTests.Tests.Factories
{
    public class DbContextWarehouseFactory
    {

        public static DbContextOptions<DbContextWarehouse> Get()
        {
            IConfiguration configuration = new ConfigurationBuilder()
                  .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
                  .AddEnvironmentVariables()
                  .Build();

            var builder = new DbContextOptionsBuilder<DbContextWarehouse>();
            Configure(
                builder,
                configuration.GetConnectionString("DbContextWarehouse"));

            return builder.Options;
        }

        public static void Configure(
            DbContextOptionsBuilder<DbContextWarehouse> builder,
            string connectionString)
        {
            builder.UseSqlServer(connectionString);
        }

    }
}
