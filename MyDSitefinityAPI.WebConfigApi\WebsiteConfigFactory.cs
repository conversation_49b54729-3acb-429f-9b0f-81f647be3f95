﻿using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Mydentist.MyDSitefinityAPI.Domain;
using Mydentist.MyDSitefinityAPI.WebConfigApi;
using System.Data.SqlTypes;
using Microsoft.EntityFrameworkCore;
using Microsoft.Data.SqlClient;

namespace Mydentist.MyDSitefinityAPI.WebConfigApi
{
    public class WebsiteConfigFactory: IWebsiteConfigFactory
    {
        private WebsiteConfigDbContext _websiteConfigDbContext;

        public WebsiteConfigFactory(WebsiteConfigDbContext websiteConfigDbContext)
        {
            _websiteConfigDbContext = websiteConfigDbContext;
        }

        public IQueryable<RolePostgraduateLookup> GetRolePostgraduates()
        {
            return _websiteConfigDbContext.RolePostgraduateLookups.AsQueryable();
        }
    }
}
