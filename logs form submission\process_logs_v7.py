

import json
import os
import re

def find_json_payloads(content):
    """
    Finds and extracts complete JSON objects that follow the specific marker string.
    It works by counting curly braces to correctly handle nested structures.
    """
    payloads = []
    marker = 'The raw json at endpoint is:'
    start_indices = [m.end() for m in re.finditer(re.escape(marker), content)]

    for start_index in start_indices:
        first_brace_index = content.find('{', start_index)
        if first_brace_index == -1:
            continue

        open_braces = 1
        for i in range(first_brace_index + 1, len(content)):
            char = content[i]
            if char == '{':
                open_braces += 1
            elif char == '}':
                open_braces -= 1

            if open_braces == 0:
                payload_str = content[first_brace_index : i + 1]
                payloads.append(payload_str)
                break
    return payloads

def main():
    log_dir = r'C:\Dev\MyDSitefinityAPI\logs form submission'
    unique_events = []
    seen_form_signatures = set()

    log_files = sorted([f for f in os.listdir(log_dir) if f.startswith('log-') and f.endswith('.txt')])

    for log_file in log_files:
        file_path = os.path.join(log_dir, log_file)
        print(f"Processing {log_file}...\r", end="")
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
        except Exception as e:
            print(f"\nError reading file {log_file}: {e}")
            continue

        json_payloads = find_json_payloads(content)

        for payload_str in json_payloads:
            try:
                outer_payload = json.loads(payload_str)
                inner_json_str = outer_payload.get("Item", {}).get("ParagraphTextFieldController")

                if not inner_json_str or not isinstance(inner_json_str, str):
                    continue

                inner_payload = json.loads(inner_json_str)
                job_questions = inner_payload.get("jobQuestions")

                if not job_questions or not isinstance(job_questions, list):
                    continue

                # Create a signature based on the Question IDs
                question_ids = sorted([q.get('QuestionId') for q in job_questions if q.get('QuestionId')])
                form_signature = tuple(question_ids)

                if form_signature not in seen_form_signatures:
                    # If we haven't seen this form structure before, add the original payload to our list
                    unique_events.append(outer_payload)
                    seen_form_signatures.add(form_signature)

            except (json.JSONDecodeError, AttributeError, TypeError):
                continue

    output_file = os.path.join(log_dir, 'unique_events_by_form.json')
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(unique_events, f, indent=2)

    print(f"\nProcessed {len(log_files)} log files.")
    print(f"Found {len(unique_events)} unique form types.")
    print(f"Output written to {output_file}")

if __name__ == "__main__":
    main()

