﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Net;
using System.Net.Http;
using System.ServiceModel;
using System.ServiceModel.Channels;
using System.ServiceModel.Description;
using System.Text;
using IDHGroup.SharedLibraries.Logging;
using IDHGroup.SharedLibraries.SitefinityServiceCaller.Authentication;
using IDHGroup.SharedLibraries.SitefinityServiceCaller.Exceptions;
using IDHGroup.SharedLibraries.SitefinityServiceCaller.Models;
using IDHGroup.SharedLibraries.SitefinityServiceCaller.mydentist;
using Newtonsoft.Json;

namespace IDHGroup.SharedLibraries.SitefinityServiceCaller
{
    public class WebserviceCaller
    {

        private const string CookieName = "user_session";
        private static string _grant_type = "password";
        private static string _accesstoken;
        private static string _client_id = "mydentistimport";
        private static string _client_secret = "gKA9ruxfgX";
        private static string username = ConfigurationManager.AppSettings["sitefinityUsername"];
        private static string password = ConfigurationManager.AppSettings["sitefinityPassword"];
        private const string SitefinityAddress = "https://mydentist-stg.sitefinity.cloud/";

        private static mydentistcloudImportSoapClient GenerateSecureWebService()
        {
            WebRequest.DefaultWebProxy = new WebProxy();

            ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3 | SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;

            mydentistcloudImportSoapClient secureWebService = new mydentistcloudImportSoapClient();

            return secureWebService;
        }

        private static HttpClient GetClient()
        {
            var client = new HttpClient()
            {
                BaseAddress = new Uri(SitefinityAddress),
            };

            return client;
        }

        private static string GetToken()
        {
            using (var client = GetClient())
            {
                client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", Convert.ToBase64String(Encoding.GetEncoding("UTF-8").GetBytes($"{_client_id}:{_client_secret}")));
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, $"{SitefinityAddress}/sitefinity/oauth/token")
                {
                    Content = new StringContent($"grant_type={_grant_type}&username={username}&client_id={_client_id}&client_secret={_client_secret}&password={password}", Encoding.UTF8, "application/x-www-form-urlencoded"),
                };
                var response = client.SendAsync(requestMessage).Result;
                response.EnsureSuccessStatusCode();
                var responseBody = response.Content.ReadAsStringAsync().Result;
                TokenResponse tmp = JsonConvert.DeserializeObject<TokenResponse>(responseBody);
                _accesstoken = tmp.access_token;

            }
            return _accesstoken;
        }


        /// <summary>
        ///     Deletes the specified case study for a practice
        /// </summary>
        /// <param name="casestudyId">The unique identifier for the case study, as it appears on Wisdom</param>
        /// <param name="practiceId">The unique identifier for the practice</param>
        /// <exception cref="NotSupportedException">The method has been called simultaneously on multiple threads</exception>
        /// <exception cref="SitefinityAuthenticationException">The web service failed to authenticate</exception>
        /// <exception cref="SitefinityWebserviceException">The web service returned an error</exception>
        /// <exception cref="TimeoutException">The web service request took too long and timed out</exception>
        /// <exception cref="FaultException">The web service reported a fault while executing the Endpoint action</exception>
        /// <exception cref="CommunicationException">An error occured while communicating with the web service</exception>
        /// <exception cref="WebException">
        ///     The web service did not return a properly formed response, the image's file path was invalid or an error occurred
        ///     while downloading the image contents
        /// </exception>
        public void CreateCasestudy(WebCaseStudy webCase, string PracticeId, List<WebCaseStudyImage> webCaseStudyImages)
        {
            CallWebServiceCloud(webService => webService.CreateCasetudyCloud(webCase, PracticeId, webCaseStudyImages));
        }

        /// <summary>
        ///     Deletes the specified case study for a practice
        /// </summary>
        /// <param name="casestudyId">The unique identifier for the case study, as it appears on Wisdom</param>
        /// <param name="practiceId">The unique identifier for the practice</param>
        /// <exception cref="NotSupportedException">The method has been called simultaneously on multiple threads</exception>
        /// <exception cref="SitefinityAuthenticationException">The web service failed to authenticate</exception>
        /// <exception cref="SitefinityWebserviceException">The web service returned an error</exception>
        /// <exception cref="TimeoutException">The web service request took too long and timed out</exception>
        /// <exception cref="FaultException">The web service reported a fault while executing the Endpoint action</exception>
        /// <exception cref="CommunicationException">An error occured while communicating with the web service</exception>
        /// <exception cref="WebException">
        ///     The web service did not return a properly formed response, the image's file path was invalid or an error occurred
        ///     while downloading the image contents
        /// </exception>
        public void DeleteCasestudy(string casestudyId, string practiceId)
        {
            CallWebServiceCloud(webService => webService.DeleteCasestudy(casestudyId, practiceId));
        }

        /// <summary>
        ///     Deletes the relationship between a clinician and their image
        /// </summary>
        /// <param name="gdcNo">The GDC (General dental council) number for a clinician</param>
        /// <param name="practiceId">The unique identifier for the practice</param>
        /// <exception cref="NotSupportedException">The method has been called simultaneously on multiple threads</exception>
        /// <exception cref="SitefinityAuthenticationException">The web service failed to authenticate</exception>
        /// <exception cref="SitefinityWebserviceException">The web service returned an error</exception>
        /// <exception cref="TimeoutException">The web service request took too long and timed out</exception>
        /// <exception cref="FaultException">The web service reported a fault while executing the Endpoint action</exception>
        /// <exception cref="CommunicationException">An error occured while communicating with the web service</exception>
        /// <exception cref="WebException">
        ///     The web service did not return a properly formed response, the image's file path was invalid or an error occurred
        ///     while downloading the image contents
        /// </exception>
        public void DeleteClinicianImageRelation(string gdcNo, string practiceId)
        {
            CallWebServiceCloud(webService => webService.DeleteClinicianImageRelationCloud(practiceId, gdcNo));
        }

        /// <summary>
        ///     Updates the "about us" text for the specified practice
        /// </summary>
        /// <param name="practiceId">The unique identifier for the practice</param>
        /// <exception cref="NotSupportedException">The method has been called simultaneously on multiple threads</exception>
        /// <exception cref="SitefinityAuthenticationException">The web service failed to authenticate</exception>
        /// <exception cref="SitefinityWebserviceException">The web service returned an error</exception>
        /// <exception cref="TimeoutException">The web service request took too long and timed out</exception>
        /// <exception cref="FaultException">The web service reported a fault while executing the Endpoint action</exception>
        /// <exception cref="CommunicationException">An error occured while communicating with the web service</exception>
        /// <exception cref="WebException">
        ///     The web service did not return a properly formed response, the image's file path was invalid or an error occurred
        ///     while downloading the image contents
        /// </exception>
        public void UpdateAboutus(string practiceId, string Aboutus)
        {
            CallWebServiceCloud(webService => webService.Aboutuscloud(practiceId, Aboutus));
        }

        /// <summary>
        ///     Updates the clinician for the specified practice using the requested status
        /// </summary>
        /// <param name="practiceId">The unique identifier for the practice</param>
        /// <param name="gdcNo">The GDC (General dental council) number for a clinician</param>
        /// <param name="status">
        ///     The status which we'll be setting for the clinician. The supported statuses are "Update", "Delete"
        ///     and "Bio"
        /// </param>
        /// <exception cref="NotSupportedException">The method has been called simultaneously on multiple threads</exception>
        /// <exception cref="SitefinityAuthenticationException">The web service failed to authenticate</exception>
        /// <exception cref="SitefinityWebserviceException">The web service returned an error</exception>
        /// <exception cref="TimeoutException">The web service request took too long and timed out</exception>
        /// <exception cref="FaultException">The web service reported a fault while executing the Endpoint action</exception>
        /// <exception cref="CommunicationException">An error occured while communicating with the web service</exception>
        /// <exception cref="WebException">
        ///     The web service did not return a properly formed response, the image's file path was invalid or an error occurred
        ///     while downloading the image contents
        /// </exception>
        public void UpdateClinician(WebClinician webclinician,WebClinicianImage webClinicianImages, string status)
        {
           CallWebServiceCloud(webService => webService.UpdateClinicianCloud(webclinician, webClinicianImages, status));
        }

        /// <summary>
        ///     Updates the opening hours for the specified practice
        /// </summary>
        /// <param name="practiceId">The unique identifier for the practice</param>
        /// <exception cref="NotSupportedException">The method has been called simultaneously on multiple threads</exception>
        /// <exception cref="SitefinityAuthenticationException">The web service failed to authenticate</exception>
        /// <exception cref="SitefinityWebserviceException">The web service returned an error</exception>
        /// <exception cref="TimeoutException">The web service request took too long and timed out</exception>
        /// <exception cref="FaultException">The web service reported a fault while executing the Endpoint action</exception>
        /// <exception cref="CommunicationException">An error occured while communicating with the web service</exception>
        /// <exception cref="WebException">
        ///     The web service did not return a properly formed response, the image's file path was invalid or an error occurred
        ///     while downloading the image contents
        /// </exception>
        public void UpdateOpeninghrs(string practiceId,List<WebOpeningHour> webOpeningHours)
        {
            CallWebServiceCloud(webservice => webservice.UpdateOpeninghrsCloud(practiceId, webOpeningHours));
        }

        /// <summary>
        ///     Updates the basic details for the specified practice
        /// </summary>
        /// <param name="practiceId">The unique identifier for the practice</param>
        /// <exception cref="NotSupportedException">The method has been called simultaneously on multiple threads</exception>
        /// <exception cref="SitefinityAuthenticationException">The web service failed to authenticate</exception>
        /// <exception cref="SitefinityWebserviceException">The web service returned an error</exception>
        /// <exception cref="TimeoutException">The web service request took too long and timed out</exception>
        /// <exception cref="FaultException">The web service reported a fault while executing the Endpoint action</exception>
        /// <exception cref="CommunicationException">An error occured while communicating with the web service</exception>
        /// <exception cref="WebException">
        ///     The web service did not return a properly formed response, the image's file path was invalid or an error occurred
        ///     while downloading the image contents
        /// </exception>
        public void UpdatePracticeTitle(WebPractice webPractice)
        {
            CallWebServiceCloud(webService => webService.UpdateTitleCloud(webPractice));
        }

        /// <summary>
        ///     Downloads the contents of the specified image and re-uploads it to Sitefinity
        /// </summary>
        /// <param name="image">The image which needs to be uploaded to Sitefinity</param>
        /// <exception cref="WebException">
        ///     The image's file path was invalid, an error occured while downloading the image or an
        ///     error occurred while uploading data
        /// </exception>
        /// <exception cref="NotSupportedException">The method has been called simultaneously on multiple threads</exception>
        /// <exception cref="SitefinityAuthenticationException">The web service failed to authenticate</exception>
        /// <exception cref="SitefinityWebserviceException">The web service returned an error</exception>
        /// <exception cref="TimeoutException">The web service request took too long and timed out</exception>
        /// <exception cref="FaultException">The web service reported a fault while executing the Endpoint action</exception>
        /// <exception cref="CommunicationException">An error occured while communicating with the web service</exception>
        /// <exception cref="WebException">
        ///     The web service did not return a properly formed response, the image's file path was invalid or an error occurred
        ///     while downloading the image contents
        /// </exception>
        public void UploadFile(IImage image)
        {
            byte[] response = DownloadFile($"http://{image.Filepath}");

            CallWebServiceCloud(webservice => webservice
                .UploadAlbumImageCloud(
                    image.ImageId,
                    image.Filename,
                    response,
                    image.ImageGallery,
                    image.Fileextension
                ));
        }

        /// <summary>
        ///     Calls a speficied action on the web service and logs exceptions
        /// </summary>
        /// <param name="webserviceAction">An action which should be requested from the Sitefinity web service</param>
        /// <exception cref="SitefinityAuthenticationException">The web service failed to authenticate</exception>
        /// <exception cref="SitefinityWebserviceException">The web service returned an error</exception>
        /// <exception cref="TimeoutException">The web service request took too long and timed out</exception>
        /// <exception cref="FaultException">The web service reported a fault while executing the Endpoint action</exception>
        /// <exception cref="CommunicationException">An error occured while communicating with the web service</exception>
        /// <exception cref="WebException">The web service did not return a properly formed response</exception>
      
        /// <summary>
        ///     Downloads a file from the specified path and returns the bytes
        /// </summary>
        /// <param name="filePath">The path containing the file</param>
        /// <exception cref="WebException">The filePath is invalid or an error occurred while downloading data</exception>
        /// <exception cref="NotSupportedException">The method has been called simultaneously on multiple threads</exception>
        /// <exception cref="ArgumentNullException">The filePath parameter is null</exception>
        /// <returns></returns>
        private byte[] DownloadFile(string filePath)
        {
            try
            {
                using (WebClient webclient = new WebClient())
                {
                    return webclient.DownloadData(filePath);
                }
            }
            catch (ArgumentNullException argumentNullException)
            {
                Log.Error(ConfigurationManager.AppSettings["ApplicationName"], "Attempted to download from a null url", argumentNullException);
                throw;
            }
            catch (NotSupportedException notSupportedException)
            {
                Log.Error(ConfigurationManager.AppSettings["ApplicationName"], $"Unsupported action occured while downloading image at file path {filePath}",
                    notSupportedException);
                throw;
            }
            catch (WebException webException)
            {
                Log.Error(ConfigurationManager.AppSettings["ApplicationName"], $"Web exception occured while downloading image at file path {filePath}", webException);
                throw;
            }
        }

        /// <summary>
        ///     Gets an instance of the web service that will transmit data to a service endpoint securely
        /// </summary>
        /// <returns></returns>
        /// 
        public void CallWebServiceCloud(Func<mydentistcloudImportSoapClient, Result> webserviceAction)
        {
            try
            {
                 var httpRequestProperty = new HttpRequestMessageProperty();

                httpRequestProperty.Headers[System.Net.HttpRequestHeader.Authorization] = "Bearer " + GetToken();

                using (OperationContextScope scope = new OperationContextScope(SecureWebService.InnerChannel))
                {
                    OperationContext.Current.OutgoingMessageProperties[HttpRequestMessageProperty.Name] = httpRequestProperty;

                    webserviceAction.Invoke(SecureWebService);
                }

            }
            catch (SitefinityAuthenticationException ex)
            {
                Log.Error(ConfigurationManager.AppSettings["ApplicationName"], "Error occured while authenticating to Sitefinity", ex);
                throw;
            }
            catch (TimeoutException ex)
            {
                Log.Error(ConfigurationManager.AppSettings["ApplicationName"], "Timed out while waiting for a response from Sitefinity web service", ex);
                throw;
            }
            catch (FaultException ex)
            {
                Log.Error(ConfigurationManager.AppSettings["ApplicationName"], "Fault thrown by Sitefinity. Possible exception returned", ex);
                throw;
            }
            catch (CommunicationException ex)
            {
                Log.Error(ConfigurationManager.AppSettings["ApplicationName"], "Communication exception thrown by Sitefinity web service", ex);
                throw;
            }
            catch (WebException ex)
            {
                Log.Error(ConfigurationManager.AppSettings["ApplicationName"], "Web exception caught while calling the Sitefinity web service", ex);
                throw;
            }
        }
  
        private static readonly mydentistcloudImportSoapClient SecureWebService = GenerateSecureWebService();
    }
}