﻿@model MyDSitefinityAPI.Models.sf_Emergencydentalappointment

<table cellspacing="0" cellpadding="20" border="0" width="100%" bgcolor="#d0e1e0">
    <tr>
        <td>

            <table cellspacing="0" cellpadding="20" border="0" width="640" align="center" bgcolor="#FFFFFF">
                <tr>
                    <td>

                        <table cellspacing="0" cellpadding="0" border="0" width="100%">
                            <tr>
                                <td>
                                    <img src="https://www.mydentist.co.uk/Sitefinity/WebsiteTemplates/Mydentist/App_Themes/Mydentist2015/images/site-assets/md_logo.png" alt="mydentist - helping the nation smile" width="265" height="70" />
                                </td>
                            </tr>

                            <tr>
                                <td height="20"></td>
                            </tr>

                            <tr>
                                <td>
                                    <p style="font-size:22px; font-family:arial,verdana,sans-serif; font-weight:bold; margin:10px 0; color:#000000; letter-spacing:-0.05em;">Customer Feedback</p>
                                    <p style="font-family:arial,verdana,sans-serif; margin:10px 0; color:#1b2523;">
                                        The below customer has left the following feedback for your practice via the mydentist website.
                                    </p>
                                </td>
                            </tr>

                            <tr>
                                <td>

                                    <table cellspacing="0" cellpadding="5" border="0" width="100%" style="font-size:14px; font-family:arial,verdana,sans-serif; color:#1b2523;">
                                        <tr>
                                            <td colspan="2" height="10"></td>
                                        </tr>
                                        <tr>
                                            <td colspan="2" height="10"></td>
                                        </tr>
                                        <tr>
                                            <td colspan="2" style="border-bottom:2px solid #d0e1e0;"><strong style="font-size:16px;">Emergency Form details</strong></td>
                                        </tr>
                                        @if (Model.Firstname != null)
                                        {
                                            <tr>
                                                <td style="border-bottom:1px dashed #d0e1e0;"><strong>First name</strong></td>
                                                <td style="border-bottom:1px dashed #d0e1e0;">@Model.Firstname</td>
                                            </tr>
                                        }
                                        @if (Model.Lastname != null)
                                        {
                                            <tr>
                                                <td style="border-bottom:1px dashed #d0e1e0;"><strong>Last name</strong></td>
                                                <td style="border-bottom:1px dashed #d0e1e0;">@Model.Lastname</td>
                                            </tr>
                                        }
                                        @if (Model.PracticeId != null)
                                        {
                                            <tr>
                                                <td style="border-bottom:1px dashed #d0e1e0;"><strong>Your Practice</strong></td>
                                                <td style="border-bottom:1px dashed #d0e1e0;">@Model.PracticeId</td>
                                            </tr>
                                        }
                                        @if (Model.ContactNumber != null)
                                        {
                                            <tr>
                                                <td style="border-bottom:1px dashed #d0e1e0;"><strong>Your Contact number</strong></td>
                                                <td style="border-bottom:1px dashed #d0e1e0;">@Model.ContactNumber</td>
                                            </tr>
                                        }
                                        @if (Model.DateOfBirth.ToString() != null)
                                        {
                                            <tr>
                                                <td style="border-bottom:1px dashed #d0e1e0;"><strong>Your Date of birth</strong></td>
                                                <td style="border-bottom:1px dashed #d0e1e0;">@Model.DateOfBirth</td>
                                            </tr>
                                        }

                                        <tr>
                                            <td style="border-bottom:1px dashed #d0e1e0;"><strong>Are you experiencing dental pain?</strong></td>
                                            <td style="border-bottom:1px dashed #d0e1e0;">@(Model.AreYouExperiencingDentalPain ? "Yes" : "No")</td>
                                        </tr>
                                        <tr>
                                            <td style="border-bottom:1px dashed #d0e1e0;"><strong>Do you have a broken tooth?</strong></td>
                                            <td style="border-bottom:1px dashed #d0e1e0;">@(Model.DoYouHaveABrokenTooth ? "Yes" : "No")</td>
                                        </tr>
                                        <tr>
                                            <td style="border-bottom:1px dashed #d0e1e0;"><strong>Do you have a lost filling? *</strong></td>
                                            <td style="border-bottom:1px dashed #d0e1e0;">@(Model.DoYouHaveALostFilling ? "Yes" : "No")</td>
                                        </tr>
                                        <tr>
                                            <td style="border-bottom:1px dashed #d0e1e0;"><strong>Do you require an interpreter for your appointment?</strong></td>
                                            <td style="border-bottom:1px dashed #d0e1e0;">@(Model.DoYouRequireInterpreter ? "Yes" : "No")</td>
                                        </tr>

                                        @if (Model.DateOfBirth.ToString() != null)
                                        {
                                            <tr>
                                                <td style="border-bottom:1px dashed #d0e1e0;"><strong>Please specify language?</strong></td>
                                                <td style="border-bottom:1px dashed #d0e1e0;">@Model.InterpreterLanguage</td>
                                            </tr>
                                        }

                                        <tr>
                                            <td style="border-bottom:1px dashed #d0e1e0;"><strong>Do you have any mobility issues which requires you to be seen in a ground floor or accessible surgery?</strong></td>
                                            <td style="border-bottom:1px dashed #d0e1e0;">@(Model.RequiresGroundFloorAccess ? "Yes" : "No")</td>
                                        </tr>

                                        <tr>
                                            <td style="border-bottom:1px dashed #d0e1e0;"><strong>Are you entitled to free NHS dental services??</strong></td>
                                            <td style="border-bottom:1px dashed #d0e1e0;">@(Model.AreYouEntitledToFreeNHSDentalServices ? "Yes" : "No")</td>
                                        </tr>

                                        <tr>
                                            <td style="border-bottom:1px dashed #d0e1e0;"><strong>I am entitled to free NHS dental services because on the first day of treatment:?</strong></td>
                                            <td style="border-bottom:1px dashed #d0e1e0;">@(Model.EntitledToFreeNHSDentalServicesOnFirstDay ? "Yes" : "No")</td>
                                        </tr>


                                        <tr>
                                            <td style="border-bottom:1px dashed #d0e1e0;"><strong>Person receiving benefit?</strong></td>
                                            <td style="border-bottom:1px dashed #d0e1e0;">@(Model.PersonReceivingBenefit ? "Yes" : "No")</td>
                                        </tr>

                                        @if (Model.NationalInsuranceNumber != null)
                                        {
                                            <tr>
                                                <td style="border-bottom:1px dashed #d0e1e0;"><strong>Enter National Insurance Number?</strong></td>
                                                <td style="border-bottom:1px dashed #d0e1e0;">@Model.NationalInsuranceNumber</td>
                                            </tr>
                                        }

                                        <tr>
                                            <td style="border-bottom:1px dashed #d0e1e0;"><strong>NHS Maternity exemption certificate / card no.?</strong></td>
                                            <td style="border-bottom:1px dashed #d0e1e0;">@Model.NHS_MaternityExemptionCertificate</td>
                                        </tr>




                                        <tr>
                                            <td style="border-bottom:1px dashed #d0e1e0;"><strong>I am entitled to free NHS dental services because during the course of treatment I get, or am included in an award (as a claimant, partner, or dependent person under 20) of:?</strong></td>
                                            <td style="border-bottom:1px dashed #d0e1e0;">@(Model.EntitledToFreeNHSDentalServicesDuringTreatment ? "Yes" : "No")</td>
                                        </tr>



                                        @if (Model.NameOfCollegeOrUniversity != null)
                                        {
                                            <tr>
                                                <td style="border-bottom:1px dashed #d0e1e0;"><strong>Enter name of college or university?</strong></td>
                                                <td style="border-bottom:1px dashed #d0e1e0;">@Model.NameOfCollegeOrUniversity</td>
                                            </tr>
                                        }




                                    </table>

                                </td>
                            </tr>
                            <tr>
                                <td height="20"></td>
                            </tr>
                        </table>

                    </td>
                </tr>
            </table>

        </td>
    </tr>
</table>