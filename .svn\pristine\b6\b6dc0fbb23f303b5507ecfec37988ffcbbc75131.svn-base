﻿using System.Text.Json;
using static MyDSitefinityAPI.Services.SitefinityFormService;

namespace MyDSitefinityAPI.Interfaces
{
    public interface ISitefinityFormService
    {
        Task<bool> InsertQuickContactForm(SitefinityForm quickContactForm, string rawJson);
        Task<bool> InsertFeedBackForm(SitefinityForm feedbackForm , string rawJson);
        Task<bool> InsertPatientSupportFeedBackForm(SitefinityForm feedbackForm, string rawJson);
        Task<bool> EmailChosenRecipient(SitefinityForm feedbackForm, string formName, string rawJson);
        Task<int> InsertRecruitmentWebsiteForm(SitefinityForm patientSupprtfeedbackForm, JsonElement json);
        Task<bool> ProcessWifiForm(SitefinityForm form, string rawJson);
        Task<bool> ProcessMarketingConsentForm(SitefinityForm form, string rawJson);
        Task<bool> ProcessHomePageMarketingMVCForm(SitefinityForm form, string rawJson);
        Task<bool> ProcessGeneralInquiryForm(SitefinityForm form, string rawJson);
        Task<bool> ProcessEmergencyTriage(SitefinityForm form, string rawJson);
        Task<bool> ProcessReferralForm(SitefinityForm sitefinityForm, string jsonElement, ReferralFormType referralFormType);
        Task<bool> PassWebApplicationFormData(SitefinityFormV2 form, string rawJson);
        Task<bool> Process507ReferralForm(SitefinityForm form, string rawJson);

    }
}
