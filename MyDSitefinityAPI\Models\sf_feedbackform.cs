﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MyDSitefinityAPI.Models
{
    [Table("sf_feedback", Schema = "dbo")]

    public class sf_feedbackform
    {
      [Key]
      [Column("id")]
      public Guid id {get; set;}
      public string? form_text_box__c008 {get; set;}
      public string? form_text_box__c007 {get; set;}
      public string? form_paragraph_text_box__c004 {get; set;}
      public string? form_multiple_choice__c011 { get; set;}
      public string? form_text_box__c014 {get; set;}
      public string? form_text_box__c007_0 {get; set;}
      public string? practice_name {get; set;}
      public string? form_text_box {get; set;}
      public string? rawJson {get; set;}
    }
}
