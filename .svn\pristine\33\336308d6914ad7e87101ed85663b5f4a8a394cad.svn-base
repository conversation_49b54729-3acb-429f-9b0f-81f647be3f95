﻿using Microsoft.EntityFrameworkCore;
using Mydentist.MyDSitefinityAPI.Persistence;
using Mydentist.MyDSitefinityAPI.WebConfigApi;

namespace MyDSitefinityAPI.IntegrationTests.Tests.Factories
{
    public class DbContextWebsiteConfigFactory
    {
        public static DbContextOptions<WebsiteConfigDbContext> Get()
        {
            IConfiguration configuration = new ConfigurationBuilder()
                  .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
                  .AddEnvironmentVariables()
                  .Build();

            var builder = new DbContextOptionsBuilder<WebsiteConfigDbContext>();
            Configure(
                builder,
                configuration.GetConnectionString("DBContextWebsiteConfig"));

            return builder.Options;
        }

        public static void Configure(
            DbContextOptionsBuilder<WebsiteConfigDbContext> builder,
            string connectionString)
        {
            builder.UseSqlServer(connectionString);
        }
    }
}
