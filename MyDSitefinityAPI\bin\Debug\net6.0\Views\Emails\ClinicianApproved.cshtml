﻿@using MyDSitefinityAPI.Models.PracticeDirectory;

@model PerformerProfile

<html>
<head>
</head>
<body>
    <div style="font-family: Arial; margin-left: 15px;">
        <h2 style="border-left: 10px solid #7e3b70; border-bottom: 1px solid #7e3b70; padding: 15px;">Image for @Model.Performer.Name was approved</h2>
        <div>
            <p style="padding: 10px;">
                Hello,
            </p>
            <p style="padding: 10px;">
                Image for @Model.Performer.Name was approved! It is now going to be transformed to meet the brand guidelines.<br />
            </p>
            <p style="padding: 10px;">
                Practice Directory
            </p>
        </div>
        <div style="border-top: 1px dashed #aaaaaa; padding: 10px;">
            <span style="color: #aaaaaa; font-style: italic;">This email has been generated automatically by the Practice Directory</span>
        </div>
    </div>
</body>
</html>