﻿using System.Data.SqlTypes;

namespace MyDSitefinityAPI.Models
{
    public class PatientReferralViewModel
    {

        public string? PracticeId { get; set; }
        public string? PrefferedTitle { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? DOB { get; set; }
        public string? NHSNumber{ get; set; }
        public string? HomePhone { get; set; }
        public string? MobilePhone { get; set; }
        public string? Email { get; set; }
        public string? EmailRef { get; set; }
        public string? Address1{ get; set; }
        public string? Address2 { get; set; }
        public string? Town { get; set; }
        public string? County { get; set; }
        public string? Postcode { get; set; }
        public string? Interest { get; set; }

    }
}
