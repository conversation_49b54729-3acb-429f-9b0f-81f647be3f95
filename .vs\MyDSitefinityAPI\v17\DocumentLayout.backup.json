{"Version": 1, "WorkspaceRootPath": "C:\\Dev\\MyDSitefinityAPI\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{6BBA3805-C7A2-4B17-931A-3C4FC6ADE0D7}|MyDSitefinityAPI\\MyDSitefinityAPI.csproj|c:\\dev\\mydsitefinityapi\\mydsitefinityapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6BBA3805-C7A2-4B17-931A-3C4FC6ADE0D7}|MyDSitefinityAPI\\MyDSitefinityAPI.csproj|solutionrelative:mydsitefinityapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Program Files\\dotnet\\sdk\\9.0.301\\Sdks\\Microsoft.NET.Sdk\\targets\\Microsoft.NET.ConflictResolution.targets||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{900175B0-D052-43D8-A39A-C24FA9E965D8}|MyDSitefinityAPI.IntegrationTests\\MyDSitefinityAPI.IntegrationTests.csproj|c:\\dev\\mydsitefinityapi\\mydsitefinityapi.integrationtests\\tests\\test.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{900175B0-D052-43D8-A39A-C24FA9E965D8}|MyDSitefinityAPI.IntegrationTests\\MyDSitefinityAPI.IntegrationTests.csproj|solutionrelative:mydsitefinityapi.integrationtests\\tests\\test.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6BBA3805-C7A2-4B17-931A-3C4FC6ADE0D7}|MyDSitefinityAPI\\MyDSitefinityAPI.csproj|c:\\dev\\mydsitefinityapi\\mydsitefinityapi\\controllers\\sitefinityformcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6BBA3805-C7A2-4B17-931A-3C4FC6ADE0D7}|MyDSitefinityAPI\\MyDSitefinityAPI.csproj|solutionrelative:mydsitefinityapi\\controllers\\sitefinityformcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6BBA3805-C7A2-4B17-931A-3C4FC6ADE0D7}|MyDSitefinityAPI\\MyDSitefinityAPI.csproj|c:\\dev\\mydsitefinityapi\\mydsitefinityapi\\emailhelpers\\emailsender.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6BBA3805-C7A2-4B17-931A-3C4FC6ADE0D7}|MyDSitefinityAPI\\MyDSitefinityAPI.csproj|solutionrelative:mydsitefinityapi\\emailhelpers\\emailsender.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A648B15D-A230-41DC-B8A5-A3CA5641F7F7}|MyDSitefinityAPI.ImplementationServices\\MyDSitefinityAPI.ImplementationServices.csproj|c:\\dev\\mydsitefinityapi\\mydsitefinityapi.implementationservices\\services\\recruitmenthubservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A648B15D-A230-41DC-B8A5-A3CA5641F7F7}|MyDSitefinityAPI.ImplementationServices\\MyDSitefinityAPI.ImplementationServices.csproj|solutionrelative:mydsitefinityapi.implementationservices\\services\\recruitmenthubservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{900175B0-D052-43D8-A39A-C24FA9E965D8}|MyDSitefinityAPI.IntegrationTests\\MyDSitefinityAPI.IntegrationTests.csproj|c:\\dev\\mydsitefinityapi\\mydsitefinityapi.integrationtests\\tests\\controllers\\sitefinityformcontrollertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{900175B0-D052-43D8-A39A-C24FA9E965D8}|MyDSitefinityAPI.IntegrationTests\\MyDSitefinityAPI.IntegrationTests.csproj|solutionrelative:mydsitefinityapi.integrationtests\\tests\\controllers\\sitefinityformcontrollertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{900175B0-D052-43D8-A39A-C24FA9E965D8}|MyDSitefinityAPI.IntegrationTests\\MyDSitefinityAPI.IntegrationTests.csproj|c:\\dev\\mydsitefinityapi\\mydsitefinityapi.integrationtests\\tests\\controllers\\clinicianapprovalcontrollertest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{900175B0-D052-43D8-A39A-C24FA9E965D8}|MyDSitefinityAPI.IntegrationTests\\MyDSitefinityAPI.IntegrationTests.csproj|solutionrelative:mydsitefinityapi.integrationtests\\tests\\controllers\\clinicianapprovalcontrollertest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6BBA3805-C7A2-4B17-931A-3C4FC6ADE0D7}|MyDSitefinityAPI\\MyDSitefinityAPI.csproj|c:\\dev\\mydsitefinityapi\\mydsitefinityapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{6BBA3805-C7A2-4B17-931A-3C4FC6ADE0D7}|MyDSitefinityAPI\\MyDSitefinityAPI.csproj|solutionrelative:mydsitefinityapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{6BBA3805-C7A2-4B17-931A-3C4FC6ADE0D7}|MyDSitefinityAPI\\MyDSitefinityAPI.csproj|c:\\dev\\mydsitefinityapi\\mydsitefinityapi\\app.config||{FA3CD31E-987B-443A-9B81-186104E8DAC1}", "RelativeMoniker": "D:0:0:{6BBA3805-C7A2-4B17-931A-3C4FC6ADE0D7}|MyDSitefinityAPI\\MyDSitefinityAPI.csproj|solutionrelative:mydsitefinityapi\\app.config||{FA3CD31E-987B-443A-9B81-186104E8DAC1}"}, {"AbsoluteMoniker": "D:0:0:{6BBA3805-C7A2-4B17-931A-3C4FC6ADE0D7}|MyDSitefinityAPI\\MyDSitefinityAPI.csproj|c:\\dev\\mydsitefinityapi\\mydsitefinityapi\\dbcontext\\dbwarehousecontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6BBA3805-C7A2-4B17-931A-3C4FC6ADE0D7}|MyDSitefinityAPI\\MyDSitefinityAPI.csproj|solutionrelative:mydsitefinityapi\\dbcontext\\dbwarehousecontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6BBA3805-C7A2-4B17-931A-3C4FC6ADE0D7}|MyDSitefinityAPI\\MyDSitefinityAPI.csproj|c:\\dev\\mydsitefinityapi\\mydsitefinityapi\\services\\sitefinityformservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6BBA3805-C7A2-4B17-931A-3C4FC6ADE0D7}|MyDSitefinityAPI\\MyDSitefinityAPI.csproj|solutionrelative:mydsitefinityapi\\services\\sitefinityformservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6BBA3805-C7A2-4B17-931A-3C4FC6ADE0D7}|MyDSitefinityAPI\\MyDSitefinityAPI.csproj|c:\\dev\\mydsitefinityapi\\mydsitefinityapi\\controllers\\clinicianapprovalcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6BBA3805-C7A2-4B17-931A-3C4FC6ADE0D7}|MyDSitefinityAPI\\MyDSitefinityAPI.csproj|solutionrelative:mydsitefinityapi\\controllers\\clinicianapprovalcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7B5357D1-8550-4616-A8AA-4BD98FCB7E7C}|MyDSitefinityApi.ClinicianPortalApi\\MyDSitefinityApi.ClinicianPortalApi.csproj|c:\\dev\\mydsitefinityapi\\mydsitefinityapi.clinicianportalapi\\clinicianportalapiwrapper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7B5357D1-8550-4616-A8AA-4BD98FCB7E7C}|MyDSitefinityApi.ClinicianPortalApi\\MyDSitefinityApi.ClinicianPortalApi.csproj|solutionrelative:mydsitefinityapi.clinicianportalapi\\clinicianportalapiwrapper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 1, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedHeight": 200, "SelectedChildIndex": 12, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:129:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:130:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Bookmark", "Name": "ST:0:0:{cce594b6-0c39-4442-ba28-10c64ac7e89f}"}, {"$type": "Bookmark", "Name": "ST:0:0:{e5c86464-96be-4d7c-9a8b-abcb3bbf5f92}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "Microsoft.NET.ConflictResolution.targets", "DocumentMoniker": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\Sdks\\Microsoft.NET.Sdk\\targets\\Microsoft.NET.ConflictResolution.targets", "RelativeDocumentMoniker": "..\\..\\Program Files\\dotnet\\sdk\\9.0.301\\Sdks\\Microsoft.NET.Sdk\\targets\\Microsoft.NET.ConflictResolution.targets", "ToolTip": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\Sdks\\Microsoft.NET.Sdk\\targets\\Microsoft.NET.ConflictResolution.targets", "RelativeToolTip": "..\\..\\Program Files\\dotnet\\sdk\\9.0.301\\Sdks\\Microsoft.NET.Sdk\\targets\\Microsoft.NET.ConflictResolution.targets", "ViewState": "AgIAAAkAAAAAAAAAAAAwwCcAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003801|", "WhenOpened": "2025-08-01T20:10:54.322Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "Test.cs", "DocumentMoniker": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.IntegrationTests\\Tests\\Test.cs", "RelativeDocumentMoniker": "MyDSitefinityAPI.IntegrationTests\\Tests\\Test.cs", "ToolTip": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.IntegrationTests\\Tests\\Test.cs", "RelativeToolTip": "MyDSitefinityAPI.IntegrationTests\\Tests\\Test.cs", "ViewState": "AgIAAEUAAAAAAAAAAAAnwGYAAABMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-01T20:07:41.837Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "EmailSender.cs", "DocumentMoniker": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI\\EmailHelpers\\EmailSender.cs", "RelativeDocumentMoniker": "MyDSitefinityAPI\\EmailHelpers\\EmailSender.cs", "ToolTip": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI\\EmailHelpers\\EmailSender.cs", "RelativeToolTip": "MyDSitefinityAPI\\EmailHelpers\\EmailSender.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAMwCgAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-01T20:02:24.232Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "ClinicianApprovalControllerTest.cs", "DocumentMoniker": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.IntegrationTests\\Tests\\Controllers\\ClinicianApprovalControllerTest.cs", "RelativeDocumentMoniker": "MyDSitefinityAPI.IntegrationTests\\Tests\\Controllers\\ClinicianApprovalControllerTest.cs", "ToolTip": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.IntegrationTests\\Tests\\Controllers\\ClinicianApprovalControllerTest.cs", "RelativeToolTip": "MyDSitefinityAPI.IntegrationTests\\Tests\\Controllers\\ClinicianApprovalControllerTest.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T15:20:02.533Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "SitefinityFormControllerTests.cs", "DocumentMoniker": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.IntegrationTests\\Tests\\Controllers\\SitefinityFormControllerTests.cs", "RelativeDocumentMoniker": "MyDSitefinityAPI.IntegrationTests\\Tests\\Controllers\\SitefinityFormControllerTests.cs", "ToolTip": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.IntegrationTests\\Tests\\Controllers\\SitefinityFormControllerTests.cs", "RelativeToolTip": "MyDSitefinityAPI.IntegrationTests\\Tests\\Controllers\\SitefinityFormControllerTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T15:19:15.668Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "RecruitmentHubService.cs", "DocumentMoniker": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.ImplementationServices\\Services\\RecruitmentHubService.cs", "RelativeDocumentMoniker": "MyDSitefinityAPI.ImplementationServices\\Services\\RecruitmentHubService.cs", "ToolTip": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.ImplementationServices\\Services\\RecruitmentHubService.cs", "RelativeToolTip": "MyDSitefinityAPI.ImplementationServices\\Services\\RecruitmentHubService.cs", "ViewState": "AgIAAB0AAAAAAAAAAAAWwEYAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T12:43:13.143Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "Program.cs", "DocumentMoniker": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI\\Program.cs", "RelativeDocumentMoniker": "MyDSitefinityAPI\\Program.cs", "ToolTip": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI\\Program.cs", "RelativeToolTip": "MyDSitefinityAPI\\Program.cs", "ViewState": "AgIAAEsAAAAAAAAAAAAWwHgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T12:44:03.292Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "DBWarehouseContext.cs", "DocumentMoniker": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI\\DBContext\\DBWarehouseContext.cs", "RelativeDocumentMoniker": "MyDSitefinityAPI\\DBContext\\DBWarehouseContext.cs", "ToolTip": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI\\DBContext\\DBWarehouseContext.cs", "RelativeToolTip": "MyDSitefinityAPI\\DBContext\\DBWarehouseContext.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABoAAABCAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T12:32:55.677Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "SitefinityFormService.cs", "DocumentMoniker": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI\\Services\\SitefinityFormService.cs", "RelativeDocumentMoniker": "MyDSitefinityAPI\\Services\\SitefinityFormService.cs", "ToolTip": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI\\Services\\SitefinityFormService.cs", "RelativeToolTip": "MyDSitefinityAPI\\Services\\SitefinityFormService.cs", "ViewState": "AgIAAOoAAAAAAAAAAAD4vwsBAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T09:31:03.149Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "SitefinityFormController.cs", "DocumentMoniker": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI\\Controllers\\SitefinityFormController.cs", "RelativeDocumentMoniker": "MyDSitefinityAPI\\Controllers\\SitefinityFormController.cs", "ToolTip": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI\\Controllers\\SitefinityFormController.cs", "RelativeToolTip": "MyDSitefinityAPI\\Controllers\\SitefinityFormController.cs", "ViewState": "AgIAAJ4AAAAAAAAAAAAewLoAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T09:30:48.336Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "app.config", "DocumentMoniker": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI\\app.config", "RelativeDocumentMoniker": "MyDSitefinityAPI\\app.config", "ToolTip": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI\\app.config", "RelativeToolTip": "MyDSitefinityAPI\\app.config", "ViewState": "AgIAAAAAAAAAAAAAAAAAABwAAABdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000601|", "WhenOpened": "2025-07-29T07:26:32.842Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "appsettings.json", "DocumentMoniker": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI\\appsettings.json", "RelativeDocumentMoniker": "MyDSitefinityAPI\\appsettings.json", "ToolTip": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI\\appsettings.json", "RelativeToolTip": "MyDSitefinityAPI\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-28T13:20:03.827Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "ClinicianApprovalController.cs", "DocumentMoniker": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI\\Controllers\\ClinicianApprovalController.cs", "RelativeDocumentMoniker": "MyDSitefinityAPI\\Controllers\\ClinicianApprovalController.cs", "ToolTip": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI\\Controllers\\ClinicianApprovalController.cs", "RelativeToolTip": "MyDSitefinityAPI\\Controllers\\ClinicianApprovalController.cs", "ViewState": "AgIAAF8AAAAAAAAAAAAuwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-25T10:58:00.035Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "ClinicianPortalApiWrapper.cs", "DocumentMoniker": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityApi.ClinicianPortalApi\\ClinicianPortalApiWrapper.cs", "RelativeDocumentMoniker": "MyDSitefinityApi.ClinicianPortalApi\\ClinicianPortalApiWrapper.cs", "ToolTip": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityApi.ClinicianPortalApi\\ClinicianPortalApiWrapper.cs", "RelativeToolTip": "MyDSitefinityApi.ClinicianPortalApi\\ClinicianPortalApiWrapper.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-25T10:39:53.62Z", "EditorCaption": ""}]}]}]}