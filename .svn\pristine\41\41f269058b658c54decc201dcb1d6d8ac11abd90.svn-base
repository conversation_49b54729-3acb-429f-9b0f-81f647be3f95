﻿using Newtonsoft.Json;
using MyDSitefinityAPI.Models;
using MyDSitefinityAPI.ExtensionMethods;
using MyDSitefinityAPI.Interfaces;

namespace MyDSitefinityAPI.Services
{
    public class FindNearestPracticeService : IFindNearestPracticeService
    {
        private readonly ILogger<FindNearestPracticeService> _logger;
        private readonly IConfiguration _configuration;

        public FindNearestPracticeService(ILogger<FindNearestPracticeService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
        }

        public async Task<(int? practiceId, string postcode)> FindNearestPracticePostcode(SitefinityForm form)
        {
            var postcode = form.GetPropertyValueByTitle("Postcode")?.Trim();

            // Log the postcode before constructing the endpoint
            _logger.LogInformation($"Postcode retrieved from form: '{postcode}'");

            // Ensure the postcode is not null or empty
            if (string.IsNullOrWhiteSpace(postcode))
            {
                _logger.LogWarning("Postcode is null or empty. Cannot construct the endpoint.");
                return (null, null);
            }

            // Get the base URL and endpoint from the configuration
            var baseUrl = _configuration["NearestPractice:BaseUrl"];
            var endpoint = "/api/Practice/GetNearestPractice";

            // URL-encode the postcode to handle spaces and special characters
            var encodedPostcode = System.Net.WebUtility.UrlEncode(postcode);
            var requestUrl = $"{baseUrl}{endpoint}?postCode={encodedPostcode}";

            // Log the constructed request URL for debugging
            _logger.LogInformation($"Constructed request URL: '{requestUrl}'");

            using (HttpClient client = new HttpClient())
            {
                // Hardcode the API key here
                var apiKey = _configuration["NearestPractice:PracticeLocationApiKey"];
                client.DefaultRequestHeaders.Add("X-ApiKey", apiKey);

                try
                {
                    _logger.LogInformation($"Requesting closest practice for Patient postcode '{postcode}'");

                    HttpResponseMessage result = await client.GetAsync(requestUrl);

                    if (!result.IsSuccessStatusCode)
                    {
                        var errorContent = await result.Content.ReadAsStringAsync();
                        _logger.LogError($"Error retrieving nearest practice: {result.ReasonPhrase} ({(int)result.StatusCode}). Response: {errorContent}");
                        return (null, null);
                    }

                    var content = await result.Content.ReadAsStringAsync();
                    var closestPracticeDetails = JsonConvert.DeserializeObject<PracticeLocationResponse>(content);

                    if (closestPracticeDetails != null)
                    {
                        _logger.LogInformation($"Close Practice found: practiceId {closestPracticeDetails.PracticeId}, postcode {closestPracticeDetails.Postcode}");
                        return (closestPracticeDetails.PracticeId, closestPracticeDetails.Postcode);
                    }
                    else
                    {
                        _logger.LogWarning($"No nearest practice found for postcode: {postcode}");
                        return (null, null);
                    }
                }
                catch (Exception e)
                {
                    _logger.LogError($"Exception retrieving nearest practice: {e.Message}");
                    return (null, null);
                }
            }
        }
    }
}