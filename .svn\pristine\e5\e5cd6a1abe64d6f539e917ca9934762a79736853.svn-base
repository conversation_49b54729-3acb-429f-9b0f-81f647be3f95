{"Name": "Form is submitted", "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49", "Item": {"JobTitle": "Project Manager", "JobReferenceNumber": "4891", "GAClientId": "", "JobCategory": "Support Centre", "TextFieldController_0": "Test08122022", "EmailTextFieldController": "<EMAIL>", "FileFieldController": [{"Id": "8166d19b-cae7-6322-9971-ff0000979a84", "ChildItemId": "7866d19b-cae7-6322-9971-ff0000979a84", "ParentItemId": "9469d19b-cae7-6322-9971-ff0000979a84", "ChildItemProviderName": "SystemLibrariesProvider", "ParentItemProviderName": "OpenAccessDataProvider", "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document", "ChildItemAdditionalInfo": "**********.pdf|http://www.mydentist.co.uk/docs/systemlibrariesprovider/form-files-sf_vacancyapplicationform/**********.pdf|docs/systemlibrariesprovider/form-files-sf_vacancyapplicationform/**********.pdf", "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_vacancyapplicationform", "ParentItemAdditionalInfo": null, "LastModified": "2022-12-08T10:44:13.827Z", "Ordinal": 0.0, "Attributes": {}, "ApplicationName": "GroupWebsite/", "ComponentPropertyName": "FileFieldController", "AvailableForLive": false, "AvailableForMaster": false, "AvailableForTemp": false, "IsParentDeleted": false, "IsChildDeleted": false}], "TextFieldController_1": "Test08122022", "TextFieldController_2": "***********", "TextFieldController_4": "", "TextFieldController_3": "M28 7ST", "TextFieldController_6": null, "TextFieldController_5": "test", "TextFieldController_7": "Yes", "TextFieldController_8": "Yes", "DropdownListFieldController_0": null, "TextFieldController_9": null, "TextFieldController_10": null, "TextFieldController_11": null, "TextFieldController_12": null, "TextFieldController_13": null, "DropdownListFieldController_1": null, "DropdownListFieldController_2": null, "DropdownListFieldController_3": null, "DropdownListFieldController_4": null, "DropdownListFieldController_5": null, "DropdownListFieldController_6": null, "DropdownListFieldController_7": null, "DropdownListFieldController_8": null}, "OriginalEvent": {"EntryId": "9469d19b-cae7-6322-9971-ff0000979a84", "ReferralCode": "17", "UserId": "d7530c9a-cae7-6322-9971-ff0000979a84", "Username": "Chitra", "IpAddress": "::1", "SubmissionTime": "2022-12-08T10:44:12.2406874Z", "FormId": "6c0bd19b-cae7-6322-9971-ff0000979a84", "FormName": "sf_vacancyapplicationform", "FormTitle": "Vacancy Application Form", "FormSubscriptionListId": "********-0000-0000-0000-********0000", "SendConfirmationEmail": false, "Controls": [{"FieldControlName": "JobTitle", "Id": "4150d19b-cae7-6322-9971-ff0000979a84", "SiblingId": "********-0000-0000-0000-********0000", "Text": null, "Type": 0, "Title": "JobTitle", "FieldName": "JobTitle", "Value": "Project Manager", "OldValue": null}, {"FieldControlName": "JobReferenceNumber", "Id": "094ad19b-cae7-6322-9971-ff0000979a84", "SiblingId": "4150d19b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Job Reference Number", "FieldName": "JobReferenceNumber", "Value": "4891", "OldValue": null}, {"FieldControlName": "GAClientId", "Id": "e146d19b-cae7-6322-9971-ff0000979a84", "SiblingId": "********-0000-0000-0000-********0000", "Text": null, "Type": 0, "Title": "GAClientId", "FieldName": "GAClientId", "Value": "", "OldValue": null}, {"FieldControlName": "JobCategory", "Id": "154ad19b-cae7-6322-9971-ff0000979a84", "SiblingId": "e146d19b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "JobCategory", "FieldName": "JobCategory", "Value": "Support Centre", "OldValue": null}, {"FieldControlName": null, "Id": "270fd19b-cae7-6322-9971-ff0000979a84", "SiblingId": "********-0000-0000-0000-********0000", "Text": null, "Type": 0, "Title": "First Name", "FieldName": "TextFieldController_0", "Value": "Test08122022", "OldValue": null}, {"FieldControlName": null, "Id": "4b0fd19b-cae7-6322-9971-ff0000979a84", "SiblingId": "270fd19b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Email", "FieldName": "EmailTextFieldController", "Value": "<EMAIL>", "OldValue": null}, {"FieldControlName": null, "Id": "5b0fd19b-cae7-6322-9971-ff0000979a84", "SiblingId": "4b0fd19b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 1, "Title": "CV Upload", "FieldName": "FileFieldController", "Value": [{"Id": "8166d19b-cae7-6322-9971-ff0000979a84", "ChildItemId": "7866d19b-cae7-6322-9971-ff0000979a84", "ParentItemId": "9469d19b-cae7-6322-9971-ff0000979a84", "ChildItemProviderName": "SystemLibrariesProvider", "ParentItemProviderName": "OpenAccessDataProvider", "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document", "ChildItemAdditionalInfo": "**********.pdf|http://www.mydentist.co.uk/docs/systemlibrariesprovider/form-files-sf_vacancyapplicationform/**********.pdf|docs/systemlibrariesprovider/form-files-sf_vacancyapplicationform/**********.pdf", "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_vacancyapplicationform", "ParentItemAdditionalInfo": null, "LastModified": "2022-12-08T10:44:13.827Z", "Ordinal": 0.0, "Attributes": {}, "ApplicationName": "GroupWebsite/", "ComponentPropertyName": "FileFieldController", "AvailableForLive": false, "AvailableForMaster": false, "AvailableForTemp": false, "IsParentDeleted": false, "IsChildDeleted": false}], "OldValue": null}, {"FieldControlName": null, "Id": "310fd19b-cae7-6322-9971-ff0000979a84", "SiblingId": "********-0000-0000-0000-********0000", "Text": null, "Type": 0, "Title": "Last Name", "FieldName": "TextFieldController_1", "Value": "Test08122022", "OldValue": null}, {"FieldControlName": null, "Id": "410fd19b-cae7-6322-9971-ff0000979a84", "SiblingId": "310fd19b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Phone Number", "FieldName": "TextFieldController_2", "Value": "***********", "OldValue": null}, {"FieldControlName": null, "Id": "650fd19b-cae7-6322-9971-ff0000979a84", "SiblingId": "410fd19b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "<PERSON>ed<PERSON> Account", "FieldName": "TextFieldController_4", "Value": "", "OldValue": null}, {"FieldControlName": null, "Id": "510fd19b-cae7-6322-9971-ff0000979a84", "SiblingId": "********-0000-0000-0000-********0000", "Text": null, "Type": 0, "Title": "Home Postcode", "FieldName": "TextFieldController_3", "Value": "M28 7ST", "OldValue": null}, {"FieldControlName": null, "Id": "790fd19b-cae7-6322-9971-ff0000979a84", "SiblingId": "3b0fd19b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "GDC Number", "FieldName": "TextFieldController_6", "Value": null, "OldValue": null}, {"FieldControlName": null, "Id": "6f0fd19b-cae7-6322-9971-ff0000979a84", "SiblingId": "790fd19b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Where did you hear about this vacancy?", "FieldName": "TextFieldController_5", "Value": "test", "OldValue": null}, {"FieldControlName": null, "Id": "8e0fd19b-cae7-6322-9971-ff0000979a84", "SiblingId": "6f0fd19b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Do you currently hold eligibility to work in the UK?", "FieldName": "TextFieldController_7", "Value": "Yes", "OldValue": null}, {"FieldControlName": null, "Id": "980fd19b-cae7-6322-9971-ff0000979a84", "SiblingId": "8e0fd19b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Do you currently hold a UK Driving licence?", "FieldName": "TextFieldController_8", "Value": "Yes", "OldValue": null}, {"FieldControlName": null, "Id": "a20fd19b-cae7-6322-9971-ff0000979a84", "SiblingId": "980fd19b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Don’t have a GDC number", "FieldName": "DropdownListFieldController_0", "Value": null, "OldValue": null}, {"FieldControlName": null, "Id": "ad0fd19b-cae7-6322-9971-ff0000979a84", "SiblingId": "a20fd19b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Current City / Town", "FieldName": "TextFieldController_9", "Value": null, "OldValue": null}, {"FieldControlName": null, "Id": "b70fd19b-cae7-6322-9971-ff0000979a84", "SiblingId": "ad0fd19b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Current Country", "FieldName": "TextFieldController_10", "Value": null, "OldValue": null}, {"FieldControlName": null, "Id": "c10fd19b-cae7-6322-9971-ff0000979a84", "SiblingId": "b70fd19b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "University of qualification", "FieldName": "TextFieldController_11", "Value": null, "OldValue": null}, {"FieldControlName": null, "Id": "cb0fd19b-cae7-6322-9971-ff0000979a84", "SiblingId": "c10fd19b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Country of Qualification", "FieldName": "TextFieldController_12", "Value": null, "OldValue": null}, {"FieldControlName": null, "Id": "d50fd19b-cae7-6322-9971-ff0000979a84", "SiblingId": "cb0fd19b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Year of qualification", "FieldName": "TextFieldController_13", "Value": null, "OldValue": null}, {"FieldControlName": null, "Id": "df0fd19b-cae7-6322-9971-ff0000979a84", "SiblingId": "d50fd19b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Visa status", "FieldName": "DropdownListFieldController_1", "Value": null, "OldValue": null}, {"FieldControlName": null, "Id": "0810d19b-cae7-6322-9971-ff0000979a84", "SiblingId": "df0fd19b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "As you do not currently hold a GDC registration, you may be interested in our trainee dental nurse programme. This is a fully funded learn while you earn course, accredited by the NEBDN. We would like to ask you a few more questions to start your career in dental nursing- is that ok?", "FieldName": "DropdownListFieldController_2", "Value": null, "OldValue": null}, {"FieldControlName": null, "Id": "1310d19b-cae7-6322-9971-ff0000979a84", "SiblingId": "0810d19b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Do you have a Grade C/4 or above in English GCSE (or equivalent)?", "FieldName": "DropdownListFieldController_3", "Value": null, "OldValue": null}, {"FieldControlName": null, "Id": "1e10d19b-cae7-6322-9971-ff0000979a84", "SiblingId": "1310d19b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Would you be comfortable working in a clinical healthcare setting (working with medical equipment, needles etc)?", "FieldName": "DropdownListFieldController_4", "Value": null, "OldValue": null}, {"FieldControlName": null, "Id": "2910d19b-cae7-6322-9971-ff0000979a84", "SiblingId": "1e10d19b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Would you be comfortable assisting with dental procedures (ie tooth extractions?)", "FieldName": "DropdownListFieldController_5", "Value": null, "OldValue": null}, {"FieldControlName": null, "Id": "3410d19b-cae7-6322-9971-ff0000979a84", "SiblingId": "2910d19b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Putting ‘Patients First’ is our key strength – would you be comfortable dealing with patients who are nervous, in pain or upset?", "FieldName": "DropdownListFieldController_6", "Value": null, "OldValue": null}, {"FieldControlName": null, "Id": "3f10d19b-cae7-6322-9971-ff0000979a84", "SiblingId": "3410d19b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Are you able to commit to a 12-18 month training programme? This will be a mixture of on the job and online learning online learning modules (topics include Microbiology/ Pathology/ Oral Surgery/ Pain control/ Perio Diseases)written and practical exams and submission of a record of experience.", "FieldName": "DropdownListFieldController_7", "Value": null, "OldValue": null}, {"FieldControlName": null, "Id": "4a10d19b-cae7-6322-9971-ff0000979a84", "SiblingId": "3f10d19b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Poss a temporary question about “are you comfortable wearing PPE?” (with a picture?)", "FieldName": "DropdownListFieldController_8", "Value": null, "OldValue": null}], "Origin": null}, "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent, Telerik.Sitefinity, Version=14.3.8000.0, Culture=neutral, PublicKeyToken=b28c218413bdf563"}