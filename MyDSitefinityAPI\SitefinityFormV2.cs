﻿namespace MyDSitefinityAPI
{
    public class SitefinityFormV2
    {
        public string Name { get; set; }
        public string SiteId { get; set; }
        public object? Item { get; set; }
        public FormEventV2 OriginalEvent { get; set; }
        public string OriginalEventType { get; set; }
    }

    public class FormEventV2
    {
        public string EntryId { get; set; }
        public string ReferralCode { get; set; }
        public string UserId { get; set; }
        public string Username { get; set; }
        public string IpAddress { get; set; }
        public DateTime SubmissionTime { get; set; }
        public string FormId { get; set; }
        public string FormName { get; set; }
        public string FormTitle { get; set; }
        public string FormSubscriptionListId { get; set; }
        public bool SendConfirmationEmail { get; set; }
        public ControlV2[] Controls { get; set; }
        public object? Origin { get; set; }
    }

    public class ControlV2
    {
        public object? FieldControlName { get; set; }
        public string Id { get; set; }
        public string SiblingId { get; set; }
        public object? Text { get; set; }
        public int Type { get; set; }
        public string Title { get; set; }
        public string FieldName { get; set; }
        public object? Value { get; set; }
        public object? OldValue { get; set; }
    }

}
