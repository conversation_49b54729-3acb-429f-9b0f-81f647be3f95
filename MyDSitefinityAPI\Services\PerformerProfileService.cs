﻿using MyDSitefinityAPI.DBContext;
using MyDSitefinityAPI.Models.PracticeDirectory;
using System.Data.Entity;
using System.Net;
using IDHGroup.SharedLibraries.SitefinityServiceCaller.Core;
using IDHGroup.SharedLibraries.SitefinityServiceCaller.Core.Exceptions;
using mydentist;
using MyDSitefinityAPI.EmailHelpers;
using MyDSitefinityAPI.Interfaces;
using MyDSitefinityAPI.Models;
using ConfigurationManager = System.Configuration.ConfigurationManager;

namespace MyDSitefinityAPI.Services
{
    /// <summary>
    /// A service which allows a performer profile to be retrieved and modified
    /// </summary>
    public class PerformerProfileService
    {
        private ILogger<PerformerProfileService> Logger { get; }
        private DBWarehouseContext DbContext { get; }
        private IEmailSender EmailSender { get; }
        private WebserviceCaller WebserviceCaller { get; }

        /// <summary>
        /// Creates a new <see cref="PerformerProfileService"/>
        /// </summary>
        /// <param name="logger">A logger for the service</param>
        /// <param name="dbContext">A DBContext. If null is passed, a new <see cref="DBWarehouseContext"/> will be created</param>
        /// <param name="emailSender">An email sender to let appropriate parties know about modifications</param>
        /// <param name="webserviceCaller">A webservice caller which will send a request to Sitefiinty, to update any approved clinicians</param>
        public PerformerProfileService(ILogger<PerformerProfileService> logger, DBWarehouseContext? dbContext, IEmailSender emailSender, WebserviceCaller webserviceCaller)
        {
            Logger = logger;
            DbContext = dbContext ?? new DBWarehouseContext();
            EmailSender = emailSender;
            WebserviceCaller = webserviceCaller;
        }

        /// <summary>
        /// Creates a new <see cref="PerformerProfileService"/>
        /// </summary>
        /// <param name="logger">A logger for the service</param>
        /// <param name="emailSender">An email sender to let appropriate parties know about modifications</param>
        /// <param name="webserviceCaller">A webservice caller which will send a request to Sitefiinty, to update any approved clinicians</param>
        public PerformerProfileService(ILogger<PerformerProfileService> logger, IEmailSender emailSender, WebserviceCaller webserviceCaller) : this(logger, null, emailSender, webserviceCaller)
        {
        }

        /// <summary>
        /// Gets the <see cref="PerformerProfile"/> associated with the image ID
        /// </summary>
        /// <param name="imageId"></param>
        /// <returns></returns>
        public PerformerProfile? GetByImageId(Guid imageId)
        {
            PerformerImage? performerImage = DbContext.PerformerImages
                .Include(performerImage => performerImage.Practice)
                .Include(performerImage => performerImage.PerformerProfile)
                .FirstOrDefault(performerImage => performerImage.ImageId == imageId);

            return performerImage?.PerformerProfile;
        }

        /// <summary>
        /// Approves a performer and saves any changes to their biography. Resets the rejection note back to null
        /// </summary>
        /// <param name="performer"></param>
        /// <param name="saltedUser">The current user</param>
        public void ApprovePerformer(PerformerProfile performer, PublicUser saltedUser)
        {
            PerformerProfile savedPerformer = DbContext.PerformerProfiles.First(potentialPerformer => potentialPerformer.GdcNumber == performer.GdcNumber);

            savedPerformer.OnlinePresenceStatusId = (int)OnlinePresenceStatus.WithMarketingTeam;
            savedPerformer.LastEditedBy = saltedUser.UserId;
            savedPerformer.LastEditedDate = DateTime.Now;
            savedPerformer.Bio = performer.Bio;
            savedPerformer.RejectionNote = null;

            DbContext.SaveChanges();
        }

        /// <summary>
        /// Rejects a performer and saves any changes to their biography
        /// </summary>
        /// <param name="performer"></param>
        /// <param name="saltedUser"></param>
        public void RejectPerformer(PerformerProfile performer, PublicUser saltedUser)
        {
            PerformerProfile savedPerformer = DbContext.PerformerProfiles.First(potentialPerformer => potentialPerformer.GdcNumber == performer.GdcNumber);

            savedPerformer.OnlinePresenceStatusId = (int)OnlinePresenceStatus.WithPracticeManager;
            savedPerformer.LastEditedBy = saltedUser.UserId;
            savedPerformer.LastEditedDate = DateTime.Now;
            savedPerformer.Bio = performer.Bio;
            savedPerformer.RejectionNote = performer.RejectionNote;

            DbContext.SaveChanges();
        }

        /// <summary>
        /// Lets the practice know that their performer has been approved
        /// </summary>
        /// <param name="approvedPerformer"></param>
        public async Task NotifyPracticeOfApprovalAsync(PerformerProfile approvedPerformer)
        {
            EmailDetail emailDetail = new EmailDetail
            {
                ViewModel = approvedPerformer,
                EmailAddress = approvedPerformer.Images.First().Practice.PMEmail,
                Subject = "Image for clinician was approved",
                TemplateLocation = "Emails/ClinicianApproved"
            };

            Logger.LogDebug($"Notifying practice of approval for performer ${approvedPerformer.GdcNumber}");

            await EmailSender.SendEmail(emailDetail);
        }

        /// <summary>
        /// Lets the practice know that their performer has been rejected
        /// </summary>
        /// <param name="rejectedPerformer"></param>
        /// <returns></returns>
        public async Task NotifyPracticeOfRejectionAsync(PerformerProfile rejectedPerformer)
        {
            EmailDetail emailDetail = new EmailDetail
            {
                ViewModel = rejectedPerformer,
                EmailAddress = rejectedPerformer.Images.First().Practice.PMEmail,
                Subject = "Image for clinician was rejected",
                TemplateLocation = "Emails/ClinicianRejected"
            };

            Logger.LogDebug($"Notifying practice of rejection for performer ${rejectedPerformer.GdcNumber}");

            await EmailSender.SendEmail(emailDetail);
        }

        /// <summary>
        /// Lets marketing know that a practice's performer has been approved so they can beautify the image
        /// </summary>
        /// <param name="approvedPerformer"></param>
        /// <returns></returns>
        public async Task NotifyMarketingOfApprovalAsync(PerformerProfile approvedPerformer)
        {
            EmailDetail emailDetail = new EmailDetail
            {
                ViewModel = approvedPerformer,
                EmailAddress = ConfigurationManager.AppSettings["MarketingTeamEmail"],
                Subject = "Image for clinician has been approved by PNS Team",
                TemplateLocation = "Emails/ClinicianApproved-Marketing"
            };

            Logger.LogDebug($"Notifying marketing of approval for performer ${approvedPerformer.GdcNumber}");

            await EmailSender.SendEmail(emailDetail);
        }

        /// <summary>
        /// Sends a request to Sitefinity to update a clinician
        /// </summary>
        /// <param name="performerProfile"></param>
        /// <param name="practiceId"></param>
        public void UpdateSitefinityClinician(PerformerProfile performerProfile, int practiceId)
        {
            IEnumerable<PracticePerson> linkedPracticePeople = performerProfile.PracticePeople
                .Where(pp => pp.PracticeId == practiceId);

            IEnumerable<PracticePerson> currentPracticePeople = linkedPracticePeople
                .Where(pp => pp.PracticePersonLinks.Any(ppl => ppl.IsLeaver == false || ppl.LeaverDate > DateTime.Now));

            PracticePerson practicePerson = currentPracticePeople.First();

            Role role = practicePerson.Role;
            RoleType roleType = role.RoleType;

            WebClinician webClinician = new WebClinician
            {
                Name = performerProfile.Performer.Name,
                PracticeId = practiceId,
                Title = performerProfile.Title,
                GdcNo = performerProfile.GdcNumber,
                Gender = performerProfile.Gender,
                DentalSchoolQualified = performerProfile.DentalSchoolQualified,
                CountryQualified = performerProfile.CountryQualified,
                YearQualified = performerProfile.YearQualified,
                Role = roleType.Description,
                Bio = performerProfile.Bio,
                MediaReferenceId = Guid.Empty.ToString()
            };

            //Image image = Image.GetByClinician(clinician.GdcNo);

            WebClinicianImage webClinicianImage = new WebClinicianImage()
            {
                ImageId = Guid.Empty.ToString()
            };

            //if (image != null)
            //{
            //    webClinicianImage.ImageId = image.ImageId;
            //    webClinicianImage.Filename = image.Filename;
            //    webClinicianImage.Fileextension = image.Fileextension;
            //    webClinicianImage.ImageGallery = image.ImageGallery;
            //    webClinicianImage.Filepath = image.Filepath;

            //}
            
            try
            {
                WebserviceCaller.UpdateClinician(webClinician, webClinicianImage, "Update");
            }
            catch (WebException)
            {
            }
            catch (SitefinityWebserviceException)
            {
            }
            catch (NotSupportedException)
            {
            }
            catch (SitefinityAuthenticationException)
            {
            }
        }
    }
}