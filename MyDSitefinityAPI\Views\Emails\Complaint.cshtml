﻿@model MyDSitefinityAPI.Models.CustomerFeedbackViewModel

<p>New form submission</p>
<p>Feedback Submitted on @Model.DateSubmitted, via IP by Anonymous</p>

<table>
	<tr>
		<td>
			Would you like to leave a compliment or complaint?
		</td>
		<td>
			Complaint
		</td>
	</tr>
	<tr>
		<td>
			The practice you visited
		</td>
		<td>
			<p>@Model.PracticeName</p>
			<p>@Html.Raw("@@REQUESTER=")@<EMAIL>("@@")</p>
		</td>
	</tr>
	<tr>
		<td>
			Your first name
		</td>
		<td>
			@Model.PatientName
		</td>
	</tr>
	<tr>
		<td>
			Your email address
		</td>
		<td>
			@Model.Email
		</td>
	</tr>
	<tr>
		<td>
			Your phone number
		</td>
		<td>
			@Model.PhoneNumber
		</td>
	</tr>
	<tr>
		<td>
			Title of your feedback
		</td>
		<td>
			@Model.FeedbackTitle
		</td>
	</tr>
	<tr>
		<td>
			Your feedback
		</td>
		<td>
			@Model.FeedbackInformation
		</td>
	</tr>
</table>

@Html.Raw("@@OPERATION=AddRequest")@Html.Raw("@@")<br/>
@Html.Raw("@@REQUESTTEMPLATE=Report a Complaint")@Html.Raw("@@")<br/>
@Html.Raw("@@SUBJECT=COMPLAINT – ")‘@Model.FeedbackTitle’@Html.Raw("@@")<br/>
@Html.Raw("@@MODE=Website Feedback")@Html.Raw("@@")<br/>
@Html.Raw("@@TECHNICIAN=PM Review")@Html.Raw("@@")<br/>
@Html.Raw("@@STATUS=PM Review")@Html.Raw("@@")<br/>
@Html.Raw("@@HOW WAS THIS RAISED?=Website")@Html.Raw("@@")<br/>

