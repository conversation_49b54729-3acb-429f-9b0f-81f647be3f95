2025-07-30T08:23:35.4097519+01:00  [WRN] Using an in-memory repository. Keys will not be persisted to storage. (28e83010)
2025-07-30T08:23:35.4129742+01:00  [WRN] Neither user profile nor HKLM registry available. Using an ephemeral key repository. Protected data will be unavailable when application exits. (54f66960)
2025-07-30T08:23:35.4521739+01:00  [WRN] No XML encryptor configured. Key {370a74da-c972-4624-8481-370c95e237af} may be persisted to storage in unencrypted form. (9ca7e61e)
2025-07-30T08:23:35.5328974+01:00 801864c5-0000-8700-b63f-84710c7967bb [WRN] Failed to determine the https port for redirect. (ca76cc21)
2025-07-30T08:23:35.8751471+01:00 801864c5-0000-8700-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4201\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sunandita\",\"lastName\":\"Bose\",\"email\":\"<EMAIL>\",\"phone\":\"7828012913\",\"Phonenumbercode\":\"IMN_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"B74 4YD\",\"CurrentlyLive\":\"GBR\",\"GraduateFrom\":\"IND\",\"GAClientId\":\"1.1353945850\"},\"jobQuestions\":[{\"QuestionId\":\"3\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"2\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"191500\"}],\"ParentQuestionId\":3}]}]}",
    "FileFieldController": [
      {
        "Id": "a8dcca67-d560-40d1-a8a7-313c0dee66ed",
        "ChildItemId": "f55e36d3-9118-4b3f-94d1-61448c6088e8",
        "ParentItemId": "f7d55c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4201_Sunandita_Bose.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4201_sunandita_bose.docx?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-30T07:23:34.237Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "f7d55c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4201_Sunandita_Bose.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4201_sunandita_bose.docx?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "f7d55c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6020",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "***************",
    "SubmissionTime": "2025-07-30T07:23:34.2208646Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4201\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sunandita\",\"lastName\":\"Bose\",\"email\":\"<EMAIL>\",\"phone\":\"7828012913\",\"Phonenumbercode\":\"IMN_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"B74 4YD\",\"CurrentlyLive\":\"GBR\",\"GraduateFrom\":\"IND\",\"GAClientId\":\"1.1353945850\"},\"jobQuestions\":[{\"QuestionId\":\"3\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"2\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"191500\"}],\"ParentQuestionId\":3}]}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "a8dcca67-d560-40d1-a8a7-313c0dee66ed",
            "ChildItemId": "f55e36d3-9118-4b3f-94d1-61448c6088e8",
            "ParentItemId": "f7d55c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4201_Sunandita_Bose.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4201_sunandita_bose.docx?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-30T07:23:34.237Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "f7d55c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4201_Sunandita_Bose.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4201_sunandita_bose.docx?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (de0df9b7)
2025-07-30T08:23:35.9648857+01:00 801864c5-0000-8700-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4201\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sunandita\",\"lastName\":\"Bose\",\"email\":\"<EMAIL>\",\"phone\":\"7828012913\",\"Phonenumbercode\":\"IMN_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"B74 4YD\",\"CurrentlyLive\":\"GBR\",\"GraduateFrom\":\"IND\",\"GAClientId\":\"1.1353945850\"},\"jobQuestions\":[{\"QuestionId\":\"3\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"2\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"191500\"}],\"ParentQuestionId\":3}]}]}",
    "FileFieldController": [
      {
        "Id": "a8dcca67-d560-40d1-a8a7-313c0dee66ed",
        "ChildItemId": "f55e36d3-9118-4b3f-94d1-61448c6088e8",
        "ParentItemId": "f7d55c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4201_Sunandita_Bose.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4201_sunandita_bose.docx?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-30T07:23:34.237Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "f7d55c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4201_Sunandita_Bose.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4201_sunandita_bose.docx?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "f7d55c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6020",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "***************",
    "SubmissionTime": "2025-07-30T07:23:34.2208646Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4201\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sunandita\",\"lastName\":\"Bose\",\"email\":\"<EMAIL>\",\"phone\":\"7828012913\",\"Phonenumbercode\":\"IMN_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"B74 4YD\",\"CurrentlyLive\":\"GBR\",\"GraduateFrom\":\"IND\",\"GAClientId\":\"1.1353945850\"},\"jobQuestions\":[{\"QuestionId\":\"3\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"2\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"191500\"}],\"ParentQuestionId\":3}]}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "a8dcca67-d560-40d1-a8a7-313c0dee66ed",
            "ChildItemId": "f55e36d3-9118-4b3f-94d1-61448c6088e8",
            "ParentItemId": "f7d55c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4201_Sunandita_Bose.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4201_sunandita_bose.docx?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-30T07:23:34.237Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "f7d55c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4201_Sunandita_Bose.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4201_sunandita_bose.docx?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (9e6dcef0)
2025-07-30T08:23:36.0502050+01:00 801864c5-0000-8700-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId f7d55c9e-cae7-6322-9971-ff0200979a84 (8f009123)
2025-07-30T08:23:36.4265839+01:00 801864c5-0000-8700-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-30T08:23:36.7075888+01:00 801864c5-0000-8700-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T08:23:36.7095763+01:00 801864c5-0000-8700-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T08:23:37.2049224+01:00 801864c5-0000-8700-b63f-84710c7967bb [INF] Received HTTP response headers after 489.902ms - 201 (f0742c1f)
2025-07-30T08:23:37.2059402+01:00 801864c5-0000-8700-b63f-84710c7967bb [INF] End processing HTTP request after 510.2987ms - 201 (7656b38e)
2025-07-30T08:23:37.2072662+01:00 801864c5-0000-8700-b63f-84710c7967bb [INF] (Webjob Application) Posted f7d55c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is True (96a36cf4)
2025-07-30T08:23:39.6840072+01:00 801864c5-0000-8700-b63f-84710c7967bb [INF] (Webjob Application) Posted f7d55c9e-cae7-6322-9971-ff0200979a84  (beea25c3)
2025-07-30T12:15:32.6279784+01:00  [WRN] Using an in-memory repository. Keys will not be persisted to storage. (28e83010)
2025-07-30T12:15:32.6314548+01:00  [WRN] Neither user profile nor HKLM registry available. Using an ephemeral key repository. Protected data will be unavailable when application exits. (54f66960)
2025-07-30T12:15:32.6754117+01:00  [WRN] No XML encryptor configured. Key {7101f932-1f01-47a6-a135-afcb088d318c} may be persisted to storage in unencrypted form. (9ca7e61e)
2025-07-30T12:15:32.7616398+01:00 80188b81-0000-8700-b63f-84710c7967bb [WRN] Failed to determine the https port for redirect. (ca76cc21)
2025-07-30T12:15:33.1529257+01:00 80188b81-0000-8700-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "bfe15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6021",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-30T11:15:31.4657572Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (41368337)
2025-07-30T12:15:33.2508122+01:00 80188b81-0000-8700-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "bfe15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6021",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-30T11:15:31.4657572Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (06640c78)
2025-07-30T12:15:33.2556884+01:00 80188b81-0000-8700-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId bfe15c9e-cae7-6322-9971-ff0200979a84 (ba06d459)
2025-07-30T12:15:33.3354653+01:00 80188b81-0000-8700-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T12:15:33.3367988+01:00 80188b81-0000-8700-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T12:15:35.2790893+01:00 80188b81-0000-8700-b63f-84710c7967bb [INF] Received HTTP response headers after 1937.0244ms - 500 (f0742c1f)
2025-07-30T12:15:35.2797877+01:00 80188b81-0000-8700-b63f-84710c7967bb [INF] End processing HTTP request after 1954.689ms - 500 (7656b38e)
2025-07-30T12:15:35.2836078+01:00 80188b81-0000-8700-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-30T12:15:35.2839737+01:00 80188b81-0000-8700-b63f-84710c7967bb [INF] (Webjob Application) Posted bfe15c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (5df9775a)
2025-07-30T12:15:37.8376541+01:00 80188b81-0000-8700-b63f-84710c7967bb [INF] (Webjob Application) Posted bfe15c9e-cae7-6322-9971-ff0200979a84  (b4fe2c32)
2025-07-30T12:15:38.9400415+01:00 8007896c-0002-9c00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "bfe15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6021",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-30T11:15:31.4657572Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (41368337)
2025-07-30T12:15:38.9410893+01:00 8007896c-0002-9c00-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "bfe15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6021",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-30T11:15:31.4657572Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (06640c78)
2025-07-30T12:15:38.9412145+01:00 8007896c-0002-9c00-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId bfe15c9e-cae7-6322-9971-ff0200979a84 (ba06d459)
2025-07-30T12:15:38.9415728+01:00 8007896c-0002-9c00-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T12:15:38.9416210+01:00 8007896c-0002-9c00-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T12:15:39.3772567+01:00 8007896c-0002-9c00-b63f-84710c7967bb [INF] Received HTTP response headers after 435.5405ms - 500 (f0742c1f)
2025-07-30T12:15:39.3773490+01:00 8007896c-0002-9c00-b63f-84710c7967bb [INF] End processing HTTP request after 435.8253ms - 500 (7656b38e)
2025-07-30T12:15:39.4015544+01:00 8007896c-0002-9c00-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-30T12:15:39.4016365+01:00 8007896c-0002-9c00-b63f-84710c7967bb [INF] (Webjob Application) Posted bfe15c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (5df9775a)
2025-07-30T12:15:39.4682042+01:00 8007896c-0002-9c00-b63f-84710c7967bb [INF] (Webjob Application) Posted bfe15c9e-cae7-6322-9971-ff0200979a84  (b4fe2c32)
2025-07-30T12:15:43.5095102+01:00 80188ba1-0000-8700-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "bfe15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6021",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-30T11:15:31.4657572Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (41368337)
2025-07-30T12:15:43.5098139+01:00 80188ba1-0000-8700-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "bfe15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6021",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-30T11:15:31.4657572Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (06640c78)
2025-07-30T12:15:43.5098609+01:00 80188ba1-0000-8700-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId bfe15c9e-cae7-6322-9971-ff0200979a84 (ba06d459)
2025-07-30T12:15:43.5100998+01:00 80188ba1-0000-8700-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T12:15:43.5101353+01:00 80188ba1-0000-8700-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T12:15:43.6485252+01:00 80188ba1-0000-8700-b63f-84710c7967bb [INF] Received HTTP response headers after 138.3141ms - 500 (f0742c1f)
2025-07-30T12:15:43.6485798+01:00 80188ba1-0000-8700-b63f-84710c7967bb [INF] End processing HTTP request after 138.5186ms - 500 (7656b38e)
2025-07-30T12:15:43.6499058+01:00 80188ba1-0000-8700-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-30T12:15:43.6499638+01:00 80188ba1-0000-8700-b63f-84710c7967bb [INF] (Webjob Application) Posted bfe15c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (5df9775a)
2025-07-30T12:15:43.7137716+01:00 80188ba1-0000-8700-b63f-84710c7967bb [INF] (Webjob Application) Posted bfe15c9e-cae7-6322-9971-ff0200979a84  (b4fe2c32)
2025-07-30T12:15:52.7846632+01:00 80101ebd-0000-6200-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "bfe15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6021",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-30T11:15:31.4657572Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (41368337)
2025-07-30T12:15:52.7849934+01:00 80101ebd-0000-6200-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "bfe15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6021",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-30T11:15:31.4657572Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (06640c78)
2025-07-30T12:15:52.7850472+01:00 80101ebd-0000-6200-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId bfe15c9e-cae7-6322-9971-ff0200979a84 (ba06d459)
2025-07-30T12:15:52.7852635+01:00 80101ebd-0000-6200-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T12:15:52.7852894+01:00 80101ebd-0000-6200-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T12:15:52.9339395+01:00 80101ebd-0000-6200-b63f-84710c7967bb [INF] Received HTTP response headers after 148.5723ms - 500 (f0742c1f)
2025-07-30T12:15:52.9340038+01:00 80101ebd-0000-6200-b63f-84710c7967bb [INF] End processing HTTP request after 148.7793ms - 500 (7656b38e)
2025-07-30T12:15:52.9343203+01:00 80101ebd-0000-6200-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-30T12:15:52.9343587+01:00 80101ebd-0000-6200-b63f-84710c7967bb [INF] (Webjob Application) Posted bfe15c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (5df9775a)
2025-07-30T12:15:53.0051715+01:00 80101ebd-0000-6200-b63f-84710c7967bb [INF] (Webjob Application) Posted bfe15c9e-cae7-6322-9971-ff0200979a84  (b4fe2c32)
2025-07-30T12:16:06.5399650+01:00 801a1f29-0001-ae00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "10e25c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6022",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:16:06.3721771Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (c44e4ca7)
2025-07-30T12:16:06.5402730+01:00 801a1f29-0001-ae00-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "10e25c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6022",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:16:06.3721771Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (0e981509)
2025-07-30T12:16:06.5403406+01:00 801a1f29-0001-ae00-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 10e25c9e-cae7-6322-9971-ff0200979a84 (fcfc4853)
2025-07-30T12:16:06.5405868+01:00 801a1f29-0001-ae00-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T12:16:06.5406119+01:00 801a1f29-0001-ae00-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T12:16:06.7634381+01:00 801a1f29-0001-ae00-b63f-84710c7967bb [INF] Received HTTP response headers after 222.7662ms - 500 (f0742c1f)
2025-07-30T12:16:06.7634913+01:00 801a1f29-0001-ae00-b63f-84710c7967bb [INF] End processing HTTP request after 222.9442ms - 500 (7656b38e)
2025-07-30T12:16:06.7637973+01:00 801a1f29-0001-ae00-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-30T12:16:06.7638345+01:00 801a1f29-0001-ae00-b63f-84710c7967bb [INF] (Webjob Application) Posted 10e25c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (f1f781bd)
2025-07-30T12:16:06.8628764+01:00 801a1f29-0001-ae00-b63f-84710c7967bb [INF] (Webjob Application) Posted 10e25c9e-cae7-6322-9971-ff0200979a84  (cb8fef88)
2025-07-30T12:16:07.9430542+01:00 8007a5ea-0001-7b00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "10e25c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6022",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:16:06.3721771Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (c44e4ca7)
2025-07-30T12:16:07.9432867+01:00 8007a5ea-0001-7b00-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "10e25c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6022",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:16:06.3721771Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (0e981509)
2025-07-30T12:16:07.9433364+01:00 8007a5ea-0001-7b00-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 10e25c9e-cae7-6322-9971-ff0200979a84 (fcfc4853)
2025-07-30T12:16:07.9435498+01:00 8007a5ea-0001-7b00-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T12:16:07.9435915+01:00 8007a5ea-0001-7b00-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T12:16:08.1424849+01:00 8007a5ea-0001-7b00-b63f-84710c7967bb [INF] Received HTTP response headers after 198.8338ms - 500 (f0742c1f)
2025-07-30T12:16:08.1425385+01:00 8007a5ea-0001-7b00-b63f-84710c7967bb [INF] End processing HTTP request after 199.0253ms - 500 (7656b38e)
2025-07-30T12:16:08.1429424+01:00 8007a5ea-0001-7b00-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-30T12:16:08.1429736+01:00 8007a5ea-0001-7b00-b63f-84710c7967bb [INF] (Webjob Application) Posted 10e25c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (f1f781bd)
2025-07-30T12:16:08.2152291+01:00 8007a5ea-0001-7b00-b63f-84710c7967bb [INF] (Webjob Application) Posted 10e25c9e-cae7-6322-9971-ff0200979a84  (cb8fef88)
2025-07-30T12:16:09.2846096+01:00 801e72ec-0003-9d00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "bfe15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6021",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-30T11:15:31.4657572Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (41368337)
2025-07-30T12:16:09.2848496+01:00 801e72ec-0003-9d00-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "bfe15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6021",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-30T11:15:31.4657572Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (06640c78)
2025-07-30T12:16:09.2848859+01:00 801e72ec-0003-9d00-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId bfe15c9e-cae7-6322-9971-ff0200979a84 (ba06d459)
2025-07-30T12:16:09.2850728+01:00 801e72ec-0003-9d00-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T12:16:09.2851057+01:00 801e72ec-0003-9d00-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T12:16:09.6811494+01:00 801e72ec-0003-9d00-b63f-84710c7967bb [INF] Received HTTP response headers after 395.9824ms - 500 (f0742c1f)
2025-07-30T12:16:09.6812200+01:00 801e72ec-0003-9d00-b63f-84710c7967bb [INF] End processing HTTP request after 396.1767ms - 500 (7656b38e)
2025-07-30T12:16:09.6826006+01:00 801e72ec-0003-9d00-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-30T12:16:09.6826431+01:00 801e72ec-0003-9d00-b63f-84710c7967bb [INF] (Webjob Application) Posted bfe15c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (5df9775a)
2025-07-30T12:16:09.7783182+01:00 801e72ec-0003-9d00-b63f-84710c7967bb [INF] (Webjob Application) Posted bfe15c9e-cae7-6322-9971-ff0200979a84  (b4fe2c32)
2025-07-30T12:16:12.2774162+01:00 8000327e-0002-8100-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "10e25c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6022",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:16:06.3721771Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (c44e4ca7)
2025-07-30T12:16:12.3031777+01:00 8000327e-0002-8100-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "10e25c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6022",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:16:06.3721771Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (0e981509)
2025-07-30T12:16:12.3032606+01:00 8000327e-0002-8100-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 10e25c9e-cae7-6322-9971-ff0200979a84 (fcfc4853)
2025-07-30T12:16:12.3034255+01:00 8000327e-0002-8100-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T12:16:12.3034480+01:00 8000327e-0002-8100-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T12:16:12.4620787+01:00 8000327e-0002-8100-b63f-84710c7967bb [INF] Received HTTP response headers after 158.5671ms - 500 (f0742c1f)
2025-07-30T12:16:12.4621353+01:00 8000327e-0002-8100-b63f-84710c7967bb [INF] End processing HTTP request after 158.7416ms - 500 (7656b38e)
2025-07-30T12:16:12.4625100+01:00 8000327e-0002-8100-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-30T12:16:12.4625449+01:00 8000327e-0002-8100-b63f-84710c7967bb [INF] (Webjob Application) Posted 10e25c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (f1f781bd)
2025-07-30T12:16:12.5307728+01:00 8000327e-0002-8100-b63f-84710c7967bb [INF] (Webjob Application) Posted 10e25c9e-cae7-6322-9971-ff0200979a84  (cb8fef88)
2025-07-30T12:16:21.5660638+01:00 8007a62c-0001-7b00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "10e25c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6022",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:16:06.3721771Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (c44e4ca7)
2025-07-30T12:16:21.5661894+01:00 8007a62c-0001-7b00-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "10e25c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6022",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:16:06.3721771Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (0e981509)
2025-07-30T12:16:21.5662144+01:00 8007a62c-0001-7b00-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 10e25c9e-cae7-6322-9971-ff0200979a84 (fcfc4853)
2025-07-30T12:16:21.5665143+01:00 8007a62c-0001-7b00-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T12:16:21.5665572+01:00 8007a62c-0001-7b00-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T12:16:21.7896959+01:00 8007a62c-0001-7b00-b63f-84710c7967bb [INF] Received HTTP response headers after 223.0789ms - 500 (f0742c1f)
2025-07-30T12:16:21.7897559+01:00 8007a62c-0001-7b00-b63f-84710c7967bb [INF] End processing HTTP request after 223.2749ms - 500 (7656b38e)
2025-07-30T12:16:21.7902268+01:00 8007a62c-0001-7b00-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-30T12:16:21.7902512+01:00 8007a62c-0001-7b00-b63f-84710c7967bb [INF] (Webjob Application) Posted 10e25c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (f1f781bd)
2025-07-30T12:16:21.8819690+01:00 8007a62c-0001-7b00-b63f-84710c7967bb [INF] (Webjob Application) Posted 10e25c9e-cae7-6322-9971-ff0200979a84  (cb8fef88)
2025-07-30T12:16:37.9799925+01:00 804b8265-0001-b900-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "10e25c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6022",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:16:06.3721771Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (c44e4ca7)
2025-07-30T12:16:37.9801213+01:00 804b8265-0001-b900-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "10e25c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6022",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:16:06.3721771Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (0e981509)
2025-07-30T12:16:37.9801451+01:00 804b8265-0001-b900-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 10e25c9e-cae7-6322-9971-ff0200979a84 (fcfc4853)
2025-07-30T12:16:37.9803619+01:00 804b8265-0001-b900-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T12:16:37.9803869+01:00 804b8265-0001-b900-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T12:16:38.1416048+01:00 804b8265-0001-b900-b63f-84710c7967bb [INF] Received HTTP response headers after 161.1635ms - 500 (f0742c1f)
2025-07-30T12:16:38.1416457+01:00 804b8265-0001-b900-b63f-84710c7967bb [INF] End processing HTTP request after 161.3272ms - 500 (7656b38e)
2025-07-30T12:16:38.1446078+01:00 804b8265-0001-b900-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-30T12:16:38.1446771+01:00 804b8265-0001-b900-b63f-84710c7967bb [INF] (Webjob Application) Posted 10e25c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (f1f781bd)
2025-07-30T12:16:38.2162823+01:00 804b8265-0001-b900-b63f-84710c7967bb [INF] (Webjob Application) Posted 10e25c9e-cae7-6322-9971-ff0200979a84  (cb8fef88)
2025-07-30T12:16:55.4689217+01:00 80120649-0000-6700-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "c1e15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6023",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-30T11:16:55.3713216Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (d3a50512)
2025-07-30T12:16:55.4691397+01:00 80120649-0000-6700-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "c1e15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6023",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-30T11:16:55.3713216Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (93ff9996)
2025-07-30T12:16:55.4691740+01:00 80120649-0000-6700-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId c1e15c9e-cae7-6322-9971-ff0200979a84 (89fe3b1e)
2025-07-30T12:16:55.4693490+01:00 80120649-0000-6700-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T12:16:55.4693750+01:00 80120649-0000-6700-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T12:16:55.7222457+01:00 80120649-0000-6700-b63f-84710c7967bb [INF] Received HTTP response headers after 252.8175ms - 500 (f0742c1f)
2025-07-30T12:16:55.7222919+01:00 80120649-0000-6700-b63f-84710c7967bb [INF] End processing HTTP request after 252.9715ms - 500 (7656b38e)
2025-07-30T12:16:55.7327210+01:00 80120649-0000-6700-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-30T12:16:55.7327889+01:00 80120649-0000-6700-b63f-84710c7967bb [INF] (Webjob Application) Posted c1e15c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (26b7b20e)
2025-07-30T12:16:55.7949549+01:00 80120649-0000-6700-b63f-84710c7967bb [INF] (Webjob Application) Posted c1e15c9e-cae7-6322-9971-ff0200979a84  (0ceedc85)
2025-07-30T12:16:56.8760756+01:00 801e73a0-0003-9d00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "c1e15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6023",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-30T11:16:55.3713216Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (d3a50512)
2025-07-30T12:16:56.8762183+01:00 801e73a0-0003-9d00-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "c1e15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6023",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-30T11:16:55.3713216Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (93ff9996)
2025-07-30T12:16:56.8762430+01:00 801e73a0-0003-9d00-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId c1e15c9e-cae7-6322-9971-ff0200979a84 (89fe3b1e)
2025-07-30T12:16:56.8763974+01:00 801e73a0-0003-9d00-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T12:16:56.8764207+01:00 801e73a0-0003-9d00-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T12:16:57.1426570+01:00 801e73a0-0003-9d00-b63f-84710c7967bb [INF] Received HTTP response headers after 266.187ms - 500 (f0742c1f)
2025-07-30T12:16:57.1426995+01:00 801e73a0-0003-9d00-b63f-84710c7967bb [INF] End processing HTTP request after 266.3321ms - 500 (7656b38e)
2025-07-30T12:16:57.1452888+01:00 801e73a0-0003-9d00-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-30T12:16:57.1453264+01:00 801e73a0-0003-9d00-b63f-84710c7967bb [INF] (Webjob Application) Posted c1e15c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (26b7b20e)
2025-07-30T12:16:57.2262746+01:00 801e73a0-0003-9d00-b63f-84710c7967bb [INF] (Webjob Application) Posted c1e15c9e-cae7-6322-9971-ff0200979a84  (0ceedc85)
2025-07-30T12:17:01.2854136+01:00 802c72da-0002-d600-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "c1e15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6023",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-30T11:16:55.3713216Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (d3a50512)
2025-07-30T12:17:01.2855389+01:00 802c72da-0002-d600-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "c1e15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6023",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-30T11:16:55.3713216Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (93ff9996)
2025-07-30T12:17:01.2855640+01:00 802c72da-0002-d600-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId c1e15c9e-cae7-6322-9971-ff0200979a84 (89fe3b1e)
2025-07-30T12:17:01.2857134+01:00 802c72da-0002-d600-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T12:17:01.2857368+01:00 802c72da-0002-d600-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T12:17:01.6019361+01:00 802c72da-0002-d600-b63f-84710c7967bb [INF] Received HTTP response headers after 316.142ms - 500 (f0742c1f)
2025-07-30T12:17:01.6019840+01:00 802c72da-0002-d600-b63f-84710c7967bb [INF] End processing HTTP request after 316.2956ms - 500 (7656b38e)
2025-07-30T12:17:01.6083469+01:00 802c72da-0002-d600-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-30T12:17:01.6083950+01:00 802c72da-0002-d600-b63f-84710c7967bb [INF] (Webjob Application) Posted c1e15c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (26b7b20e)
2025-07-30T12:17:01.7431256+01:00 802c72da-0002-d600-b63f-84710c7967bb [INF] (Webjob Application) Posted c1e15c9e-cae7-6322-9971-ff0200979a84  (0ceedc85)
2025-07-30T12:17:10.8004120+01:00 802c735e-0002-d600-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "c1e15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6023",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-30T11:16:55.3713216Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (d3a50512)
2025-07-30T12:17:10.8005417+01:00 802c735e-0002-d600-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "c1e15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6023",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-30T11:16:55.3713216Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (93ff9996)
2025-07-30T12:17:10.8005653+01:00 802c735e-0002-d600-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId c1e15c9e-cae7-6322-9971-ff0200979a84 (89fe3b1e)
2025-07-30T12:17:10.8007216+01:00 802c735e-0002-d600-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T12:17:10.8007459+01:00 802c735e-0002-d600-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T12:17:11.0461499+01:00 802c735e-0002-d600-b63f-84710c7967bb [INF] Received HTTP response headers after 245.3445ms - 500 (f0742c1f)
2025-07-30T12:17:11.0461937+01:00 802c735e-0002-d600-b63f-84710c7967bb [INF] End processing HTTP request after 245.5052ms - 500 (7656b38e)
2025-07-30T12:17:11.0511220+01:00 802c735e-0002-d600-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-30T12:17:11.0511597+01:00 802c735e-0002-d600-b63f-84710c7967bb [INF] (Webjob Application) Posted c1e15c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (26b7b20e)
2025-07-30T12:17:11.1392376+01:00 802c735e-0002-d600-b63f-84710c7967bb [INF] (Webjob Application) Posted c1e15c9e-cae7-6322-9971-ff0200979a84  (0ceedc85)
2025-07-30T12:17:27.1814466+01:00 801e292c-0003-c200-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "c1e15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6023",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-30T11:16:55.3713216Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (d3a50512)
2025-07-30T12:17:27.1817056+01:00 801e292c-0003-c200-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "c1e15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6023",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-30T11:16:55.3713216Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (93ff9996)
2025-07-30T12:17:27.1817500+01:00 801e292c-0003-c200-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId c1e15c9e-cae7-6322-9971-ff0200979a84 (89fe3b1e)
2025-07-30T12:17:27.1819606+01:00 801e292c-0003-c200-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T12:17:27.1821066+01:00 801e292c-0003-c200-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T12:17:27.4372594+01:00 801e292c-0003-c200-b63f-84710c7967bb [INF] Received HTTP response headers after 255.0928ms - 500 (f0742c1f)
2025-07-30T12:17:27.4373008+01:00 801e292c-0003-c200-b63f-84710c7967bb [INF] End processing HTTP request after 255.3779ms - 500 (7656b38e)
2025-07-30T12:17:27.4469209+01:00 801e292c-0003-c200-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-30T12:17:27.4469612+01:00 801e292c-0003-c200-b63f-84710c7967bb [INF] (Webjob Application) Posted c1e15c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (26b7b20e)
2025-07-30T12:17:27.5190414+01:00 801e292c-0003-c200-b63f-84710c7967bb [INF] (Webjob Application) Posted c1e15c9e-cae7-6322-9971-ff0200979a84  (0ceedc85)
2025-07-30T12:17:47.9916790+01:00 8014f07a-0001-a700-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "12e25c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6024",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:17:47.8900254Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (4e763010)
2025-07-30T12:17:47.9920175+01:00 8014f07a-0001-a700-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "12e25c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6024",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:17:47.8900254Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (6d7baa74)
2025-07-30T12:17:47.9921041+01:00 8014f07a-0001-a700-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 12e25c9e-cae7-6322-9971-ff0200979a84 (2b2ead25)
2025-07-30T12:17:47.9927245+01:00 8014f07a-0001-a700-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T12:17:47.9927631+01:00 8014f07a-0001-a700-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T12:17:48.2420249+01:00 8014f07a-0001-a700-b63f-84710c7967bb [INF] Received HTTP response headers after 249.1826ms - 500 (f0742c1f)
2025-07-30T12:17:48.2420823+01:00 8014f07a-0001-a700-b63f-84710c7967bb [INF] End processing HTTP request after 249.418ms - 500 (7656b38e)
2025-07-30T12:17:48.2445017+01:00 8014f07a-0001-a700-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-30T12:17:48.2445487+01:00 8014f07a-0001-a700-b63f-84710c7967bb [INF] (Webjob Application) Posted 12e25c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (f9446bd9)
2025-07-30T12:17:48.3517684+01:00 8014f07a-0001-a700-b63f-84710c7967bb [INF] (Webjob Application) Posted 12e25c9e-cae7-6322-9971-ff0200979a84  (26e0fb47)
2025-07-30T12:17:49.3993957+01:00 802fb043-0002-f200-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "12e25c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6024",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:17:47.8900254Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (4e763010)
2025-07-30T12:17:49.3996323+01:00 802fb043-0002-f200-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "12e25c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6024",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:17:47.8900254Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (6d7baa74)
2025-07-30T12:17:49.3996650+01:00 802fb043-0002-f200-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 12e25c9e-cae7-6322-9971-ff0200979a84 (2b2ead25)
2025-07-30T12:17:49.3998213+01:00 802fb043-0002-f200-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T12:17:49.3998391+01:00 802fb043-0002-f200-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T12:17:49.6503892+01:00 802fb043-0002-f200-b63f-84710c7967bb [INF] Received HTTP response headers after 250.4911ms - 500 (f0742c1f)
2025-07-30T12:17:49.6504440+01:00 802fb043-0002-f200-b63f-84710c7967bb [INF] End processing HTTP request after 250.6476ms - 500 (7656b38e)
2025-07-30T12:17:49.6542129+01:00 802fb043-0002-f200-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-30T12:17:49.6542601+01:00 802fb043-0002-f200-b63f-84710c7967bb [INF] (Webjob Application) Posted 12e25c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (f9446bd9)
2025-07-30T12:17:49.7382270+01:00 802fb043-0002-f200-b63f-84710c7967bb [INF] (Webjob Application) Posted 12e25c9e-cae7-6322-9971-ff0200979a84  (26e0fb47)
2025-07-30T12:17:53.7868197+01:00 801e29fe-0003-c200-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "12e25c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6024",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:17:47.8900254Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (4e763010)
2025-07-30T12:17:53.7870203+01:00 801e29fe-0003-c200-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "12e25c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6024",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:17:47.8900254Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (6d7baa74)
2025-07-30T12:17:53.7870516+01:00 801e29fe-0003-c200-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 12e25c9e-cae7-6322-9971-ff0200979a84 (2b2ead25)
2025-07-30T12:17:53.7872552+01:00 801e29fe-0003-c200-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T12:17:53.7872974+01:00 801e29fe-0003-c200-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T12:17:53.9459065+01:00 801e29fe-0003-c200-b63f-84710c7967bb [INF] Received HTTP response headers after 158.5488ms - 500 (f0742c1f)
2025-07-30T12:17:53.9459616+01:00 801e29fe-0003-c200-b63f-84710c7967bb [INF] End processing HTTP request after 158.7528ms - 500 (7656b38e)
2025-07-30T12:17:53.9467551+01:00 801e29fe-0003-c200-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-30T12:17:53.9467871+01:00 801e29fe-0003-c200-b63f-84710c7967bb [INF] (Webjob Application) Posted 12e25c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (f9446bd9)
2025-07-30T12:17:54.0492982+01:00 801e29fe-0003-c200-b63f-84710c7967bb [INF] (Webjob Application) Posted 12e25c9e-cae7-6322-9971-ff0200979a84  (26e0fb47)
2025-07-30T12:18:03.0909827+01:00 8009ffbb-0001-a600-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "12e25c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6024",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:17:47.8900254Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (4e763010)
2025-07-30T12:18:03.0911024+01:00 8009ffbb-0001-a600-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "12e25c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6024",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:17:47.8900254Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (6d7baa74)
2025-07-30T12:18:03.0911231+01:00 8009ffbb-0001-a600-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 12e25c9e-cae7-6322-9971-ff0200979a84 (2b2ead25)
2025-07-30T12:18:03.0913194+01:00 8009ffbb-0001-a600-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T12:18:03.0913504+01:00 8009ffbb-0001-a600-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T12:18:03.4332106+01:00 8009ffbb-0001-a600-b63f-84710c7967bb [INF] Received HTTP response headers after 341.8082ms - 500 (f0742c1f)
2025-07-30T12:18:03.4332527+01:00 8009ffbb-0001-a600-b63f-84710c7967bb [INF] End processing HTTP request after 341.9726ms - 500 (7656b38e)
2025-07-30T12:18:03.4342221+01:00 8009ffbb-0001-a600-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-30T12:18:03.4342715+01:00 8009ffbb-0001-a600-b63f-84710c7967bb [INF] (Webjob Application) Posted 12e25c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (f9446bd9)
2025-07-30T12:18:03.5611401+01:00 8009ffbb-0001-a600-b63f-84710c7967bb [INF] (Webjob Application) Posted 12e25c9e-cae7-6322-9971-ff0200979a84  (26e0fb47)
2025-07-30T12:18:19.6136210+01:00 8007a798-0001-7b00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "12e25c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6024",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:17:47.8900254Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (4e763010)
2025-07-30T12:18:19.6137429+01:00 8007a798-0001-7b00-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "12e25c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6024",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:17:47.8900254Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4226\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Leah\",\"lastName\":\"Murray\",\"email\":\"<EMAIL>\",\"phone\":\"07809254066\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Hu115qd\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (6d7baa74)
2025-07-30T12:18:19.6137642+01:00 8007a798-0001-7b00-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 12e25c9e-cae7-6322-9971-ff0200979a84 (2b2ead25)
2025-07-30T12:18:19.6139550+01:00 8007a798-0001-7b00-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T12:18:19.6139878+01:00 8007a798-0001-7b00-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T12:18:19.8581405+01:00 8007a798-0001-7b00-b63f-84710c7967bb [INF] Received HTTP response headers after 244.1039ms - 500 (f0742c1f)
2025-07-30T12:18:19.8581875+01:00 8007a798-0001-7b00-b63f-84710c7967bb [INF] End processing HTTP request after 244.2708ms - 500 (7656b38e)
2025-07-30T12:18:19.8599119+01:00 8007a798-0001-7b00-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-30T12:18:19.8599659+01:00 8007a798-0001-7b00-b63f-84710c7967bb [INF] (Webjob Application) Posted 12e25c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (f9446bd9)
2025-07-30T12:18:19.9867447+01:00 8007a798-0001-7b00-b63f-84710c7967bb [INF] (Webjob Application) Posted 12e25c9e-cae7-6322-9971-ff0200979a84  (26e0fb47)
2025-07-30T12:31:57.4923636+01:00 8009143b-0003-7800-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4174\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Marynel\",\"lastName\":\"Cleaver\",\"email\":\"<EMAIL>\",\"phone\":\"07399928524\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"B98 7HS\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "ef3566ee-68a4-45a3-bdc4-37361ddab7bf",
        "ChildItemId": "39ec1411-42b6-4f40-b4bf-7e938018fd1c",
        "ParentItemId": "c8e15c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4174_Marynel_Cleaver.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-30T11:31:57.3Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "c8e15c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4174_Marynel_Cleaver.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "c8e15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6025",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:31:57.2857061Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4174\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Marynel\",\"lastName\":\"Cleaver\",\"email\":\"<EMAIL>\",\"phone\":\"07399928524\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"B98 7HS\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "ef3566ee-68a4-45a3-bdc4-37361ddab7bf",
            "ChildItemId": "39ec1411-42b6-4f40-b4bf-7e938018fd1c",
            "ParentItemId": "c8e15c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4174_Marynel_Cleaver.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-30T11:31:57.3Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "c8e15c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4174_Marynel_Cleaver.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (3ed9b0ab)
2025-07-30T12:31:57.4926366+01:00 8009143b-0003-7800-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4174\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Marynel\",\"lastName\":\"Cleaver\",\"email\":\"<EMAIL>\",\"phone\":\"07399928524\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"B98 7HS\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "ef3566ee-68a4-45a3-bdc4-37361ddab7bf",
        "ChildItemId": "39ec1411-42b6-4f40-b4bf-7e938018fd1c",
        "ParentItemId": "c8e15c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4174_Marynel_Cleaver.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-30T11:31:57.3Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "c8e15c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4174_Marynel_Cleaver.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "c8e15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6025",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:31:57.2857061Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4174\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Marynel\",\"lastName\":\"Cleaver\",\"email\":\"<EMAIL>\",\"phone\":\"07399928524\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"B98 7HS\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "ef3566ee-68a4-45a3-bdc4-37361ddab7bf",
            "ChildItemId": "39ec1411-42b6-4f40-b4bf-7e938018fd1c",
            "ParentItemId": "c8e15c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4174_Marynel_Cleaver.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-30T11:31:57.3Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "c8e15c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4174_Marynel_Cleaver.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (195be2a3)
2025-07-30T12:31:57.5273741+01:00 8009143b-0003-7800-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId c8e15c9e-cae7-6322-9971-ff0200979a84 (5dde666d)
2025-07-30T12:31:57.8948926+01:00 8009143b-0003-7800-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-30T12:31:58.2068939+01:00 8009143b-0003-7800-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T12:31:58.2069309+01:00 8009143b-0003-7800-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T12:32:01.6597696+01:00 8009143b-0003-7800-b63f-84710c7967bb [INF] Received HTTP response headers after 3452.8141ms - 201 (f0742c1f)
2025-07-30T12:32:01.6598143+01:00 8009143b-0003-7800-b63f-84710c7967bb [INF] End processing HTTP request after 3453.004ms - 201 (7656b38e)
2025-07-30T12:32:01.6635769+01:00 8009143b-0003-7800-b63f-84710c7967bb [INF] (Webjob Application) Posted c8e15c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is True (fd9f9489)
2025-07-30T12:32:01.7834206+01:00 8009143b-0003-7800-b63f-84710c7967bb [INF] (Webjob Application) Posted c8e15c9e-cae7-6322-9971-ff0200979a84  (40dc6d2b)
2025-07-30T12:32:59.9628701+01:00 80006738-0003-8b00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4174\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Marynel\",\"lastName\":\"Cleaver\",\"email\":\"<EMAIL>\",\"phone\":\"07399928524\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"B98 7HS\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "8bbc64ba-6970-4a27-bcec-b3c4a472e2ae",
        "ChildItemId": "39ec1411-42b6-4f40-b4bf-7e938018fd1c",
        "ParentItemId": "c9e15c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4174_Marynel_Cleaver.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-30T11:32:59.867Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "c9e15c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4174_Marynel_Cleaver.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "c9e15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6026",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:32:59.8514659Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4174\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Marynel\",\"lastName\":\"Cleaver\",\"email\":\"<EMAIL>\",\"phone\":\"07399928524\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"B98 7HS\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "8bbc64ba-6970-4a27-bcec-b3c4a472e2ae",
            "ChildItemId": "39ec1411-42b6-4f40-b4bf-7e938018fd1c",
            "ParentItemId": "c9e15c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4174_Marynel_Cleaver.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-30T11:32:59.867Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "c9e15c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4174_Marynel_Cleaver.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (8e4295a7)
2025-07-30T12:32:59.9630551+01:00 80006738-0003-8b00-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4174\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Marynel\",\"lastName\":\"Cleaver\",\"email\":\"<EMAIL>\",\"phone\":\"07399928524\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"B98 7HS\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "8bbc64ba-6970-4a27-bcec-b3c4a472e2ae",
        "ChildItemId": "39ec1411-42b6-4f40-b4bf-7e938018fd1c",
        "ParentItemId": "c9e15c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4174_Marynel_Cleaver.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-30T11:32:59.867Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "c9e15c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4174_Marynel_Cleaver.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "c9e15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6026",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:32:59.8514659Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4174\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Marynel\",\"lastName\":\"Cleaver\",\"email\":\"<EMAIL>\",\"phone\":\"07399928524\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"B98 7HS\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "8bbc64ba-6970-4a27-bcec-b3c4a472e2ae",
            "ChildItemId": "39ec1411-42b6-4f40-b4bf-7e938018fd1c",
            "ParentItemId": "c9e15c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4174_Marynel_Cleaver.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-30T11:32:59.867Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "c9e15c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4174_Marynel_Cleaver.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (1da33296)
2025-07-30T12:32:59.9631666+01:00 80006738-0003-8b00-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId c9e15c9e-cae7-6322-9971-ff0200979a84 (e5059bcc)
2025-07-30T12:33:00.2625555+01:00 80006738-0003-8b00-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-30T12:33:00.4680055+01:00 80006738-0003-8b00-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T12:33:00.4680455+01:00 80006738-0003-8b00-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T12:33:00.7906692+01:00 80006738-0003-8b00-b63f-84710c7967bb [INF] Received HTTP response headers after 322.5743ms - 500 (f0742c1f)
2025-07-30T12:33:00.7907268+01:00 80006738-0003-8b00-b63f-84710c7967bb [INF] End processing HTTP request after 322.7716ms - 500 (7656b38e)
2025-07-30T12:33:00.7911617+01:00 80006738-0003-8b00-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-30T12:33:00.7912000+01:00 80006738-0003-8b00-b63f-84710c7967bb [INF] (Webjob Application) Posted c9e15c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (66072889)
2025-07-30T12:33:00.8607556+01:00 80006738-0003-8b00-b63f-84710c7967bb [INF] (Webjob Application) Posted c9e15c9e-cae7-6322-9971-ff0200979a84  (1dd45396)
2025-07-30T12:33:02.0304922+01:00 801a2827-0001-ae00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4174\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Marynel\",\"lastName\":\"Cleaver\",\"email\":\"<EMAIL>\",\"phone\":\"07399928524\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"B98 7HS\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "8bbc64ba-6970-4a27-bcec-b3c4a472e2ae",
        "ChildItemId": "39ec1411-42b6-4f40-b4bf-7e938018fd1c",
        "ParentItemId": "c9e15c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4174_Marynel_Cleaver.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-30T11:32:59.867Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "c9e15c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4174_Marynel_Cleaver.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "c9e15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6026",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:32:59.8514659Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4174\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Marynel\",\"lastName\":\"Cleaver\",\"email\":\"<EMAIL>\",\"phone\":\"07399928524\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"B98 7HS\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "8bbc64ba-6970-4a27-bcec-b3c4a472e2ae",
            "ChildItemId": "39ec1411-42b6-4f40-b4bf-7e938018fd1c",
            "ParentItemId": "c9e15c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4174_Marynel_Cleaver.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-30T11:32:59.867Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "c9e15c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4174_Marynel_Cleaver.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (8e4295a7)
2025-07-30T12:33:02.0306912+01:00 801a2827-0001-ae00-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4174\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Marynel\",\"lastName\":\"Cleaver\",\"email\":\"<EMAIL>\",\"phone\":\"07399928524\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"B98 7HS\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "8bbc64ba-6970-4a27-bcec-b3c4a472e2ae",
        "ChildItemId": "39ec1411-42b6-4f40-b4bf-7e938018fd1c",
        "ParentItemId": "c9e15c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4174_Marynel_Cleaver.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-30T11:32:59.867Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "c9e15c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4174_Marynel_Cleaver.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "c9e15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6026",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:32:59.8514659Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4174\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Marynel\",\"lastName\":\"Cleaver\",\"email\":\"<EMAIL>\",\"phone\":\"07399928524\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"B98 7HS\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "8bbc64ba-6970-4a27-bcec-b3c4a472e2ae",
            "ChildItemId": "39ec1411-42b6-4f40-b4bf-7e938018fd1c",
            "ParentItemId": "c9e15c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4174_Marynel_Cleaver.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-30T11:32:59.867Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "c9e15c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4174_Marynel_Cleaver.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (1da33296)
2025-07-30T12:33:02.0308237+01:00 801a2827-0001-ae00-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId c9e15c9e-cae7-6322-9971-ff0200979a84 (e5059bcc)
2025-07-30T12:33:02.1984809+01:00 801a2827-0001-ae00-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-30T12:33:02.4366117+01:00 801a2827-0001-ae00-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T12:33:02.4366487+01:00 801a2827-0001-ae00-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T12:33:02.7257547+01:00 801a2827-0001-ae00-b63f-84710c7967bb [INF] Received HTTP response headers after 289.0489ms - 500 (f0742c1f)
2025-07-30T12:33:02.7257960+01:00 801a2827-0001-ae00-b63f-84710c7967bb [INF] End processing HTTP request after 289.2598ms - 500 (7656b38e)
2025-07-30T12:33:02.7281950+01:00 801a2827-0001-ae00-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-30T12:33:02.7282335+01:00 801a2827-0001-ae00-b63f-84710c7967bb [INF] (Webjob Application) Posted c9e15c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (66072889)
2025-07-30T12:33:02.7936191+01:00 801a2827-0001-ae00-b63f-84710c7967bb [INF] (Webjob Application) Posted c9e15c9e-cae7-6322-9971-ff0200979a84  (1dd45396)
2025-07-30T12:33:06.8720443+01:00 800efcf7-0002-7a00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4174\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Marynel\",\"lastName\":\"Cleaver\",\"email\":\"<EMAIL>\",\"phone\":\"07399928524\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"B98 7HS\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "8bbc64ba-6970-4a27-bcec-b3c4a472e2ae",
        "ChildItemId": "39ec1411-42b6-4f40-b4bf-7e938018fd1c",
        "ParentItemId": "c9e15c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4174_Marynel_Cleaver.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-30T11:32:59.867Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "c9e15c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4174_Marynel_Cleaver.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "c9e15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6026",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:32:59.8514659Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4174\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Marynel\",\"lastName\":\"Cleaver\",\"email\":\"<EMAIL>\",\"phone\":\"07399928524\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"B98 7HS\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "8bbc64ba-6970-4a27-bcec-b3c4a472e2ae",
            "ChildItemId": "39ec1411-42b6-4f40-b4bf-7e938018fd1c",
            "ParentItemId": "c9e15c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4174_Marynel_Cleaver.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-30T11:32:59.867Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "c9e15c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4174_Marynel_Cleaver.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (8e4295a7)
2025-07-30T12:33:06.8722091+01:00 800efcf7-0002-7a00-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4174\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Marynel\",\"lastName\":\"Cleaver\",\"email\":\"<EMAIL>\",\"phone\":\"07399928524\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"B98 7HS\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "8bbc64ba-6970-4a27-bcec-b3c4a472e2ae",
        "ChildItemId": "39ec1411-42b6-4f40-b4bf-7e938018fd1c",
        "ParentItemId": "c9e15c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4174_Marynel_Cleaver.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-30T11:32:59.867Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "c9e15c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4174_Marynel_Cleaver.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "c9e15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6026",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:32:59.8514659Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4174\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Marynel\",\"lastName\":\"Cleaver\",\"email\":\"<EMAIL>\",\"phone\":\"07399928524\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"B98 7HS\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "8bbc64ba-6970-4a27-bcec-b3c4a472e2ae",
            "ChildItemId": "39ec1411-42b6-4f40-b4bf-7e938018fd1c",
            "ParentItemId": "c9e15c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4174_Marynel_Cleaver.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-30T11:32:59.867Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "c9e15c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4174_Marynel_Cleaver.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (1da33296)
2025-07-30T12:33:06.8723145+01:00 800efcf7-0002-7a00-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId c9e15c9e-cae7-6322-9971-ff0200979a84 (e5059bcc)
2025-07-30T12:33:07.0487545+01:00 800efcf7-0002-7a00-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-30T12:33:07.1329309+01:00 800efcf7-0002-7a00-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T12:33:07.1329692+01:00 800efcf7-0002-7a00-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T12:33:07.5442748+01:00 800efcf7-0002-7a00-b63f-84710c7967bb [INF] Received HTTP response headers after 411.2542ms - 500 (f0742c1f)
2025-07-30T12:33:07.5443169+01:00 800efcf7-0002-7a00-b63f-84710c7967bb [INF] End processing HTTP request after 411.4324ms - 500 (7656b38e)
2025-07-30T12:33:07.5488155+01:00 800efcf7-0002-7a00-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-30T12:33:07.5488750+01:00 800efcf7-0002-7a00-b63f-84710c7967bb [INF] (Webjob Application) Posted c9e15c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (66072889)
2025-07-30T12:33:07.7174900+01:00 800efcf7-0002-7a00-b63f-84710c7967bb [INF] (Webjob Application) Posted c9e15c9e-cae7-6322-9971-ff0200979a84  (1dd45396)
2025-07-30T12:33:16.7771917+01:00 80bb3137-0000-ee00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4174\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Marynel\",\"lastName\":\"Cleaver\",\"email\":\"<EMAIL>\",\"phone\":\"07399928524\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"B98 7HS\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "8bbc64ba-6970-4a27-bcec-b3c4a472e2ae",
        "ChildItemId": "39ec1411-42b6-4f40-b4bf-7e938018fd1c",
        "ParentItemId": "c9e15c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4174_Marynel_Cleaver.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-30T11:32:59.867Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "c9e15c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4174_Marynel_Cleaver.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "c9e15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6026",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:32:59.8514659Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4174\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Marynel\",\"lastName\":\"Cleaver\",\"email\":\"<EMAIL>\",\"phone\":\"07399928524\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"B98 7HS\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "8bbc64ba-6970-4a27-bcec-b3c4a472e2ae",
            "ChildItemId": "39ec1411-42b6-4f40-b4bf-7e938018fd1c",
            "ParentItemId": "c9e15c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4174_Marynel_Cleaver.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-30T11:32:59.867Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "c9e15c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4174_Marynel_Cleaver.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (8e4295a7)
2025-07-30T12:33:16.7773576+01:00 80bb3137-0000-ee00-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4174\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Marynel\",\"lastName\":\"Cleaver\",\"email\":\"<EMAIL>\",\"phone\":\"07399928524\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"B98 7HS\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "8bbc64ba-6970-4a27-bcec-b3c4a472e2ae",
        "ChildItemId": "39ec1411-42b6-4f40-b4bf-7e938018fd1c",
        "ParentItemId": "c9e15c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4174_Marynel_Cleaver.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-30T11:32:59.867Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "c9e15c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4174_Marynel_Cleaver.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "c9e15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6026",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:32:59.8514659Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4174\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Marynel\",\"lastName\":\"Cleaver\",\"email\":\"<EMAIL>\",\"phone\":\"07399928524\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"B98 7HS\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "8bbc64ba-6970-4a27-bcec-b3c4a472e2ae",
            "ChildItemId": "39ec1411-42b6-4f40-b4bf-7e938018fd1c",
            "ParentItemId": "c9e15c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4174_Marynel_Cleaver.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-30T11:32:59.867Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "c9e15c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4174_Marynel_Cleaver.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (1da33296)
2025-07-30T12:33:16.7774494+01:00 80bb3137-0000-ee00-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId c9e15c9e-cae7-6322-9971-ff0200979a84 (e5059bcc)
2025-07-30T12:33:16.9453994+01:00 80bb3137-0000-ee00-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-30T12:33:17.0310078+01:00 80bb3137-0000-ee00-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T12:33:17.0310437+01:00 80bb3137-0000-ee00-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T12:33:17.3878718+01:00 80bb3137-0000-ee00-b63f-84710c7967bb [INF] Received HTTP response headers after 356.781ms - 500 (f0742c1f)
2025-07-30T12:33:17.3879146+01:00 80bb3137-0000-ee00-b63f-84710c7967bb [INF] End processing HTTP request after 356.9519ms - 500 (7656b38e)
2025-07-30T12:33:17.3890451+01:00 80bb3137-0000-ee00-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-30T12:33:17.3890788+01:00 80bb3137-0000-ee00-b63f-84710c7967bb [INF] (Webjob Application) Posted c9e15c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (66072889)
2025-07-30T12:33:17.4815263+01:00 80bb3137-0000-ee00-b63f-84710c7967bb [INF] (Webjob Application) Posted c9e15c9e-cae7-6322-9971-ff0200979a84  (1dd45396)
2025-07-30T12:33:33.5404063+01:00 800067f0-0003-8b00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4174\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Marynel\",\"lastName\":\"Cleaver\",\"email\":\"<EMAIL>\",\"phone\":\"07399928524\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"B98 7HS\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "8bbc64ba-6970-4a27-bcec-b3c4a472e2ae",
        "ChildItemId": "39ec1411-42b6-4f40-b4bf-7e938018fd1c",
        "ParentItemId": "c9e15c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4174_Marynel_Cleaver.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-30T11:32:59.867Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "c9e15c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4174_Marynel_Cleaver.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "c9e15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6026",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:32:59.8514659Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4174\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Marynel\",\"lastName\":\"Cleaver\",\"email\":\"<EMAIL>\",\"phone\":\"07399928524\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"B98 7HS\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "8bbc64ba-6970-4a27-bcec-b3c4a472e2ae",
            "ChildItemId": "39ec1411-42b6-4f40-b4bf-7e938018fd1c",
            "ParentItemId": "c9e15c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4174_Marynel_Cleaver.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-30T11:32:59.867Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "c9e15c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4174_Marynel_Cleaver.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (8e4295a7)
2025-07-30T12:33:33.5406626+01:00 800067f0-0003-8b00-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4174\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Marynel\",\"lastName\":\"Cleaver\",\"email\":\"<EMAIL>\",\"phone\":\"07399928524\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"B98 7HS\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "8bbc64ba-6970-4a27-bcec-b3c4a472e2ae",
        "ChildItemId": "39ec1411-42b6-4f40-b4bf-7e938018fd1c",
        "ParentItemId": "c9e15c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4174_Marynel_Cleaver.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-30T11:32:59.867Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "c9e15c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4174_Marynel_Cleaver.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "c9e15c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6026",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-30T11:32:59.8514659Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4174\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Marynel\",\"lastName\":\"Cleaver\",\"email\":\"<EMAIL>\",\"phone\":\"07399928524\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"B98 7HS\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "8bbc64ba-6970-4a27-bcec-b3c4a472e2ae",
            "ChildItemId": "39ec1411-42b6-4f40-b4bf-7e938018fd1c",
            "ParentItemId": "c9e15c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4174_Marynel_Cleaver.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-30T11:32:59.867Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "c9e15c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4174_Marynel_Cleaver.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4174_marynel_cleaver.docx?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (1da33296)
2025-07-30T12:33:33.5407701+01:00 800067f0-0003-8b00-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId c9e15c9e-cae7-6322-9971-ff0200979a84 (e5059bcc)
2025-07-30T12:33:33.7806688+01:00 800067f0-0003-8b00-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-30T12:33:33.8581487+01:00 800067f0-0003-8b00-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T12:33:33.8581869+01:00 800067f0-0003-8b00-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T12:33:34.5247027+01:00 800067f0-0003-8b00-b63f-84710c7967bb [INF] Received HTTP response headers after 666.467ms - 500 (f0742c1f)
2025-07-30T12:33:34.5247445+01:00 800067f0-0003-8b00-b63f-84710c7967bb [INF] End processing HTTP request after 666.645ms - 500 (7656b38e)
2025-07-30T12:33:34.5283526+01:00 800067f0-0003-8b00-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-30T12:33:34.5283827+01:00 800067f0-0003-8b00-b63f-84710c7967bb [INF] (Webjob Application) Posted c9e15c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (66072889)
2025-07-30T12:33:34.6150288+01:00 800067f0-0003-8b00-b63f-84710c7967bb [INF] (Webjob Application) Posted c9e15c9e-cae7-6322-9971-ff0200979a84  (1dd45396)
2025-07-30T21:26:48.7874251+01:00  [WRN] Using an in-memory repository. Keys will not be persisted to storage. (28e83010)
2025-07-30T21:26:48.7905366+01:00  [WRN] Neither user profile nor HKLM registry available. Using an ephemeral key repository. Protected data will be unavailable when application exits. (54f66960)
2025-07-30T21:26:48.8283529+01:00  [WRN] No XML encryptor configured. Key {ca616dcb-902a-4bdf-8eca-a10d0f428714} may be persisted to storage in unencrypted form. (9ca7e61e)
2025-07-30T21:26:48.9055101+01:00 8030b1e1-0002-f200-b63f-84710c7967bb [WRN] Failed to determine the https port for redirect. (ca76cc21)
2025-07-30T21:26:49.2405933+01:00 8030b1e1-0002-f200-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4519\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Stephanie Hor Yan\",\"lastName\":\"Fung\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M3 7BU\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": [
      {
        "Id": "7a8ea07b-47c9-49b2-9eae-0d6643f23258",
        "ChildItemId": "f45c028b-d9ae-453a-bd11-3adcbb054f84",
        "ParentItemId": "01e65c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4519_Stephanie Hor Yan_Fung.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-30T20:26:47.623Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "01e65c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4519_Stephanie Hor Yan_Fung.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "01e65c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6027",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-30T20:26:47.6065619Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4519\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Stephanie Hor Yan\",\"lastName\":\"Fung\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M3 7BU\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "7a8ea07b-47c9-49b2-9eae-0d6643f23258",
            "ChildItemId": "f45c028b-d9ae-453a-bd11-3adcbb054f84",
            "ParentItemId": "01e65c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4519_Stephanie Hor Yan_Fung.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-30T20:26:47.623Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "01e65c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4519_Stephanie Hor Yan_Fung.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (a15a305c)
2025-07-30T21:26:49.3273930+01:00 8030b1e1-0002-f200-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4519\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Stephanie Hor Yan\",\"lastName\":\"Fung\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M3 7BU\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": [
      {
        "Id": "7a8ea07b-47c9-49b2-9eae-0d6643f23258",
        "ChildItemId": "f45c028b-d9ae-453a-bd11-3adcbb054f84",
        "ParentItemId": "01e65c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4519_Stephanie Hor Yan_Fung.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-30T20:26:47.623Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "01e65c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4519_Stephanie Hor Yan_Fung.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "01e65c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6027",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-30T20:26:47.6065619Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4519\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Stephanie Hor Yan\",\"lastName\":\"Fung\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M3 7BU\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "7a8ea07b-47c9-49b2-9eae-0d6643f23258",
            "ChildItemId": "f45c028b-d9ae-453a-bd11-3adcbb054f84",
            "ParentItemId": "01e65c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4519_Stephanie Hor Yan_Fung.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-30T20:26:47.623Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "01e65c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4519_Stephanie Hor Yan_Fung.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (ce2b304a)
2025-07-30T21:26:49.4108475+01:00 8030b1e1-0002-f200-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 01e65c9e-cae7-6322-9971-ff0200979a84 (f223ca46)
2025-07-30T21:26:49.7669406+01:00 8030b1e1-0002-f200-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-30T21:26:50.1664142+01:00 8030b1e1-0002-f200-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T21:26:50.1677071+01:00 8030b1e1-0002-f200-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T21:26:51.2987167+01:00 8030b1e1-0002-f200-b63f-84710c7967bb [INF] Received HTTP response headers after 1125.6659ms - 201 (f0742c1f)
2025-07-30T21:26:51.2994157+01:00 8030b1e1-0002-f200-b63f-84710c7967bb [INF] End processing HTTP request after 1142.9665ms - 201 (7656b38e)
2025-07-30T21:26:51.3007129+01:00 8030b1e1-0002-f200-b63f-84710c7967bb [INF] (Webjob Application) Posted 01e65c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is True (8437eb02)
2025-07-30T21:26:53.1765330+01:00 8030b1e1-0002-f200-b63f-84710c7967bb [INF] (Webjob Application) Posted 01e65c9e-cae7-6322-9971-ff0200979a84  (6917aace)
2025-07-30T21:35:52.9403579+01:00 8006b6c3-0003-db00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4519\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Stephanie Hor Yan\",\"lastName\":\"Fung\",\"email\":\"<EMAIL>\",\"phone\":\"0**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M3 7BU\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": [
      {
        "Id": "29620a77-301a-4297-bae9-ea2cabfecab7",
        "ChildItemId": "f45c028b-d9ae-453a-bd11-3adcbb054f84",
        "ParentItemId": "06e65c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4519_Stephanie Hor Yan_Fung.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-30T20:35:52.78Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "06e65c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4519_Stephanie Hor Yan_Fung.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "06e65c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6028",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "***************",
    "SubmissionTime": "2025-07-30T20:35:52.7489161Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4519\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Stephanie Hor Yan\",\"lastName\":\"Fung\",\"email\":\"<EMAIL>\",\"phone\":\"0**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M3 7BU\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "29620a77-301a-4297-bae9-ea2cabfecab7",
            "ChildItemId": "f45c028b-d9ae-453a-bd11-3adcbb054f84",
            "ParentItemId": "06e65c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4519_Stephanie Hor Yan_Fung.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-30T20:35:52.78Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "06e65c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4519_Stephanie Hor Yan_Fung.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (7f38629d)
2025-07-30T21:35:52.9471223+01:00 8006b6c3-0003-db00-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4519\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Stephanie Hor Yan\",\"lastName\":\"Fung\",\"email\":\"<EMAIL>\",\"phone\":\"0**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M3 7BU\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": [
      {
        "Id": "29620a77-301a-4297-bae9-ea2cabfecab7",
        "ChildItemId": "f45c028b-d9ae-453a-bd11-3adcbb054f84",
        "ParentItemId": "06e65c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4519_Stephanie Hor Yan_Fung.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-30T20:35:52.78Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "06e65c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4519_Stephanie Hor Yan_Fung.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "06e65c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6028",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "***************",
    "SubmissionTime": "2025-07-30T20:35:52.7489161Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4519\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Stephanie Hor Yan\",\"lastName\":\"Fung\",\"email\":\"<EMAIL>\",\"phone\":\"0**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M3 7BU\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "29620a77-301a-4297-bae9-ea2cabfecab7",
            "ChildItemId": "f45c028b-d9ae-453a-bd11-3adcbb054f84",
            "ParentItemId": "06e65c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4519_Stephanie Hor Yan_Fung.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-30T20:35:52.78Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "06e65c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4519_Stephanie Hor Yan_Fung.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (3894cef8)
2025-07-30T21:35:52.9473764+01:00 8006b6c3-0003-db00-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 06e65c9e-cae7-6322-9971-ff0200979a84 (97c06857)
2025-07-30T21:35:53.1469994+01:00 8006b6c3-0003-db00-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-30T21:35:53.2415572+01:00 8006b6c3-0003-db00-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T21:35:53.2416169+01:00 8006b6c3-0003-db00-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T21:35:54.1556417+01:00 8006b6c3-0003-db00-b63f-84710c7967bb [INF] Received HTTP response headers after 913.9303ms - 500 (f0742c1f)
2025-07-30T21:35:54.1557203+01:00 8006b6c3-0003-db00-b63f-84710c7967bb [INF] End processing HTTP request after 914.2376ms - 500 (7656b38e)
2025-07-30T21:35:54.1580784+01:00 8006b6c3-0003-db00-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-30T21:35:54.1581359+01:00 8006b6c3-0003-db00-b63f-84710c7967bb [INF] (Webjob Application) Posted 06e65c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (0407c5a7)
2025-07-30T21:35:54.2706848+01:00 8006b6c3-0003-db00-b63f-84710c7967bb [INF] (Webjob Application) Posted 06e65c9e-cae7-6322-9971-ff0200979a84  (5d4e23fc)
2025-07-30T21:35:55.3253104+01:00 80411088-0000-ed00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4519\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Stephanie Hor Yan\",\"lastName\":\"Fung\",\"email\":\"<EMAIL>\",\"phone\":\"0**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M3 7BU\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": [
      {
        "Id": "29620a77-301a-4297-bae9-ea2cabfecab7",
        "ChildItemId": "f45c028b-d9ae-453a-bd11-3adcbb054f84",
        "ParentItemId": "06e65c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4519_Stephanie Hor Yan_Fung.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-30T20:35:52.78Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "06e65c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4519_Stephanie Hor Yan_Fung.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "06e65c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6028",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "***************",
    "SubmissionTime": "2025-07-30T20:35:52.7489161Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4519\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Stephanie Hor Yan\",\"lastName\":\"Fung\",\"email\":\"<EMAIL>\",\"phone\":\"0**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M3 7BU\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "29620a77-301a-4297-bae9-ea2cabfecab7",
            "ChildItemId": "f45c028b-d9ae-453a-bd11-3adcbb054f84",
            "ParentItemId": "06e65c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4519_Stephanie Hor Yan_Fung.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-30T20:35:52.78Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "06e65c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4519_Stephanie Hor Yan_Fung.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (7f38629d)
2025-07-30T21:35:55.3255921+01:00 80411088-0000-ed00-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4519\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Stephanie Hor Yan\",\"lastName\":\"Fung\",\"email\":\"<EMAIL>\",\"phone\":\"0**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M3 7BU\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": [
      {
        "Id": "29620a77-301a-4297-bae9-ea2cabfecab7",
        "ChildItemId": "f45c028b-d9ae-453a-bd11-3adcbb054f84",
        "ParentItemId": "06e65c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4519_Stephanie Hor Yan_Fung.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-30T20:35:52.78Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "06e65c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4519_Stephanie Hor Yan_Fung.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "06e65c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6028",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "***************",
    "SubmissionTime": "2025-07-30T20:35:52.7489161Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4519\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Stephanie Hor Yan\",\"lastName\":\"Fung\",\"email\":\"<EMAIL>\",\"phone\":\"0**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M3 7BU\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "29620a77-301a-4297-bae9-ea2cabfecab7",
            "ChildItemId": "f45c028b-d9ae-453a-bd11-3adcbb054f84",
            "ParentItemId": "06e65c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4519_Stephanie Hor Yan_Fung.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-30T20:35:52.78Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "06e65c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4519_Stephanie Hor Yan_Fung.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (3894cef8)
2025-07-30T21:35:55.3257859+01:00 80411088-0000-ed00-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 06e65c9e-cae7-6322-9971-ff0200979a84 (97c06857)
2025-07-30T21:35:55.5180900+01:00 80411088-0000-ed00-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-30T21:35:55.6755952+01:00 80411088-0000-ed00-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T21:35:55.6756380+01:00 80411088-0000-ed00-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T21:35:56.5213662+01:00 80411088-0000-ed00-b63f-84710c7967bb [INF] Received HTTP response headers after 845.6561ms - 500 (f0742c1f)
2025-07-30T21:35:56.5214374+01:00 80411088-0000-ed00-b63f-84710c7967bb [INF] End processing HTTP request after 845.899ms - 500 (7656b38e)
2025-07-30T21:35:56.5229476+01:00 80411088-0000-ed00-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-30T21:35:56.5230112+01:00 80411088-0000-ed00-b63f-84710c7967bb [INF] (Webjob Application) Posted 06e65c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (0407c5a7)
2025-07-30T21:35:56.6010672+01:00 80411088-0000-ed00-b63f-84710c7967bb [INF] (Webjob Application) Posted 06e65c9e-cae7-6322-9971-ff0200979a84  (5d4e23fc)
2025-07-30T21:36:00.6442333+01:00 8004786f-0002-9000-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4519\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Stephanie Hor Yan\",\"lastName\":\"Fung\",\"email\":\"<EMAIL>\",\"phone\":\"0**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M3 7BU\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": [
      {
        "Id": "29620a77-301a-4297-bae9-ea2cabfecab7",
        "ChildItemId": "f45c028b-d9ae-453a-bd11-3adcbb054f84",
        "ParentItemId": "06e65c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4519_Stephanie Hor Yan_Fung.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-30T20:35:52.78Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "06e65c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4519_Stephanie Hor Yan_Fung.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "06e65c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6028",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "***************",
    "SubmissionTime": "2025-07-30T20:35:52.7489161Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4519\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Stephanie Hor Yan\",\"lastName\":\"Fung\",\"email\":\"<EMAIL>\",\"phone\":\"0**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M3 7BU\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "29620a77-301a-4297-bae9-ea2cabfecab7",
            "ChildItemId": "f45c028b-d9ae-453a-bd11-3adcbb054f84",
            "ParentItemId": "06e65c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4519_Stephanie Hor Yan_Fung.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-30T20:35:52.78Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "06e65c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4519_Stephanie Hor Yan_Fung.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (7f38629d)
2025-07-30T21:36:00.6475827+01:00 8004786f-0002-9000-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4519\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Stephanie Hor Yan\",\"lastName\":\"Fung\",\"email\":\"<EMAIL>\",\"phone\":\"0**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M3 7BU\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": [
      {
        "Id": "29620a77-301a-4297-bae9-ea2cabfecab7",
        "ChildItemId": "f45c028b-d9ae-453a-bd11-3adcbb054f84",
        "ParentItemId": "06e65c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4519_Stephanie Hor Yan_Fung.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-30T20:35:52.78Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "06e65c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4519_Stephanie Hor Yan_Fung.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "06e65c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6028",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "***************",
    "SubmissionTime": "2025-07-30T20:35:52.7489161Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4519\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Stephanie Hor Yan\",\"lastName\":\"Fung\",\"email\":\"<EMAIL>\",\"phone\":\"0**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M3 7BU\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "29620a77-301a-4297-bae9-ea2cabfecab7",
            "ChildItemId": "f45c028b-d9ae-453a-bd11-3adcbb054f84",
            "ParentItemId": "06e65c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4519_Stephanie Hor Yan_Fung.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-30T20:35:52.78Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "06e65c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4519_Stephanie Hor Yan_Fung.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (3894cef8)
2025-07-30T21:36:00.6477506+01:00 8004786f-0002-9000-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 06e65c9e-cae7-6322-9971-ff0200979a84 (97c06857)
2025-07-30T21:36:00.8610893+01:00 8004786f-0002-9000-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-30T21:36:00.9442830+01:00 8004786f-0002-9000-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T21:36:00.9443176+01:00 8004786f-0002-9000-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T21:36:01.7740813+01:00 8004786f-0002-9000-b63f-84710c7967bb [INF] Received HTTP response headers after 829.703ms - 500 (f0742c1f)
2025-07-30T21:36:01.7741239+01:00 8004786f-0002-9000-b63f-84710c7967bb [INF] End processing HTTP request after 829.9023ms - 500 (7656b38e)
2025-07-30T21:36:01.7753096+01:00 8004786f-0002-9000-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-30T21:36:01.7753549+01:00 8004786f-0002-9000-b63f-84710c7967bb [INF] (Webjob Application) Posted 06e65c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (0407c5a7)
2025-07-30T21:36:01.8628776+01:00 8004786f-0002-9000-b63f-84710c7967bb [INF] (Webjob Application) Posted 06e65c9e-cae7-6322-9971-ff0200979a84  (5d4e23fc)
2025-07-30T21:36:10.9130922+01:00 8006b733-0003-db00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4519\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Stephanie Hor Yan\",\"lastName\":\"Fung\",\"email\":\"<EMAIL>\",\"phone\":\"0**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M3 7BU\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": [
      {
        "Id": "29620a77-301a-4297-bae9-ea2cabfecab7",
        "ChildItemId": "f45c028b-d9ae-453a-bd11-3adcbb054f84",
        "ParentItemId": "06e65c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4519_Stephanie Hor Yan_Fung.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-30T20:35:52.78Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "06e65c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4519_Stephanie Hor Yan_Fung.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "06e65c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6028",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "***************",
    "SubmissionTime": "2025-07-30T20:35:52.7489161Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4519\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Stephanie Hor Yan\",\"lastName\":\"Fung\",\"email\":\"<EMAIL>\",\"phone\":\"0**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M3 7BU\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "29620a77-301a-4297-bae9-ea2cabfecab7",
            "ChildItemId": "f45c028b-d9ae-453a-bd11-3adcbb054f84",
            "ParentItemId": "06e65c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4519_Stephanie Hor Yan_Fung.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-30T20:35:52.78Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "06e65c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4519_Stephanie Hor Yan_Fung.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (7f38629d)
2025-07-30T21:36:10.9133233+01:00 8006b733-0003-db00-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4519\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Stephanie Hor Yan\",\"lastName\":\"Fung\",\"email\":\"<EMAIL>\",\"phone\":\"0**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M3 7BU\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": [
      {
        "Id": "29620a77-301a-4297-bae9-ea2cabfecab7",
        "ChildItemId": "f45c028b-d9ae-453a-bd11-3adcbb054f84",
        "ParentItemId": "06e65c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4519_Stephanie Hor Yan_Fung.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-30T20:35:52.78Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "06e65c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4519_Stephanie Hor Yan_Fung.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "06e65c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6028",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "***************",
    "SubmissionTime": "2025-07-30T20:35:52.7489161Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4519\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Stephanie Hor Yan\",\"lastName\":\"Fung\",\"email\":\"<EMAIL>\",\"phone\":\"0**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M3 7BU\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "29620a77-301a-4297-bae9-ea2cabfecab7",
            "ChildItemId": "f45c028b-d9ae-453a-bd11-3adcbb054f84",
            "ParentItemId": "06e65c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4519_Stephanie Hor Yan_Fung.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-30T20:35:52.78Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "06e65c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4519_Stephanie Hor Yan_Fung.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (3894cef8)
2025-07-30T21:36:10.9134650+01:00 8006b733-0003-db00-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 06e65c9e-cae7-6322-9971-ff0200979a84 (97c06857)
2025-07-30T21:36:11.1319338+01:00 8006b733-0003-db00-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-30T21:36:11.1855143+01:00 8006b733-0003-db00-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T21:36:11.1855460+01:00 8006b733-0003-db00-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T21:36:12.0340748+01:00 8006b733-0003-db00-b63f-84710c7967bb [INF] Received HTTP response headers after 848.4796ms - 500 (f0742c1f)
2025-07-30T21:36:12.0341151+01:00 8006b733-0003-db00-b63f-84710c7967bb [INF] End processing HTTP request after 848.6697ms - 500 (7656b38e)
2025-07-30T21:36:12.0344062+01:00 8006b733-0003-db00-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-30T21:36:12.0344523+01:00 8006b733-0003-db00-b63f-84710c7967bb [INF] (Webjob Application) Posted 06e65c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (0407c5a7)
2025-07-30T21:36:12.1063764+01:00 8006b733-0003-db00-b63f-84710c7967bb [INF] (Webjob Application) Posted 06e65c9e-cae7-6322-9971-ff0200979a84  (5d4e23fc)
2025-07-30T21:36:28.1642667+01:00 8009e699-0003-7800-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4519\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Stephanie Hor Yan\",\"lastName\":\"Fung\",\"email\":\"<EMAIL>\",\"phone\":\"0**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M3 7BU\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": [
      {
        "Id": "29620a77-301a-4297-bae9-ea2cabfecab7",
        "ChildItemId": "f45c028b-d9ae-453a-bd11-3adcbb054f84",
        "ParentItemId": "06e65c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4519_Stephanie Hor Yan_Fung.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-30T20:35:52.78Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "06e65c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4519_Stephanie Hor Yan_Fung.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "06e65c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6028",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "***************",
    "SubmissionTime": "2025-07-30T20:35:52.7489161Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4519\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Stephanie Hor Yan\",\"lastName\":\"Fung\",\"email\":\"<EMAIL>\",\"phone\":\"0**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M3 7BU\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "29620a77-301a-4297-bae9-ea2cabfecab7",
            "ChildItemId": "f45c028b-d9ae-453a-bd11-3adcbb054f84",
            "ParentItemId": "06e65c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4519_Stephanie Hor Yan_Fung.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-30T20:35:52.78Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "06e65c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4519_Stephanie Hor Yan_Fung.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (7f38629d)
2025-07-30T21:36:28.1675496+01:00 8009e699-0003-7800-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4519\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Stephanie Hor Yan\",\"lastName\":\"Fung\",\"email\":\"<EMAIL>\",\"phone\":\"0**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M3 7BU\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
    "FileFieldController": [
      {
        "Id": "29620a77-301a-4297-bae9-ea2cabfecab7",
        "ChildItemId": "f45c028b-d9ae-453a-bd11-3adcbb054f84",
        "ParentItemId": "06e65c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4519_Stephanie Hor Yan_Fung.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-30T20:35:52.78Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "06e65c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4519_Stephanie Hor Yan_Fung.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "06e65c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6028",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "***************",
    "SubmissionTime": "2025-07-30T20:35:52.7489161Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4519\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Stephanie Hor Yan\",\"lastName\":\"Fung\",\"email\":\"<EMAIL>\",\"phone\":\"0**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M3 7BU\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "29620a77-301a-4297-bae9-ea2cabfecab7",
            "ChildItemId": "f45c028b-d9ae-453a-bd11-3adcbb054f84",
            "ParentItemId": "06e65c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4519_Stephanie Hor Yan_Fung.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-30T20:35:52.78Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "06e65c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4519_Stephanie Hor Yan_Fung.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4519_stephanie-hor-yan_fung.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (3894cef8)
2025-07-30T21:36:28.1677010+01:00 8009e699-0003-7800-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 06e65c9e-cae7-6322-9971-ff0200979a84 (97c06857)
2025-07-30T21:36:28.3653288+01:00 8009e699-0003-7800-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-30T21:36:28.4380674+01:00 8009e699-0003-7800-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-30T21:36:28.4381141+01:00 8009e699-0003-7800-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-30T21:36:29.5691107+01:00 8009e699-0003-7800-b63f-84710c7967bb [INF] Received HTTP response headers after 1130.9414ms - 500 (f0742c1f)
2025-07-30T21:36:29.5691603+01:00 8009e699-0003-7800-b63f-84710c7967bb [INF] End processing HTTP request after 1131.1591ms - 500 (7656b38e)
2025-07-30T21:36:29.5701760+01:00 8009e699-0003-7800-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-30T21:36:29.5702133+01:00 8009e699-0003-7800-b63f-84710c7967bb [INF] (Webjob Application) Posted 06e65c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (0407c5a7)
2025-07-30T21:36:29.6992641+01:00 8009e699-0003-7800-b63f-84710c7967bb [INF] (Webjob Application) Posted 06e65c9e-cae7-6322-9971-ff0200979a84  (5d4e23fc)
