﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
  </configSections>
  <connectionStrings>
	  
	  <add name ="Warehouse" connectionString="Data Source=myd-dev-clarity;Initial Catalog=Warehouse; Trusted_Connection=True; Encrypt=False" providerName="System.Data.SqlClient"/>
	  <add name="GroupWebsitePracticeData" connectionString="Data Source=10.1.2.115; User ID=formSubmitUser; Password= Password3456!; Initial Catalog=GroupWebsitePracticeData;" providerName="System.Data.SqlClient"/>
	  <add name="Staging" connectionString="Data Source=172.30.1.119; User ID=formSubmitUser; Password= Password3456!;Initial Catalog=Staging;" providerName="System.Data.SqlClient"/>
	  <add name="ClinicianIntegrationMultiRole" connectionString="Data Source=10.1.2.115; User ID=formSubmitUser; Password= Password3456!; Initial Catalog=ClinicianIntegration_MultiRole_Dev;" providerName="System.Data.SqlClient"/>
  </connectionStrings>
  <appSettings>
    <add key="sitefinityUsername" value="DataUpdate"/>
    <add key="sitefinityPassword" value="wZpFf847FaODf5hpXul2"/>
    <add key="EmailToList" value="<EMAIL>"/>
    <add key="VacancyApplyApi" value="https://api.mydentist.co.uk/Vacancy.Api.V4.Dev/api/Applications/Apply"/>
    <add key="LogLocation" value="C:\\Logs\\FormSubmission.API\\1.0.0 Test\\log.txt"/>
    <add key="VacancyApiKey" value="df5da1d8-620c-4636-8c52-3c327ac43a11"/>
    <add key="EmailTemplate" value="CustomerFeedbackSubmitted.cshtml"/>
    <add key="ReferralEmailTemplate" value="PatientReferralSubmitted.cshtml"/>
    <add key="PatientSupportFeedBackUser" value="WXHO\\mmckessy"/>
    <add key="APIKey" value="ec32870c8968aebacab659d03d0098317857142729baeaa3c3da3e116dba685a"/>
    <add key="RawJsonLogging" value="yes"/>
    <add key="MapLogging" value="yes"/>
    <add key="applicationEmail" value="<EMAIL>"/>
    <add key="EmailToOverride" value=""/>
    <add key="MarketingTeamEmail" value="<EMAIL>"/>
    <add key="PracticeViewImageApprovalUrl" value="http://dev-myd-web/PracticeView/Images/Status"/>
	<add key="websiteConfigBaseUrl" value="https://api.mydentist.co.uk/webconfig.API.V6.Dev/api/"/>
	<add key="websiteConfigApiKey" value="CT-Hvk6OuOtBGlq75VhLEch5zfA5dV1IJmrA"/>
	<add key="EmailEndpoint" value="http://172.30.1.192/EmailInterfaceServiceV2/Service1.svc"/>
  </appSettings>
  <entityFramework>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer"/>
    </providers>
  </entityFramework>
</configuration>