﻿using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Mydentist.MyDSitefinityApi.ClinicianPortalApi
{
    public class ClinicianPortalApiWrapper: IClinicianPortalApiWrapper
    {
        private readonly ClinicianPortalSettings _clinicianPortalSettings;

        public ClinicianPortalApiWrapper(IOptions<ClinicianPortalSettings> clinicianPortalSettings)
        {
            _clinicianPortalSettings = clinicianPortalSettings?.Value;
        }

        public async Task<T> GetApiResponse<T>(string url)
        {
            HttpClientHandler handler = new HttpClientHandler();

            using (var httpClient = new HttpClient(handler))
            {
                try
                {
                    httpClient.DefaultRequestHeaders.Add("Cache-Control", "no-cache");
                    httpClient.DefaultRequestHeaders.Add("X-ApiKey", _clinicianPortalSettings.ApiKey);

                    Uri uri = new Uri(_clinicianPortalSettings.Url + url);

                    var response = await httpClient.GetAsync(uri);

                    var content = await response.Content.ReadAsStringAsync();

                    if (response.StatusCode == HttpStatusCode.BadRequest)
                        throw new WebException(content);

                    if (!response.IsSuccessStatusCode)
                        throw new WebException(
                            $"Unsuccessful result ({response.StatusCode}) when calling {uri} from Api {response.ReasonPhrase} when communicating with CRM API as user {Environment.UserName}. {content}");

                    if (content == null || string.IsNullOrEmpty(content))
                        throw new InvalidDataException(
                            $"Expected a result from GetApiResponse but {content} value is determined");

                    return JsonConvert.DeserializeObject<T>(content);
                }
                catch (HttpRequestException ex)
                {
                    //Log.Error(ex, $"Exception from Dynamics Api. {ex.Message}");

                    throw;
                }
                catch (WebException ex)
                {
                    //Log.Error(ex, $"Exception from Dynamics Api. {ex.Message}");

                    throw;
                }
            }
        }
    }

    public interface IClinicianPortalApiWrapper
    {
        Task<T> GetApiResponse<T>(string url);
    }

}
