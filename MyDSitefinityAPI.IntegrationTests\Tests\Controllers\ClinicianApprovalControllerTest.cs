﻿using System.Web;
using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using MyDSitefinity;
using MyDSitefinityAPI.Controllers;
using MyDSitefinityAPI.IntegrationTests.Mock;
using MyDSitefinityAPI.Models;

namespace MyDSitefinityAPI.IntegrationTests.Tests.Controllers
{
    [TestClass]
    public class ClinicianApprovalControllerTest : Test
    {
        private ClinicianApprovalController Controller { get; }

        public ClinicianApprovalControllerTest()
        {
            ILogger<ClinicianApprovalController> logger = new MockLogger<ClinicianApprovalController>();

            Controller = new ClinicianApprovalController(logger, PublicUserAuthenticationService, PerformerProfileService)
            {
                ControllerContext = new ControllerContext()
                {
                    HttpContext = new DefaultHttpContext()
                }
            };
        }

        [TestMethod]
        public void GetLoginForm()
        {
            Guid imageId = Guid.NewGuid();

            // Login without a token
            ActionResult actionResult = Controller.Login(imageId, null);

            Assert.IsNotNull(actionResult, "Controller returned null");
            Assert.IsInstanceOfType(actionResult, typeof(ViewResult), "Controller did not return a view");

            ViewResult viewResult = (ViewResult)actionResult;
            Assert.IsInstanceOfType(viewResult.Model, typeof(PublicUser));

            PublicUser model = (PublicUser)(viewResult.Model);
            Assert.AreEqual(imageId, model.KeyParameter, "ImageId in view does not match");

            // Login with a token
            string testUserHash = PublicUserAuthenticationService.ComputeHash(TestPublicUser.UserId, TestPublicUser.Salt);
            actionResult = Controller.Login(imageId, testUserHash);

            Assert.IsNotNull(actionResult, "Controller returned null");
            Assert.IsInstanceOfType(actionResult, typeof(RedirectToActionResult), "Controller did not return a redirect");

            RedirectToActionResult redirectResult = (RedirectToActionResult)actionResult;
            Assert.AreEqual("Approve", redirectResult.ActionName, "Action name was not equal to 'Approve'");
            Assert.AreEqual("ClinicianApproval", redirectResult.ControllerName, "Controller name was not equal to 'ClinicianApproval'");
        }

        [TestMethod]
        public void PostLoginForm()
        {
            // Login without a username or password
            JsonResult jsonResult = Controller.Login(new PublicUser());
            Assert.IsNotNull(jsonResult, "Controller returned null");
            Assert.IsInstanceOfType(jsonResult.Value, typeof(ResultViewModel));

            ResultViewModel result = (ResultViewModel)(jsonResult.Value);
            Assert.IsNull(result.Value, "Result was not null");
            Assert.AreEqual("Must provide both a username and a password.", result.Error);
            Assert.AreEqual(401, Controller.HttpContext.Response.StatusCode);

            // Login without a username
            jsonResult = Controller.Login(new PublicUser { Password = TestPassword });
            Assert.IsNotNull(jsonResult, "Controller returned null");
            Assert.IsInstanceOfType(jsonResult.Value, typeof(ResultViewModel));

            result = (ResultViewModel)(jsonResult.Value);
            Assert.IsNull(result.Value, "Result was not null");
            Assert.AreEqual("Must provide both a username and a password.", result.Error);
            Assert.AreEqual(401, Controller.HttpContext.Response.StatusCode);

            // Login without a password
            jsonResult = Controller.Login(new PublicUser { UserId = TestUsername });
            Assert.IsNotNull(jsonResult, "Controller returned null");
            Assert.IsInstanceOfType(jsonResult.Value, typeof(ResultViewModel));

            result = (ResultViewModel)(jsonResult.Value);
            Assert.IsNull(result.Value, "Result was not null");
            Assert.AreEqual("Must provide both a username and a password.", result.Error);
            Assert.AreEqual(401, Controller.HttpContext.Response.StatusCode);

            // Login with invalid credentials
            jsonResult = Controller.Login(new PublicUser { UserId = "3746354234rt7t2feed23", Password = "3w7t263rt2rt762" });
            Assert.IsNotNull(jsonResult, "Controller returned null");
            Assert.IsInstanceOfType(jsonResult.Value, typeof(ResultViewModel));

            result = (ResultViewModel)(jsonResult.Value);
            Assert.IsNull(result.Value, "Result was not null");
            Assert.AreEqual("Invalid username or password.", result.Error);
            Assert.AreEqual(401, Controller.HttpContext.Response.StatusCode);

            // Login with valid credentials
            jsonResult = Controller.Login(new PublicUser { UserId = TestUsername, Password = TestPassword });
            Assert.IsNotNull(jsonResult, "Controller returned null");
            Assert.IsInstanceOfType(jsonResult.Value, typeof(ResultViewModel));

            string expectedHash = PublicUserAuthenticationService.ComputeHash(TestUsername, TestPublicUser.Salt);

            result = (ResultViewModel)(jsonResult.Value);
            Assert.IsNull(result.Error, "Error was not null");
            Assert.AreEqual(HttpUtility.UrlEncode(expectedHash), result.Value);
        }

        [TestMethod]
        public void GetApproveClinicianForm()
        {
            // Approve without a token
            ActionResult actionResult = Controller.ApproveClinician(TestPerformerImage.ImageId, null);

            Assert.IsNotNull(actionResult, "Controller returned null");
            Assert.IsInstanceOfType(actionResult, typeof(ViewResult));

            ViewResult viewResult = (ViewResult)actionResult;
            Assert.IsNotNull(viewResult.ViewData["TokenIsEmpty"]);
            Assert.IsTrue((bool)viewResult.ViewData["TokenIsEmpty"], "View should be aware that the token is empty");
            Assert.IsNull(viewResult.Model, "Model should be null as the request did not pass a valid token");
            
            // Approve with an invalid token
            actionResult = Controller.ApproveClinician(TestPerformerImage.ImageId, "wbgyuwgefyutgweyufgwyuegfyuwegfyuwgeyu");

            Assert.IsNotNull(actionResult, "Controller returned null");
            Assert.IsInstanceOfType(actionResult, typeof(RedirectToActionResult), "User should be redirected to login page as they have a token. It's just invalid");

            RedirectToActionResult redirectToActionResult = (RedirectToActionResult)actionResult;
            Assert.AreEqual("Login", redirectToActionResult.ActionName);
            Assert.AreEqual("ClinicianApproval",redirectToActionResult.ControllerName);

            // Approve with an invalid image id
            string testUserHash = PublicUserAuthenticationService.ComputeHash(TestPublicUser.UserId, TestPublicUser.Salt);
            actionResult = Controller.ApproveClinician(Guid.NewGuid(), testUserHash);
            
            Assert.IsNotNull(actionResult, "Controller returned null");
            Assert.IsInstanceOfType(actionResult, typeof(ViewResult));

            viewResult = (ViewResult)actionResult;
            Assert.IsNotNull(viewResult.ViewData["TokenIsEmpty"]);
            Assert.IsFalse((bool)viewResult.ViewData["TokenIsEmpty"], "View should be aware that the token is not empty");
            Assert.IsNull(viewResult.Model, "Model should be null as the request did not pass a valid image id");

            // Approve with a valid image id and valid token
            actionResult = Controller.ApproveClinician(TestPerformerImage.ImageId, testUserHash);
            
            Assert.IsNotNull(actionResult, "Controller returned null");
            Assert.IsInstanceOfType(actionResult, typeof(ViewResult));

            viewResult = (ViewResult)actionResult;
            Assert.IsNotNull(viewResult.ViewData["TokenIsEmpty"]);
            Assert.IsFalse((bool)viewResult.ViewData["TokenIsEmpty"], "View should be aware that the token is not empty");
            Assert.IsNotNull(viewResult.Model, "Model should not be null as the request passed an existing image's id");
        }
    }
}
