﻿using System;

namespace IDHGroup.SharedLibraries.SitefinityServiceCaller.Exceptions
{
    public class SitefinityWebserviceException : Exception
    {
        public SitefinityWebserviceException(string error)
        {
            ReturnedError = error;
        }

        /// <summary>Creates and returns a string representation of the current exception.</summary>
        /// <returns>A string representation of the current exception.</returns>
        /// <PermissionSet>
        ///     <IPermission
        ///         class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089"
        ///         version="1" PathDiscovery="*AllFiles*" />
        /// </PermissionSet>
        public override string ToString()
        {
            return $"The Sitefinity webservice returned the following error: {ReturnedError}";
        }

        private string ReturnedError { get; }
    }
}