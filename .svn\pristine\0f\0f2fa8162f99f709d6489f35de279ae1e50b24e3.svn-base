﻿using System.ComponentModel.DataAnnotations.Schema;

namespace MyDSitefinityAPI.Models
{
    [Table("sf_emergency_dental_appointment", Schema = "dbo")]
    public class sf_Emergencydentalappointment
    {
        public int ID { get; set; }
        public DateTime DateOfBirth { get; set; }
        public string PracticeId { get; set; }
        public string Firstname { get; set; }
        public string Lastname { get; set; }
        public string ContactNumber { get; set; }
        public bool AreYouExperiencingDentalPain { get; set; }
        public bool DoYouHaveABrokenTooth { get; set; }
        public bool DoYouRequireInterpreter { get; set; }
        public string InterpreterLanguage { get; set; }
        public bool RequiresGroundFloorAccess { get; set; }
        public bool PersonReceivingBenefit { get; set; }
        public string NHS_MaternityExemptionCertificate { get; set; }
        public bool EntitledToFreeNHSDentalServicesOnFirstDay { get; set; }
        public bool EntitledToFreeNHSDentalServicesDuringTreatment { get; set; }
        public string NameOfCollegeOrUniversity { get; set; }
        public string NationalInsuranceNumber { get; set; }
        public DateTime DateOfBirthForNHSService { get; set; }
        public bool AreYouEntitledToFreeNHSDentalServices { get; set; }
        public bool DoYouHaveALostFilling { get; set; }
        public DateTime? DateBabyDueOrBorn { get; set; }
        public DateTime? CreatedDate { get; set; }
    }
}
