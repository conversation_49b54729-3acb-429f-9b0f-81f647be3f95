﻿using Microsoft.EntityFrameworkCore;
using Mydentist.MyDSitefinityAPI.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mydentist.MyDSitefinityAPI.WebConfigApi
{

    public class WebsiteConfigDbContext : DbContext
    {
        public WebsiteConfigDbContext(DbContextOptions<WebsiteConfigDbContext> options) : base(options)
        {
        }

        public DbSet<WebsiteConfigModel> WebsiteConfigModel { get; set; }
        public DbSet<RolePostgraduateLookup> RolePostgraduateLookups { get; set; }

    }
}
