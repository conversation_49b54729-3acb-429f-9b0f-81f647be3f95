﻿@model string

<div class="loader">
    <div class="loader_box">
        <h1 class="loader_title">Checking if we're already logged in...</h1>
        <div class="loader_subtitle">Thanks for your patience</div>
    </div>
    <div class="loader_image"></div>
</div>

<script>

    /**
     * Searches the query string for a given parameter name and 
     */
    function getQueryParameter(parameterName) {

        var queryString = window.location.search.substring(1);
        var queryParts = queryString.split("&");
        var parameterValue = null;

        // Look through each of the query string parameters and check whether the name matches our argument. If it does, return the value of the parameter
        for (var index = 0; index < queryParts.length && parameterValue === null; index++) {

            var parameterParts = queryParts[index].split("=");
            if (parameterParts[0] === parameterName) {

                parameterValue = parameterParts[1];
            }
        }
        
        return parameterValue;
    }

    /**
     * Checks whether a token has been set in the local storage. If it has then refresh the page with the token added. Otherwise, redirect to the login page
     */
    function redirectToPageWithToken() {
        
        var localStorage = window.localStorage;
        var token = localStorage.getItem("PublicUserToken");
        var imageId = getQueryParameter("imageid");

        if (token != null && token !== "") {
            
            window.location.href = "./ApproveClinician?imageId=" + imageId + "&token=" + token;
        }
        else {

            window.location.href = "./Login?imageId=" + imageId;
        }
    }

    redirectToPageWithToken();

</script>