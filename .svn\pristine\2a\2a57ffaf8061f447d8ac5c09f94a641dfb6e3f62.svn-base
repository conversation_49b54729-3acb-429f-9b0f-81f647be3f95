﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MyDSitefinityAPI.Models.PracticeDirectory
{
    [Table("Role", Schema = "Directory")]
    public class Role
    {
        [Key]
        [Column("RoleId")]
        public int RoleId { get; set; }

        [Column("RoleTypeId")]
        public int RoleTypeId { get; set; }

        [ForeignKey("RoleId")]
        public virtual List<PracticePerson> PracticePeople { get; set; }

        [ForeignKey("RoleTypeId")]
        public virtual RoleType RoleType { get; set; }
    }
}
