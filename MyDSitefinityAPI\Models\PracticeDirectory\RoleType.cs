﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MyDSitefinityAPI.Models.PracticeDirectory
{
    [Table("RoleType", Schema = "Directory")]
    public class RoleType
    {
        [Key]
        [Column("RoleTypeId")]
        public int RoleTypeId { get; set; }

        [Column("Description")]
        public string Description { get; set; }

        [ForeignKey("RoleTypeId")]
        public virtual List<Role> Roles { get; set; }
    }
}
