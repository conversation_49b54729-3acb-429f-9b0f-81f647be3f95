﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace EmailService
{
    using System.Runtime.Serialization;
    
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="base_class", Namespace="http://schemas.datacontract.org/2004/07/EmailInterfaceServiceV2.shared_Objects")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(EmailService.base_return))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(EmailService.SendEmailResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(EmailService.GetemailStatusResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(EmailService.Email))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(EmailService.GetEmailStatusRequest))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(EmailService.SendEmailRequest))]
    public partial class base_class : object
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="base_return", Namespace="http://schemas.datacontract.org/2004/07/EmailInterfaceServiceV2.shared_Objects")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(EmailService.SendEmailResponse))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(EmailService.GetemailStatusResponse))]
    public partial class base_return : EmailService.base_class
    {
        
        private bool ErrorOccuredField;
        
        private System.Collections.Generic.List<EmailService.error_details> Error_DetailsField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool ErrorOccured
        {
            get
            {
                return this.ErrorOccuredField;
            }
            set
            {
                this.ErrorOccuredField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<EmailService.error_details> Error_Details
        {
            get
            {
                return this.Error_DetailsField;
            }
            set
            {
                this.Error_DetailsField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SendEmailResponse", Namespace="http://schemas.datacontract.org/2004/07/EmailInterfaceServiceV2.Return_Object")]
    public partial class SendEmailResponse : EmailService.base_return
    {
        
        private int Mail_IDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Mail_ID
        {
            get
            {
                return this.Mail_IDField;
            }
            set
            {
                this.Mail_IDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetemailStatusResponse", Namespace="http://schemas.datacontract.org/2004/07/EmailInterfaceServiceV2.Return_Object")]
    public partial class GetemailStatusResponse : EmailService.base_return
    {
        
        private EmailService.Email_Status emailstatusField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public EmailService.Email_Status emailstatus
        {
            get
            {
                return this.emailstatusField;
            }
            set
            {
                this.emailstatusField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Email", Namespace="http://schemas.datacontract.org/2004/07/EmailInterfaceServiceV2.Input_Objects")]
    public partial class Email : EmailService.base_class
    {
        
        private System.Collections.Generic.List<string> AttachmentsField;
        
        private System.Collections.Generic.List<string> BCCField;
        
        private System.Collections.Generic.List<string> CCField;
        
        private System.DateTime DelayDateTimeField;
        
        private bool DelayEmailField;
        
        private System.Collections.Generic.List<string> FromField;
        
        private bool IsHTMLField;
        
        private string SubjectField;
        
        private System.Collections.Generic.List<string> ToField;
        
        private string bodyField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<string> Attachments
        {
            get
            {
                return this.AttachmentsField;
            }
            set
            {
                this.AttachmentsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<string> BCC
        {
            get
            {
                return this.BCCField;
            }
            set
            {
                this.BCCField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<string> CC
        {
            get
            {
                return this.CCField;
            }
            set
            {
                this.CCField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime DelayDateTime
        {
            get
            {
                return this.DelayDateTimeField;
            }
            set
            {
                this.DelayDateTimeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool DelayEmail
        {
            get
            {
                return this.DelayEmailField;
            }
            set
            {
                this.DelayEmailField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<string> From
        {
            get
            {
                return this.FromField;
            }
            set
            {
                this.FromField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsHTML
        {
            get
            {
                return this.IsHTMLField;
            }
            set
            {
                this.IsHTMLField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Subject
        {
            get
            {
                return this.SubjectField;
            }
            set
            {
                this.SubjectField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Collections.Generic.List<string> To
        {
            get
            {
                return this.ToField;
            }
            set
            {
                this.ToField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string body
        {
            get
            {
                return this.bodyField;
            }
            set
            {
                this.bodyField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GetEmailStatusRequest", Namespace="http://schemas.datacontract.org/2004/07/EmailInterfaceServiceV2.Input_Objects")]
    public partial class GetEmailStatusRequest : EmailService.base_class
    {
        
        private EmailService.Application_Details Application_DetialsField;
        
        private int QueueEmailIDField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public EmailService.Application_Details Application_Detials
        {
            get
            {
                return this.Application_DetialsField;
            }
            set
            {
                this.Application_DetialsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int QueueEmailID
        {
            get
            {
                return this.QueueEmailIDField;
            }
            set
            {
                this.QueueEmailIDField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SendEmailRequest", Namespace="http://schemas.datacontract.org/2004/07/EmailInterfaceServiceV2.Input_Objects")]
    public partial class SendEmailRequest : EmailService.base_class
    {
        
        private EmailService.Application_Details App_DetailsField;
        
        private EmailService.Email emailField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public EmailService.Application_Details App_Details
        {
            get
            {
                return this.App_DetailsField;
            }
            set
            {
                this.App_DetailsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public EmailService.Email email
        {
            get
            {
                return this.emailField;
            }
            set
            {
                this.emailField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Application_Details", Namespace="http://schemas.datacontract.org/2004/07/EmailInterfaceServiceV2.Input_Objects")]
    public partial class Application_Details : object
    {
        
        private string Requesting_SystemField;
        
        private string Requesting_VersionField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Requesting_System
        {
            get
            {
                return this.Requesting_SystemField;
            }
            set
            {
                this.Requesting_SystemField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Requesting_Version
        {
            get
            {
                return this.Requesting_VersionField;
            }
            set
            {
                this.Requesting_VersionField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="error_details", Namespace="http://schemas.datacontract.org/2004/07/EmailInterfaceServiceV2.shared_Objects")]
    public partial class error_details : object
    {
        
        private EmailService.error_list ErrorCodeField;
        
        private string ErrorMessageField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public EmailService.error_list ErrorCode
        {
            get
            {
                return this.ErrorCodeField;
            }
            set
            {
                this.ErrorCodeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ErrorMessage
        {
            get
            {
                return this.ErrorMessageField;
            }
            set
            {
                this.ErrorMessageField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="error_list", Namespace="http://schemas.datacontract.org/2004/07/EmailInterfaceServiceV2.shared_Objects")]
    public enum error_list : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        non = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        DAL_ERROR = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        SERIALIZATION_ERROR = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        SQL_ERROR = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ITEM_MISSING = 3,
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Email_Status", Namespace="http://schemas.datacontract.org/2004/07/EmailInterfaceServiceV2.Return_Object")]
    public enum Email_Status : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        NotCollected = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ProcessedSuccesfully = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        PendingWithError = 3,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        FailedWithError = 4,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ServiceError = 0,
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ServiceModel.ServiceContractAttribute(ConfigurationName="EmailService.IService1")]
    public interface IService1
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/Send_Mail", ReplyAction="http://tempuri.org/IService1/Send_MailResponse")]
        System.Threading.Tasks.Task<EmailService.SendEmailResponse> Send_MailAsync(EmailService.SendEmailRequest mail);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/Get_Mail_Status", ReplyAction="http://tempuri.org/IService1/Get_Mail_StatusResponse")]
        System.Threading.Tasks.Task<EmailService.GetemailStatusResponse> Get_Mail_StatusAsync(EmailService.GetEmailStatusRequest get_status);
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    public interface IService1Channel : EmailService.IService1, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    public partial class Service1Client : System.ServiceModel.ClientBase<EmailService.IService1>, EmailService.IService1
    {
        
        /// <summary>
        /// Implement this partial method to configure the service endpoint.
        /// </summary>
        /// <param name="serviceEndpoint">The endpoint to configure</param>
        /// <param name="clientCredentials">The client credentials</param>
        static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);
        
        public Service1Client() : 
                base(Service1Client.GetDefaultBinding(), Service1Client.GetDefaultEndpointAddress())
        {
            this.Endpoint.Name = EndpointConfiguration.BasicHttpBinding_IService1.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public Service1Client(EndpointConfiguration endpointConfiguration) : 
                base(Service1Client.GetBindingForEndpoint(endpointConfiguration), Service1Client.GetEndpointAddress(endpointConfiguration))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public Service1Client(EndpointConfiguration endpointConfiguration, string remoteAddress) : 
                base(Service1Client.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public Service1Client(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(Service1Client.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public Service1Client(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        public System.Threading.Tasks.Task<EmailService.SendEmailResponse> Send_MailAsync(EmailService.SendEmailRequest mail)
        {
            return base.Channel.Send_MailAsync(mail);
        }
        
        public System.Threading.Tasks.Task<EmailService.GetemailStatusResponse> Get_Mail_StatusAsync(EmailService.GetEmailStatusRequest get_status)
        {
            return base.Channel.Get_Mail_StatusAsync(get_status);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
        
        private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.BasicHttpBinding_IService1))
            {
                System.ServiceModel.BasicHttpBinding result = new System.ServiceModel.BasicHttpBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                return result;
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.BasicHttpBinding_IService1))
            {
                return new System.ServiceModel.EndpointAddress("http://************/EmailInterfaceServiceV2/Service1.svc");
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.Channels.Binding GetDefaultBinding()
        {
            return Service1Client.GetBindingForEndpoint(EndpointConfiguration.BasicHttpBinding_IService1);
        }
        
        private static System.ServiceModel.EndpointAddress GetDefaultEndpointAddress()
        {
            return Service1Client.GetEndpointAddress(EndpointConfiguration.BasicHttpBinding_IService1);
        }
        
        public enum EndpointConfiguration
        {
            
            BasicHttpBinding_IService1,
        }
    }
}
