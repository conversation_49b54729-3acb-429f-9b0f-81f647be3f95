<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Authors>$(AssemblyName)</Authors>
    <Product>Mydentist.$(AssemblyName)</Product>
    <Company>$(Authors)</Company>
    <AssemblyVersion>1.0.0</AssemblyVersion>
    <FileVersion>1.0.0</FileVersion>
    <PackageId>Mydentist.$(AssemblyName)</PackageId>
    <Version>$(VersionPrefix)</Version>
    <AssemblyName>Mydentist.$(MSBuildProjectName)</AssemblyName>
    <RootNamespace>Mydentist.$(MSBuildProjectName.Replace(" ", "_"))</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.1.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="7.0.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="7.0.4" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="7.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\MyDSitefinityAPI.Domain\MyDSitefinityAPI.Domain.csproj" />
  </ItemGroup>

</Project>
