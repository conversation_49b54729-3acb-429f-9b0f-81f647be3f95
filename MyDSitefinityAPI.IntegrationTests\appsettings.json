{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DbContextWarehouse": "Data Source=myd-dev-clarity;Initial Catalog=Warehouse; Trusted_Connection=True; Encrypt=False", "DBContextWebsiteConfig": "Data Source=myd-dev-clarity;Initial Catalog=WebsiteConfig; Trusted_Connection=True; Encrypt=False"}, "ClinicianPortalSettings": {"ApiKey": "1137cb2f-891b-46fd-a13c-e4f77e78e3b6", "Url": "https://api.mydentist.co.uk/clinicianportal.api.v3.dev/api/"}, "AppSettings": {"LogLocation": "C:\\Logs\\FormSubmission.API\\1.0.0 Test\\log.txt", "RawJsonLogging": "yes", "MapLogging": "yes", "EmailToList": "<EMAIL>", "VacancyApplyApi": "https://api.mydentist.co.uk/Vacancy.Api.V4.Dev/api/Applications/Apply", "VacancyApiKey": "df5da1d8-620c-4636-8c52-3c327ac43a11", "EmailTemplate": "CustomerFeedbackSubmitted.cshtml", "GeneralInquiryTemplate": "GeneralInquiryEmail.cshtml", "ReferralEmailTemplate": "PatientReferralSubmitted.cshtml", "PatientSupportFeedBackUser": "WXHO\\mmckessy", "APIKey": "ec32870c8968aebacab659d03d0098317857142729baeaa3c3da3e116dba685a", "ApplicationEmail": "<EMAIL>", "EmailToOverride": "", "MarketingTeamEmail": "<EMAIL>", "PracticeViewImageApprovalUrl": "http://myd-web/PracticeView/Images/Status", "WebsiteConfigBaseUrl": "https://api.mydentist.co.uk/webconfig.API.V4.Dev/api/", "WebsiteConfigApiKey": "CT-Hvk6OuOtBGlq75VhLEch5zfA5dV1IJmrA", "EmailEndpoint": "http://172.30.1.192/EmailInterfaceServiceV2/Service1.svc", "PracticeApi": "https://apinew.mydentist.co.uk/WebPractice.API.V1.Test", "PracticeApiKey": "11233", "PracticeLocationTreatmentsApi": "https://api.mydentist.co.uk/PracticeLocation.API.V1.Dev/api/Practice/GetNearestPracticeWithTreatments", "PracticeLocationApiKey": "1191661"}}