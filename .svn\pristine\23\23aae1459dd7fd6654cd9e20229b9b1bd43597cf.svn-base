﻿using Mydentist.MyDSitefinityAPI.Domain;
using Mydentist.MyDSitefinityAPI.ImplementationServices.Dtos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mydentist.MyDSitefinityAPI.ImplementationServices.Interfaces
{
    public interface IPracticeManagerServices
    {
        string ComposePracticeManagerMail(string practiceManagerEmail, Referrals referral, string template);
        public Task<PracticeLocationDto> FindNearestPracticeByPostCode(string postCode);
        public Task<PracticeDetailsDto> GetPracticeDetailsById(int id);

    }
}
