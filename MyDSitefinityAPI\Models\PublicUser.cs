﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MyDSitefinityAPI.Models;

[Table("User", Schema = "dbo")]
public class PublicUser
{
    /// <summary>
    /// The unique record ID for the user
    /// </summary>
    [Key]
    [Column("RecordId")]
    public int Id { get; set; }

    /// <summary>
    /// The username of the user
    /// </summary>
    [Required(ErrorMessage = "UserId is required")]
    [Column("UserId")]
    public string UserId { get; set; }

    /// <summary>
    /// The password of the user (salted if fetched from the database)
    /// </summary>
    [Required(ErrorMessage = "Password is required")]
    [DataType(DataType.Password)]
    [Column("Password")]
    public string Password { get; set; }
    
    /// <summary>
    /// The salt for the current user
    /// </summary>
    [Column("Salt")]
    public string Salt { get; set; }

    /// <summary>
    /// Whether the current user is deleted
    /// </summary>
    [Column("Deleted")]
    public bool IsDeleted { get; set; }

    /// <summary>
    /// The image id we're approving
    /// </summary>
    [NotMapped]
    public Guid KeyParameter { get; set; }
}