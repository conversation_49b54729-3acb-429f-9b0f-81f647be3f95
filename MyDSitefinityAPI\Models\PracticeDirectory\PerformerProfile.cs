﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MyDSitefinityAPI.Models.PracticeDirectory
{
    /// <summary>
    /// Represents the public details set for staff member on practice directory
    /// </summary>
    [Table("PerformerDetails", Schema = "Directory")]
    public class PerformerProfile
    {
        [Key]
        [Column("GdcNo")]
        public string GdcNumber { get; set; }

        [Column("Title")]
        public string Title { get; set; }

        [Column("DentalSchoolQualified")]
        public string DentalSchoolQualified { get; set; }

        [Column("CountryQualified")]
        public string CountryQualified { get; set; }

        [Column("YearQualified")]
        public string YearQualified { get; set; }

        [Column("Bio")]
        public string Bio { get; set; }

        [Column("Gender")]
        public string Gender { get; set; }

        [Column("RejectionNote")]
        public string? RejectionNote { get; set; }

        [Column("Image_Id")]
        public Guid? ImageId { get; set; }

        [Column("OnlinePresenceStatus")]
        public int OnlinePresenceStatusId { get; set; }

        [Column("LastEditedBy")]
        public string LastEditedBy { get; set; }

        [Column("LastEditedDate")]
        public DateTime? LastEditedDate { get; set; }

        [Column("FurtherTraining")]
        public string FurtherTraining { get; set; }

        [Column("GdcSpecialistRegistration")]
        public string GdcSpecialistRegistration { get; set; }

        [ForeignKey("GdcNumber")]
        public virtual Performer Performer { get; set; }

        [ForeignKey("GdcNumber")]
        public virtual List<PerformerImage> Images { get; set; }

        [ForeignKey("GdcNumber")]
        public virtual List<PracticePerson> PracticePeople { get; set; }

        [NotMapped]
        public OnlinePresenceStatus OnlinePresenceStatus => (OnlinePresenceStatus)OnlinePresenceStatusId;

        /// <summary>
        /// The token of the user who's currently approving or rejecting the clinician
        /// </summary>
        [NotMapped]
        public string Token { get; set; }
    }

    public enum OnlinePresenceStatus
    {
        WithPracticeManager = 0,
        WithPNSTeam = 1,
        WithMarketingTeam = 2,
        WithWebsite = 3,
        WithNobody = 4
    }
}
