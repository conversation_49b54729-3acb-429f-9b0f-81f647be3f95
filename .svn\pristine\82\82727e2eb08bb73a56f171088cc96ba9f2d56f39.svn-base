﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mydentist.MyDSitefinityAPI.ImplementationServices.Dtos
{
    public class WebsiteCareerApplicantDTO
    {
        public JobInfo Jobinfo { get; set; }
        public WebApplicant Applicant { get; set; }
        public List<JobQuestion> jobQuestions { get; set; }

        public class JobInfo
        {
            public string jobId { get; set; }
            public string jobTitle { get; set; }
            public string jobCompany { get; set; }
            public string jobLocation { get; set; }
        }

        public class WebApplicant
        {
            public string firstName { get; set; }
            public string lastName { get; set; }
            public string email { get; set; }
            public string phone { get; set; }
            public string Homepostcode { get; set; }
        }

        public class Answer
        {
            public string AnswerId { get; set; }
            public string AnswerValue { get; set; }
        }


        public class FollowupAnswer
        {
            public string AnswerId { get; set; }
            public string AnswerValue { get; set; }
        }

        public class JobQuestion
        {
            public string QuestionId { get; set; }
            public string Question { get; set; }
            public string QuestionType { get; set; }
            public List<Answer> Answers { get; set; }
            public List<FollowUpQuestion> FollowUpQuestions { get; set; }
        }
        public class FollowUpQuestion
        {
            public string FollowupQuestionId { get; set; }
            public string FollowupQuestion { get; set; }
            public List<FollowupAnswer> followupAnswers { get; set; }
            public int ParentQuestionId { get; set; }
        }




    }
}
