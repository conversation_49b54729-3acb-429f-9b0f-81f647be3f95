﻿@model  MyDSitefinityAPI.Models.CustomerFeedbackViewModel

<table cellspacing="0" cellpadding="20" border="0" width="100%" bgcolor="#d0e1e0">
    <tr>
        <td>

            <table cellspacing="0" cellpadding="20" border="0" width="640" align="center" bgcolor="#FFFFFF">
                <tr>
                    <td>

                        <table cellspacing="0" cellpadding="0" border="0" width="100%">
                            <tr>
                                <td>
                                    <img src="https://www.mydentist.co.uk/Sitefinity/WebsiteTemplates/Mydentist/App_Themes/Mydentist2015/images/site-assets/md_logo.png" alt="mydentist - helping the nation smile" width="265" height="70" />
                                </td>
                            </tr>

                            <tr>
                                <td height="20"></td>
                            </tr>

                            <tr>
                                <td>
                                    <p style="font-size:22px; font-family:arial,verdana,sans-serif; font-weight:bold; margin:10px 0; color:#000000; letter-spacing:-0.05em;">Customer Feedback</p>
                                    <p style="font-family:arial,verdana,sans-serif; margin:10px 0; color:#1b2523;">
                                        The below customer has left the following feedback for your practice via the mydentist website.
                                    </p>
                                </td>
                            </tr>

                            <tr>
                                <td>

                                    <table cellspacing="0" cellpadding="5" border="0" width="100%" style="font-size:14px; font-family:arial,verdana,sans-serif; color:#1b2523;">
                                        <tr>
                                            <td colspan="2" height="10"></td>
                                        </tr>
                                        <tr>
                                            <td colspan="2" height="10"></td>
                                        </tr>
                                        <tr>
                                            <td colspan="2" style="border-bottom:2px solid #d0e1e0;"><strong style="font-size:16px;">Feedback details</strong></td>
                                        </tr>
                                        @if (Model.PracticeName != null)
                                        {
                                            <tr>
                                                <td style="border-bottom:1px dashed #d0e1e0;"><strong>Practice Name</strong></td>
                                                <td style="border-bottom:1px dashed #d0e1e0;">@Model.PracticeName</td>
                                            </tr>
                                        }
                                        @if (Model.FeedbackType != null)
                                        {
                                            <tr>
                                                <td style="border-bottom:1px dashed #d0e1e0;"><strong>Would you like to leave a compliment or complaint?</strong></td>
                                                <td style="border-bottom:1px dashed #d0e1e0;">@Model.FeedbackType</td>
                                            </tr>
                                        }
                                        @if (Model.PatientName != null)
                                        {
                                            <tr>
                                                <td style="border-bottom:1px dashed #d0e1e0;"><strong>Your name</strong></td>
                                                <td style="border-bottom:1px dashed #d0e1e0;">@Model.PatientName</td>
                                            </tr>
                                        }
                                        @if (Model.Email != null)
                                        {
                                            <tr>
                                                <td style="border-bottom:1px dashed #d0e1e0;"><strong>Your email address</strong></td>
                                                <td style="border-bottom:1px dashed #d0e1e0;">@Model.Email</td>
                                            </tr>
                                        }
                                        @if (Model.PhoneNumber != null)
                                        {
                                            <tr>
                                                <td style="border-bottom:1px dashed #d0e1e0;"><strong>Your phone number</strong></td>
                                                <td style="border-bottom:1px dashed #d0e1e0;">@Model.PhoneNumber</td>
                                            </tr>
                                        }
                                        @if (Model.FeedbackTitle != null)
                                        {
                                            <tr>
                                                <td style="border-bottom:1px dashed #d0e1e0;"><strong>Brief description</strong></td>
                                                <td style="border-bottom:1px dashed #d0e1e0;">@Model.FeedbackTitle</td>
                                            </tr>
                                        }
                                        @if (Model.FeedbackInformation != null)
                                        {
                                            <tr>
                                                <td style="border-bottom:1px dashed #d0e1e0;"><strong>Your feedback</strong></td>
                                                <td style="border-bottom:1px dashed #d0e1e0;">@Model.FeedbackInformation</td>
                                            </tr>
                                        }
                                    </table>

                                </td>
                            </tr>
                            <tr>
                                <td height="20"></td>
                            </tr>
                        </table>

                    </td>
                </tr>
            </table>

        </td>
    </tr>
</table>