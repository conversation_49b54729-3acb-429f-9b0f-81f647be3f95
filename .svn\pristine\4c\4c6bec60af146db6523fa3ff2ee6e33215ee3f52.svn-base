﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MyDSitefinityAPI.Models
{
    [Table("sf_quickcontactpmform", Schema = "dbo")]

    public class sf_quickcontactpmform
    {
        [Key]
        [Column("id")]
        public Guid id { get; set; }
        public string? Form_Text_Box__C004 { get; set; }
        public string? Form_Text_Box__C003 { get; set; }
        public string? Form_Text_Box { get; set; }
        public string? Hidden_Practice_Id { get; set; }
        public string? Form_Text_Box__C011 { get; set; }
        public string? Form_Text_Box__C010 { get; set; }

        public string? Form_Text_Box__C015 { get; set; }
        public string? g_a_client_id { get; set; }
        public string? Processed_Date { get; set; }
        public string? Error_Date { get; set; }
        public string? RawJson { get; set; }
        public string? TreatmentOptions { get; set; }
    }
}
