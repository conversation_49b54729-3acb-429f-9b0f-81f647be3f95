# Gemini Project Instructions: MyDSitefinityAPI

This file contains project-specific instructions and guidelines for the Gemini AI assistant. It ensures that all modifications and contributions adhere to the established standards of the `MyDSitefinityAPI` project.

## 1. General Principles

- **Adhere to Existing Conventions:** Before making any changes, analyze the surrounding code, file structure, and naming conventions. All new code must be consistent with the existing style.
- **Separation of Concerns:** Respect the established project structure (e.g., `Domain`, `Persistence`, `ImplementationServices`). Logic should be placed in the appropriate layer.
- **SOLID Principles:** Apply SOLID principles in all refactoring and new feature development.

## 2. Refactoring Plan

This plan will be executed step-by-step. After each step, the project will be compiled and all integration tests will be run to ensure no functionality is broken.

1.  **Dependency Cleanup:**
    -   Remove the unused `EntityFramework` NuGet package from `MyDSitefinityAPI.csproj`.
    -   Remove the unused `EntityFramework` NuGet package from `MyDSitefinityAPI.Persistence.csproj`.

2.  **Security Enhancement:**
    -   Move the hardcoded credentials and URLs in `RecruitmentHubService.cs` to `appsettings.json`.
    -   Update the code to read these values from configuration.

3.  **Code Quality Improvements:**
    -   **`PracticeManagerService.cs`**: Address the `//todo` by implementing or removing the commented-out code for `GetPracticeDetailsById`.
    -   **`PracticeManagerService.cs`**: Refactor the `ComposePracticeManagerMail` method to use a more robust templating approach instead of chained `.Replace()` calls.
    -   **`RecruitmentFormTransformerService.cs`**: Create a generic helper method to handle the repeated null-checking and logging logic to reduce code duplication.
    -   **`RecruitmentWebsiteFormImplentationService.cs`**: Improve the readability of the `SwitchDetailsIfReferral` method.

4.  **Documentation:**
    -   Update this `GEMINI.md` file as needed.
    -   Add comments to explain any complex or non-obvious code that is introduced during the refactoring process.

## 3. Code Style & Naming

- **Naming:** Follow Microsoft's C# naming conventions (e.g., `PascalCase` for classes, methods, and properties; `camelCase` for local variables and method arguments).
- **Formatting:** Maintain the existing code formatting. If an `.editorconfig` file is added to the project, it will be treated as the single source of truth for all formatting rules.
- **Comments:** Add comments only to explain the *why* behind complex or non-obvious code, not the *what*.

## 4. Dependency Management

- **NuGet Packages:** Do not add new NuGet packages without explicit instruction. When asked to update packages, do so cautiously and verify that there are no breaking changes.
- **Unused Dependencies:** If you identify unused `using` statements or package references, you may remove them as part of a refactoring task.
