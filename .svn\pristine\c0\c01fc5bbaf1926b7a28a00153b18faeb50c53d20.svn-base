
using MyDSitefinityAPI.Converters;
using System.Text.Json.Serialization;

namespace MyDSitefinityAPI
{

    public class SitefinityForm
    {
        public string Name { get; set; }
        public string SiteId { get; set; }
        public object? Item { get; set; }
        public FormEvent OriginalEvent { get; set; }
        public string OriginalEventType { get; set; }
     }

        public class FormEvent
        {
            public string EntryId { get; set; }
            public string ReferralCode { get; set; }
            public string UserId { get; set; }
            public string Username { get; set; }
            public string IpAddress { get; set; }
            public DateTime SubmissionTime { get; set; }
            public string FormId { get; set; }
            public string FormName { get; set; }
            public string FormTitle { get; set; }
            public string FormSubscriptionListId { get; set; }
            public bool SendConfirmationEmail { get; set; }
            public Control[] Controls { get; set; }
            public object? Origin { get; set; }
        }

        public class Control
        {
            public object? FieldControlName { get; set; }
            public string Id { get; set; }
            public string SiblingId { get; set; }
            public object? Text { get; set; }
            public int Type { get; set; }
            public string Title { get; set; }
            public string FieldName { get; set; }

            [JsonConverter(typeof(StringOrArrayConverter))]
            public string Value { get; set; }
            public object? OldValue { get; set; }
        }
    
}