{"Name": "Form is submitted", "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49", "Item": {"ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"156\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Testing2304\",\"lastName\":\"test56\",\"email\":\"<EMAIL>\",\"phone\":\"0789956985\",\"Homepostcode\":\"m26 1gg\"},\"jobQuestions\":[{\"QuestionId\":\"15\",\"Question\":\"\",\"QuestionType\":\"\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"24\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"14\",\"FollowupQuestion\":\"\",\"FollowupQuestionType\":\"\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"125693\"}],\"ParentQuestionId\":15}]}]}", "FileFieldController": null}, "OriginalEvent": {"EntryId": "3af62a9d-cae7-6322-9971-ff0000979a84", "ReferralCode": "28", "UserId": "d7530c9a-cae7-6322-9971-ff0000979a84", "Username": "Chitra", "IpAddress": "::1", "SubmissionTime": "2024-05-07T10:23:56.7298278Z", "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84", "FormName": "sf_recruitmentjobapplication", "FormTitle": "Recruitment Job Application", "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000", "SendConfirmationEmail": false, "Controls": [{"FieldControlName": null, "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84", "SiblingId": "00000000-0000-0000-0000-000000000000", "Text": null, "Type": 0, "Title": "PayloadJson", "FieldName": "ParagraphTextFieldController", "Value": "{\"Jobinfo\":{\"jobId\":\"156\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Testing2304\",\"lastName\":\"test56\",\"email\":\"<EMAIL>\",\"phone\":\"0789956985\",\"Homepostcode\":\"m26 1gg\"},\"jobQuestions\":[{\"QuestionId\":\"15\",\"Question\":\"\",\"QuestionType\":\"\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"24\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"14\",\"FollowupQuestion\":\"\",\"FollowupQuestionType\":\"\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"125693\"}],\"ParentQuestionId\":15}]}]}", "OldValue": null}, {"FieldControlName": null, "Id": "ded52a9d-cae7-6322-9971-ff0000979a84", "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 1, "Title": "CV upload", "FieldName": "FileFieldController", "Value": null, "OldValue": null}], "Origin": null}, "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent, Telerik.Sitefinity, Version=15.0.8227.0, Culture=neutral, PublicKeyToken=b28c218413bdf563"}