﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mydentist.MyDSitefinityAPI.WebConfigApi
{
    [Table("RolePostgraduateLookup")]
    public class RolePostgraduateLookup
    {
        [Key]
        public int Id { get; set; }
        public string WebsiteRole { get; set; }
        public string InternalPostgraduateDefinition { get; set; }
    }
}
