﻿using System;
using System.ServiceModel;
using System.ServiceModel.Channels;
using System.ServiceModel.Configuration;
using System.ServiceModel.Description;
using System.ServiceModel.Dispatcher;
using HttpRequestMessageProperty = System.ServiceModel.Channels.HttpRequestMessageProperty;

namespace SitefinityAuthentication
{
    /// <summary>
    /// Adding authentication via endpoint behaviour. Sending the usernames and passwords every time was causing Sitefinity to throw an exception due to logging in too quickly.
    /// Credit to https://blogs.msmvps.com/paulomorgado/2007/04/26/wcf-building-an-http-user-agent-message-inspector/
    /// </summary>
    public class SitefinityEndpointBehaviour : BehaviorExtensionElement, IEndpointBehavior, IClientMessageInspector
    {
        public override Type BehaviorType => typeof(SitefinityEndpointBehaviour);
        
        public void Validate(ServiceEndpoint endpoint)
        {
        }

        public void AddBindingParameters(ServiceEndpoint endpoint, BindingParameterCollection bindingParameters)
        {
        }

        public void ApplyDispatchBehavior(ServiceEndpoint endpoint, EndpointDispatcher endpointDispatcher)
        {
        }

        public void ApplyClientBehavior(ServiceEndpoint endpoint, ClientRuntime clientRuntime)
        {
            clientRuntime.MessageInspectors.Add(this);
        }

        public object BeforeSendRequest(ref Message request, IClientChannel channel)
        {
            HttpRequestMessageProperty requestProperty = null;

            if (request.Properties.TryGetValue(HttpRequestMessageProperty.Name, out object existingRequestProperty))
                requestProperty = existingRequestProperty as HttpRequestMessageProperty;

            if (requestProperty == null)
            {
                requestProperty = new HttpRequestMessageProperty();
                request.Properties.Add(HttpRequestMessageProperty.Name, requestProperty);
            };

            requestProperty.Headers["Cookie"] = SitefinityAuthenticator.GetAuthenticationCookieHeader();

            return null;
        }

        public void AfterReceiveReply(ref Message reply, object correlationState)
        {
        }

        protected override object CreateBehavior()
        {
            return this;
        }

    }
}
