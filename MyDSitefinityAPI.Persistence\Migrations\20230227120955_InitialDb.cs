﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Mydentist.MyDSitefinityAPI.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class InitialDb : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "Recruitment");

            migrationBuilder.CreateTable(
                name: "RecruitmentWebsiteForm",
                schema: "Recruitment",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    FirstName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    LastName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ContactNumber = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Email = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    DesiredWorkingLocation = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    DesiredRole = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    GdcNumber = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ReferrerName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ReferrerGdcNumber = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ReferrerEmailAddress = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ReferrerContactNumber = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    FormName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    GAClientId = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Channel = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ClinicianType = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    NonClinicianType = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CreatedDateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    IsProcessed = table.Column<bool>(type: "bit", nullable: true),
                    ProcessedDateTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RecruitmentWebsiteForm", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "RecruitmentWebsiteForm",
                schema: "Recruitment");
        }
    }
}
