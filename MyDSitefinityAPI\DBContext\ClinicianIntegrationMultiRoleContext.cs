﻿using MyDSitefinityAPI.Interfaces;
using MyDSitefinityAPI.Models;
using System.Data.Entity;

namespace MyDSitefinityAPI.DBContext
{
    public class ClinicianIntegrationMultiRoleContext : DbContext, IClinicianIntegrationMultiRoleContext
    {
        public ClinicianIntegrationMultiRoleContext() : base("name=ClinicianIntegrationMultiRole")
        {
        }

        public DbSet<SharedList> sharedList { get; set; }
    }
}
