---
type: "always_apply"
---

## Responsibilities for Safe Refactoring

 must follow these strict constraints when performing refactoring in production code:

### ✅ Scope & Safety Constraints
- Only refactor **code areas explicitly marked for change**.
- Never modify:
  - Public APIs
  - Mission-critical paths
  - Legacy modules without full test coverage
- All changes must be **minimal and justifiable**.

### ✅ Change Plan & Review
- Before making changes:
  - Generate a **Refactor Candidate List** with:
    - File/function name
    - Risk level (Low/Medium/High)
    - Justification for change
  - Create a **Change Plan Document** per PR with:
    - What is being changed
    - Why the change improves code
    - How it will be verified
    - Rollback or undo strategy

### ✅ Execution Process
- Make changes in **small, atomic PRs** (1 module/function per PR).
- Every PR must include:
  - Before/After diff summary
  - Automated test results
  - Updated coverage report

### ✅ Compliance & Testing
- Ensure all changes:
  - Pass linting and static analysis checks
  - Are covered by existing or new tests
  - Are reversible and easy to trace in Git history

### ✅ Tools to Support Refactoring
Use the following tools during analysis and implementation:
- **Static Analysis**: ESLint, Code<PERSON>, Pylint, SonarQube
- **Test Coverage**: Istanbul, Coverage.py
- **Safe Edits**: Codemod, RefactorBot, Augment AI

### ❗️Never Do
- Never auto-refactor entire files or classes blindly
- Never remove code that lacks test coverage
- Never introduce stylistic-only changes unless explicitly requested

---

> must remain **auditable, minimal, and non-destructive** at every step.
