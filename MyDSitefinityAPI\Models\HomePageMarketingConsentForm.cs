﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace MyDSitefinityAPI.Models
{
    [Table("sf_homepagemarketingsignup", Schema = "dbo")]

    public class HomePageMarketingConsentForm
    {
        [Key]
        [Column("Id")]
        public int Id { get; set; }
        public string EntryId { get; set; }
        public string? ChosenMarketingPromos { get; set; }
        public string? RawJson { get; set; }
        public DateTime CreatedDate { get; set; }
        public string? Email { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? PhoneNumber { get; set; }
        public DateTime? DOB { get; set; }
        public string? Postcode { get; set; }
        public string PracticePostcode { get; set; }
        public Int64? PracticeId { get; set; }

    }
}
