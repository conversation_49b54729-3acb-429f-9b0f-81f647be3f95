﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;

using log4net;
using log4net.Config;
using log4net.Appender;
using log4net.Core;
using log4net.Filter;
using log4net.Layout;
using log4net.Repository.Hierarchy;
using System.Reflection;
using System.Diagnostics;

namespace IDHGroup.SharedLibraries.Logging
{
    public class Log
    {
        private ILog _logger;
        private string _applicationName;

        public Log()
        {
            Assembly assembly = Assembly.GetEntryAssembly();

            FileVersionInfo fvi = FileVersionInfo.GetVersionInfo(assembly.Location);
            string version = fvi.FileVersion;

            _applicationName = fvi.FileName.Replace(".exe", string.Empty);

            _logger = LogManager.GetLogger(_applicationName);

            ThreadContext.Properties["Version"] = version;
            ThreadContext.Properties["ApplicationName"] = _applicationName;
            ThreadContext.Properties["Server"] = Environment.MachineName;

            XmlConfigurator.Configure();
        }

        #region Infos

        public void Info(string message) 
        {
            _logger.Info(message);
        }

        public static void Info(string applicationName, string message)
        {
            ILog logger = LogManager.GetLogger(applicationName);

            Assembly assembly = Assembly.GetExecutingAssembly();
            FileVersionInfo fvi = FileVersionInfo.GetVersionInfo(assembly.Location);
            string version = fvi.FileVersion;

            ThreadContext.Properties["Version"] = version;
            ThreadContext.Properties["ApplicationName"] = applicationName;
            ThreadContext.Properties["Server"] = Environment.MachineName;

            XmlConfigurator.Configure();

            logger.Info(message);       
        }

        #endregion

        #region Debugs
       
        public void Debug(string message)
        {
            _logger.Debug(message);
        }

        public static void Debug(string applicationName, string message)
        {
            ILog logger = LogManager.GetLogger(applicationName);

            Assembly assembly = Assembly.GetExecutingAssembly();
            FileVersionInfo fvi = FileVersionInfo.GetVersionInfo(assembly.Location);
            string version = fvi.FileVersion;

            ThreadContext.Properties["Version"] = version;
            ThreadContext.Properties["ApplicationName"] = applicationName;
            ThreadContext.Properties["Server"] = Environment.MachineName;

            XmlConfigurator.Configure();

            logger.Debug(message);
        }

        #endregion

        #region Warns

        public void Warn(string message)
        {
            _logger.Warn(message);
        }

        public static void Warn(string applicationName, string message)
        {
            ILog logger = LogManager.GetLogger(applicationName);

            Assembly assembly = Assembly.GetExecutingAssembly();
            FileVersionInfo fvi = FileVersionInfo.GetVersionInfo(assembly.Location);
            string version = fvi.FileVersion;

            ThreadContext.Properties["Version"] = version;
            ThreadContext.Properties["ApplicationName"] = applicationName;
            ThreadContext.Properties["Server"] = Environment.MachineName;

            XmlConfigurator.Configure();

            logger.Warn(message);
        }

        #endregion

        #region Errors

        public void Error(string message)
        {
            _logger.Error(message);
        }

        public void Error(string message, Exception ex)
        {
            Assembly assembly = Assembly.GetExecutingAssembly();
            FileVersionInfo fvi = FileVersionInfo.GetVersionInfo(assembly.Location);
            string version = fvi.FileVersion;

            ThreadContext.Properties["Version"] = version;
            ThreadContext.Properties["Server"] = Environment.MachineName;

            XmlConfigurator.Configure();

            _logger.Error(message, ex);
        }

        public static void Error(string applicationName, string message, Exception ex)
        {
            ILog logger = LogManager.GetLogger(applicationName);

            Assembly assembly = Assembly.GetExecutingAssembly();
            FileVersionInfo fvi = FileVersionInfo.GetVersionInfo(assembly.Location);
            string version = fvi.FileVersion;

            ThreadContext.Properties["Version"] = version;
            ThreadContext.Properties["Server"] = Environment.MachineName;
            ThreadContext.Properties["ApplicationName"] = applicationName; 

            XmlConfigurator.Configure();

            logger.Error(message, ex);        
        }

        #endregion

        #region Fatals

        public void Fatal(string message)
        {
            _logger.Fatal(message);
        }

        public static void Fatal(string applicationName, string message)
        {
            ILog logger = LogManager.GetLogger(applicationName);

            Assembly assembly = Assembly.GetExecutingAssembly();
            FileVersionInfo fvi = FileVersionInfo.GetVersionInfo(assembly.Location);
            string version = fvi.FileVersion;

            ThreadContext.Properties["Version"] = version;
            ThreadContext.Properties["Server"] = Environment.MachineName;
            ThreadContext.Properties["ApplicationName"] = applicationName; 

            XmlConfigurator.Configure();

            logger.Fatal(message);
        }

        public static void Fatal(string applicationName, string message, Exception ex)
        {
            ILog logger = LogManager.GetLogger(applicationName);

            Assembly assembly = Assembly.GetExecutingAssembly();
            FileVersionInfo fvi = FileVersionInfo.GetVersionInfo(assembly.Location);
            string version = fvi.FileVersion;

            ThreadContext.Properties["Version"] = version;
            ThreadContext.Properties["Server"] = Environment.MachineName;
            ThreadContext.Properties["ApplicationName"] = applicationName; 

            XmlConfigurator.Configure();

            logger.Fatal(message, ex);
        }

        #endregion
    }
}
