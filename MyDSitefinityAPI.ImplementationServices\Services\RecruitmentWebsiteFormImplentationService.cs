﻿using AutoMapper;
using Microsoft.Extensions.Logging;
using Mydentist.MyDSitefinityAPI.Domain;
using Mydentist.MyDSitefinityAPI.ImplementationService;
using Mydentist.MyDSitefinityAPI.Persistence;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mydentist.MyDSitefinityAPI.ImplementationServices
{
    public class RecruitmentWebsiteFormImplentationService: IRecruitmentWebsiteFormImplentationService
    {
        private readonly IRecruitmentWebsiteFormService _recruitmentWebsiteFormService;
        private readonly IRecruitmentFormTransformerService _recruitmentFormTransformerService;
        private readonly IMapper _mapper;
        private readonly ILogger _logger;

        public RecruitmentWebsiteFormImplentationService(IRecruitmentWebsiteFormService recruitmentWebsiteFormService
                                                    , IRecruitmentFormTransformerService recruitmentFormTransformerService
                                                    , IMapper mapper
                                                    , ILogger logger)
        {
            _recruitmentWebsiteFormService = recruitmentWebsiteFormService;
            _recruitmentFormTransformerService = recruitmentFormTransformerService;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task <RecruitmentWebsiteFormModel> ProcessRequest(RecruitmentWebsiteFormRequest recruitmentWebsiteFormRequest)
        {
            RecruitmentWebsiteFormModel recruitmentWebsiteFormModelWithIds = await _recruitmentFormTransformerService.InjectSharedListValuesAndSetChannel(recruitmentWebsiteFormRequest);

            RecruitmentWebsiteFormModel recruitmentWebsiteFormModelWithoutIds = _mapper.Map<RecruitmentWebsiteFormModel>(recruitmentWebsiteFormRequest);

            _mapper.Map(recruitmentWebsiteFormModelWithIds, recruitmentWebsiteFormModelWithoutIds);

            recruitmentWebsiteFormModelWithoutIds = SwitchDetailsIfReferral(recruitmentWebsiteFormModelWithoutIds);

            await _recruitmentWebsiteFormService.Add(recruitmentWebsiteFormModelWithoutIds);

            return recruitmentWebsiteFormModelWithoutIds;
        }

        public RecruitmentWebsiteFormModel SwitchDetailsIfReferral(RecruitmentWebsiteFormModel recruitmentWebsiteFormModel)
        {
            if (!string.IsNullOrEmpty(recruitmentWebsiteFormModel.ReferrerName))
            {
                _logger.LogInformation($"Recruitment Form Identified as Referral as ReferrerName '{recruitmentWebsiteFormModel.ReferrerName}' has a value.");

                string refereeName = recruitmentWebsiteFormModel.ReferrerName;
                string refereeGdcNumber = recruitmentWebsiteFormModel.ReferrerGdcNumber;
                string refereeContactNumber = recruitmentWebsiteFormModel.ReferrerContactNumber;
                string refereeEmailAddress = recruitmentWebsiteFormModel.ReferrerEmailAddress;

                string refererFirstName = recruitmentWebsiteFormModel.FirstName;
                string refererLastName = recruitmentWebsiteFormModel.LastName;
                string refererEmail = recruitmentWebsiteFormModel.Email;
                string refererContactNumber = recruitmentWebsiteFormModel.ContactNumber;
                string refererGdcNumber = recruitmentWebsiteFormModel.GdcNumber;

                recruitmentWebsiteFormModel.Email = refereeEmailAddress;
                recruitmentWebsiteFormModel.GdcNumber = refereeGdcNumber;
                recruitmentWebsiteFormModel.ContactNumber = refereeContactNumber;
                recruitmentWebsiteFormModel.FirstName = refereeName.FirstName();
                recruitmentWebsiteFormModel.LastName = refereeName.LastName();

                recruitmentWebsiteFormModel.ReferrerEmailAddress = refererEmail;
                recruitmentWebsiteFormModel.ReferrerGdcNumber = refererGdcNumber;
                recruitmentWebsiteFormModel.ReferrerContactNumber = refererContactNumber;
                recruitmentWebsiteFormModel.ReferrerName = $"{refererFirstName} {refererLastName}";
            }
            else
                _logger.LogInformation($"Recruitment Form not deemed to be a Referral as ReferrerName as it is null or empty.");

            return recruitmentWebsiteFormModel;
        }
    }
}
