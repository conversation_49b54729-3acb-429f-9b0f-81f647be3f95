﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using MyDSitefinityAPI.Models.PracticeDirectory;

namespace MyDSitefinityAPI.IntegrationTests.Tests.Services
{
    [TestClass]
    public class PerformerProfileServiceTest : Test
    {
        [TestMethod]
        public void GetImageById()
        {
            PerformerProfile? performerProfile = PerformerProfileService.GetByImageId(TestPerformerProfile.ImageId.Value);

            Assert.IsNotNull(performerProfile, "Performer profile was not returned");
            Assert.AreEqual(TestPerformerProfile.ImageId, performerProfile.ImageId);
            Assert.AreEqual(TestPerformerProfile.GdcNumber, performerProfile.GdcNumber);
            Assert.AreEqual(TestPerformerProfile.OnlinePresenceStatusId, performerProfile.OnlinePresenceStatusId);
            Assert.AreEqual(TestPerformerProfile.Bio, performerProfile.Bio);
            Assert.AreEqual(TestPerformerProfile.CountryQualified, performerProfile.CountryQualified);
            Assert.AreEqual(TestPerformerProfile.DentalSchoolQualified, performerProfile.DentalSchoolQualified);
            Assert.AreEqual(TestPerformerProfile.FurtherTraining, performerProfile.FurtherTraining);
            Assert.AreEqual(TestPerformerProfile.GdcSpecialistRegistration, performerProfile.GdcSpecialistRegistration);
            Assert.AreEqual(TestPerformerProfile.Gender, performerProfile.Gender);
            Assert.AreEqual(TestPerformerProfile.LastEditedBy, performerProfile.LastEditedBy);
            Assert.AreEqual(TestPerformerProfile.LastEditedDate, performerProfile.LastEditedDate);
            Assert.AreEqual(TestPerformerProfile.RejectionNote, performerProfile.RejectionNote);

            Assert.IsNotNull(performerProfile.Performer, "Performer was not included");
            Assert.AreEqual(TestPerformerProfile.Performer.Name, performerProfile.Performer.Name);
            Assert.AreEqual(TestPerformerProfile.Performer.GdcNumber, performerProfile.Performer.GdcNumber);

            Assert.IsNotNull(performerProfile.Images, "Performer images weren't included");

            PerformerImage? image = performerProfile.Images.FirstOrDefault();
            Assert.IsNotNull(image, "Performer profile should have at least 1 image");
            Assert.AreEqual(TestPerformerImage.ImageId, image.ImageId);
            Assert.AreEqual(TestPerformerImage.PracticeId, image.PracticeId);

            Assert.IsNotNull(image.Practice, "Image's originating practice was not included");
            Assert.IsNotNull(TestPractice.PracticeName);
            Assert.IsNotNull(TestPractice.PMEmail);
            Assert.IsNotNull(TestPractice.PMName);

            Assert.AreEqual(TestPractice.PracticeID, image.Practice.PracticeID);
            Assert.AreEqual(TestPractice.PracticeName, image.Practice.PracticeName);
            Assert.AreEqual(TestPractice.PMEmail, image.Practice.PMEmail);
            Assert.AreEqual(TestPractice.PMName, image.Practice.PMName);
        }

        [TestMethod]
        public void ApprovePerformer()
        {
            TestPerformerProfile.Bio = "wjfwhbfuywegbvfdyuwegfyuwe";
            TestPerformerProfile.RejectionNote = "Not good enough";
            PerformerProfileService.ApprovePerformer(TestPerformerProfile, TestPublicUser);

            PerformerProfile approvedPerformer = DbWarehouseContext.PerformerProfiles.First(performerProfile => performerProfile.GdcNumber == TestPerformerProfile.GdcNumber);
            
            Assert.AreEqual(OnlinePresenceStatus.WithMarketingTeam, approvedPerformer.OnlinePresenceStatus);
            Assert.AreEqual("wjfwhbfuywegbvfdyuwegfyuwe", approvedPerformer.Bio);
            Assert.IsNull(approvedPerformer.RejectionNote);
        }

        [TestMethod]
        public void RejectPerformer()
        {
            TestPerformerProfile.Bio = "yfgb278grf268gr7862g3672g36";
            TestPerformerProfile.RejectionNote = "Not good enough";
            PerformerProfileService.RejectPerformer(TestPerformerProfile, TestPublicUser);

            PerformerProfile approvedPerformer = DbWarehouseContext.PerformerProfiles.First(performerProfile => performerProfile.GdcNumber == TestPerformerProfile.GdcNumber);
            
            Assert.AreEqual(OnlinePresenceStatus.WithPracticeManager, approvedPerformer.OnlinePresenceStatus);
            Assert.AreEqual("yfgb278grf268gr7862g3672g36", approvedPerformer.Bio);
            Assert.AreEqual("Not good enough", approvedPerformer.RejectionNote);
        }

        [TestMethod]
        public async Task NotifyPracticeOfApprovalAsync()
        {
            await PerformerProfileService.NotifyPracticeOfApprovalAsync(TestPerformerProfile);
        }

        [TestMethod]
        public async Task NotifyPracticeOfRejectionAsync()
        {
            await PerformerProfileService.NotifyPracticeOfRejectionAsync(TestPerformerProfile);
        }

        [TestMethod]
        public async Task NotifyMarketingOfApprovalAsync()
        {
            await PerformerProfileService.NotifyMarketingOfApprovalAsync(TestPerformerProfile);
        }

        [TestMethod]
        public void UpdateSitefinityClinician()
        {
            // Clinician must be approved so the website can pick it up
            TestPerformerProfile.OnlinePresenceStatusId = (int)OnlinePresenceStatus.WithMarketingTeam;
            DbWarehouseContext.SaveChanges();

            // The PNS team are approving the bio, not marketing. We have no need to send an image
            PerformerProfileService.UpdateSitefinityClinician(TestPerformerProfile, TestPerformerImage.PracticeId);
        }
    }
}
