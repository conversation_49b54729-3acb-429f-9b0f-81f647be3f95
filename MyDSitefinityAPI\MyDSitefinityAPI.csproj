<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>
	<PropertyGroup>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<NoWarn>$(NoWarn);1591</NoWarn>
		<Company>mydentist</Company>
		<Copyright>2023</Copyright>
		<AssemblyVersion>4.0.3</AssemblyVersion>
		<FileVersion>4.0.3</FileVersion>
	</PropertyGroup>
	<ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="7.0.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json.Bson" Version="1.0.2" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="6.0.2" />
    <PackageReference Include="Serilog.Extensions.Logging.File" Version="3.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Swagger" Version="6.4.0" />
    <PackageReference Include="EntityFramework" Version="6.4.4" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.2.3" />
    <PackageReference Include="Swashbuckle.AspNetCore.Filters" Version="7.0.5" />
    <PackageReference Include="System.ServiceModel.Duplex" Version="4.10.*" />
    <PackageReference Include="System.ServiceModel.Federation" Version="4.10.*" />
    <PackageReference Include="System.ServiceModel.Http" Version="4.10.*" />
    <PackageReference Include="System.ServiceModel.NetTcp" Version="4.10.*" />
    <PackageReference Include="System.ServiceModel.Security" Version="4.10.*" />
    <PackageReference Include="System.Text.Json" Version="7.0.0" />
	</ItemGroup>
	<ItemGroup>
	  <Content Update="app.config">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </Content>
	  <Content Update="app.Release.config">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </Content>
	  <Content Update="appsettings.json">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </Content>
	  <Content Update="Views\Emails\Compliment.cshtml">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </Content>
	  <Content Update="Views\Emails\Complaint.cshtml">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </Content>
	  <Content Update="Views\Emails\ClinicianApproved-Marketing.cshtml">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </Content>
	  <Content Update="Views\Emails\ClinicianRejected.cshtml">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </Content>
	  <Content Update="Views\Emails\CustomerFeedbackSubmitted.cshtml">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </Content>
	  <Content Update="Views\Emails\ClinicianApproved.cshtml">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </Content>
	  <Content Update="Views\Emails\GeneralInquiryEmail.cshtml">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </Content>
	  <Content Update="Views\Emails\PatientReferralSubmitted.cshtml">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </Content>
	</ItemGroup>
	<ItemGroup>
	  <ProjectReference Include="..\MyDSitefinityAPI.ImplementationServices\MyDSitefinityAPI.ImplementationServices.csproj" />
	  <ProjectReference Include="..\SitefinityServiceCaller.Core\SitefinityServiceCaller.Core.csproj" />
	</ItemGroup>
	<ItemGroup>
		<Folder Include="Connected Services\" />
	</ItemGroup>
	<ItemGroup>
	  <None Update="Emails\ClinicianReferringForm.html">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </None>
	  <None Update="Emails\PatientReferralForm.html">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </None>
	</ItemGroup>
  <ProjectExtensions><VisualStudio><UserProperties /></VisualStudio></ProjectExtensions>
</Project>
