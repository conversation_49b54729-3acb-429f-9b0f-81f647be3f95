﻿using MyDSitefinityAPI.Interfaces;
using MyDSitefinityAPI.Models;
using System.Data.Entity;

namespace MyDSitefinityAPI.DBContext
{
    public class GroupWebsitePracticeDataContext : DbContext, IGroupWebsitePracticeDataContext
    {
        public GroupWebsitePracticeDataContext() : base("name=GroupWebsitePracticeData")
        {
        }

        public DbSet<PublicUser> PublicUsers { get; set; }
        public DbSet<WidgetOptions> WidgetOptions { get; set; }
    }
}
