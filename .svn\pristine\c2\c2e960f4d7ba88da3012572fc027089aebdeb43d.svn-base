﻿using Microsoft.AspNetCore.Mvc;
using MyDSitefinityAPI.CustomAttributes;
using MyDSitefinityAPI.Interfaces;
using System.Text.Json;
using System.Net;
using MyDSitefinityAPI.Models;
using Swashbuckle.AspNetCore.Filters;

namespace MyDSitefinityAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]/[action]")]
    public class VacancyController : ControllerBase
    {
        private IVacancyService _vacancyService;

        public VacancyController(IVacancyService vacancyService)
        {
           _vacancyService = vacancyService;
        }

        /// <summary>
        /// Applies to a vacancy
        /// </summary>
        /// <response code="201">Application created Successfully</response>
        /// <response code="500">Server Error</response>
        [HttpPost]
        [ApiKeyAuth]
        public async Task<ActionResult<string>> ApplyToVacancy([FromHeader(Name = "X-ApiKey")] string header, JsonElement json)
        {          
            var result = await _vacancyService.ApplyForVacancy(json);
            if(result == HttpStatusCode.OK)
            {
                return CreatedAtAction(nameof(ApplyToVacancy), $"Application complete");
            }
            else
            {
                return StatusCode(StatusCodes.Status500InternalServerError, "Error processing the application");
            }
        }

    }
}
