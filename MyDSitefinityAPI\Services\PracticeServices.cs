﻿using MyDSitefinityAPI.Interfaces;
using MyDSitefinityAPI.Models;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;

namespace MyDSitefinityAPI.Services
{
    public class PracticeServices: IPracticeServices
    {
        private readonly HttpClient _httpClient;
        private readonly string _practiceApi;

        public PracticeServices(IConfiguration configuration, HttpClient httpClient)
        {
            _practiceApi = $"{configuration.GetSection("AppSettings:PracticeApi").Value}/api/Practices";
            _httpClient = httpClient;

            if (!_httpClient.DefaultRequestHeaders.Contains("APIKey"))
            {
                _httpClient.DefaultRequestHeaders.Add("APIKey", configuration.GetSection("AppSettings:PracticeApiKey").Value);
            }

            _httpClient.BaseAddress = new Uri(_practiceApi);
        }

        public async Task<PracticeEmail> GetPracticeEmailAsync(string practiceId, string type)
        {
            PracticeEmail practiceEmail = await GetPracticeApiResultAsync<PracticeEmail>($"{_practiceApi}/{practiceId}/Email?type={type}").ConfigureAwait(false);
            return practiceEmail;
        }

        private async Task<TResult> GetPracticeApiResultAsync<TResult>(string path)
        {
            HttpResponseMessage response = await _httpClient.GetAsync(path).ConfigureAwait(false);
            string responseBody = await response.Content.ReadAsStringAsync().ConfigureAwait(false); // Reading the responses body before checking the success code allows us to read potential error messages
            response.EnsureSuccessStatusCode();

            var practiceList = JsonConvert.DeserializeObject<TResult>(responseBody);
            return practiceList;
        }

    }
}
