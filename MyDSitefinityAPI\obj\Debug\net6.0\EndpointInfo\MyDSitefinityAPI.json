{"openapi": "3.0.1", "info": {"title": "mydentist Sitefinity API", "description": "An API for interaction between Sitefinity and the mydentist backend", "version": "v1"}, "paths": {"/workflow/ClinicianApproval/Login": {"get": {"tags": ["ClinicianA<PERSON>roval"], "summary": "Displays a login form for the current user", "parameters": [{"name": "imageId", "in": "query", "description": "The ID of the image the user wants to approve", "schema": {"type": "string", "format": "uuid"}}, {"name": "token", "in": "query", "description": "A salted username", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}, "post": {"tags": ["ClinicianA<PERSON>roval"], "summary": "Authenticates a user with a username and password", "requestBody": {"description": "A PublicUser object representing the current user", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublicUser"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PublicUser"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PublicUser"}}}}, "responses": {"200": {"description": "Success"}}}}, "/workflow/ClinicianApproval/ApproveClinician": {"get": {"tags": ["ClinicianA<PERSON>roval"], "parameters": [{"name": "imageId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "token", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}, "post": {"tags": ["ClinicianA<PERSON>roval"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"GdcNumber": {"type": "string"}, "Title": {"type": "string"}, "DentalSchoolQualified": {"type": "string"}, "CountryQualified": {"type": "string"}, "YearQualified": {"type": "string"}, "Bio": {"type": "string"}, "Gender": {"type": "string"}, "RejectionNote": {"type": "string"}, "ImageId": {"type": "string", "format": "uuid"}, "OnlinePresenceStatusId": {"type": "integer", "format": "int32"}, "LastEditedBy": {"type": "string"}, "LastEditedDate": {"type": "string", "format": "date-time"}, "FurtherTraining": {"type": "string"}, "GdcSpecialistRegistration": {"type": "string"}, "Performer.GdcNumber": {"type": "string"}, "Performer.Name": {"type": "string"}, "Performer.PerformerProfile.GdcNumber": {"type": "string"}, "Performer.PerformerProfile.Title": {"type": "string"}, "Performer.PerformerProfile.DentalSchoolQualified": {"type": "string"}, "Performer.PerformerProfile.CountryQualified": {"type": "string"}, "Performer.PerformerProfile.YearQualified": {"type": "string"}, "Performer.PerformerProfile.Bio": {"type": "string"}, "Performer.PerformerProfile.Gender": {"type": "string"}, "Performer.PerformerProfile.RejectionNote": {"type": "string"}, "Performer.PerformerProfile.ImageId": {"type": "string", "format": "uuid"}, "Performer.PerformerProfile.OnlinePresenceStatusId": {"type": "integer", "format": "int32"}, "Performer.PerformerProfile.LastEditedBy": {"type": "string"}, "Performer.PerformerProfile.LastEditedDate": {"type": "string", "format": "date-time"}, "Performer.PerformerProfile.FurtherTraining": {"type": "string"}, "Performer.PerformerProfile.GdcSpecialistRegistration": {"type": "string"}, "Performer.PerformerProfile.Performer.GdcNumber": {"type": "string"}, "Performer.PerformerProfile.Performer.Name": {"type": "string"}, "Performer.PerformerProfile.Performer.PerformerProfile": {"$ref": "#/components/schemas/PerformerProfile"}, "Performer.PerformerProfile.Images": {"type": "array", "items": {"$ref": "#/components/schemas/PerformerImage"}}, "Performer.PerformerProfile.PracticePeople": {"type": "array", "items": {"$ref": "#/components/schemas/PracticePerson"}}, "Performer.PerformerProfile.OnlinePresenceStatus": {"$ref": "#/components/schemas/OnlinePresenceStatus"}, "Performer.PerformerProfile.Token": {"type": "string", "description": "The token of the user who's currently approving or rejecting the clinician"}, "Images": {"type": "array", "items": {"$ref": "#/components/schemas/PerformerImage"}}, "PracticePeople": {"type": "array", "items": {"$ref": "#/components/schemas/PracticePerson"}}, "OnlinePresenceStatus": {"$ref": "#/components/schemas/OnlinePresenceStatus"}, "Token": {"type": "string", "description": "The token of the user who's currently approving or rejecting the clinician"}}}, "encoding": {"GdcNumber": {"style": "form"}, "Title": {"style": "form"}, "DentalSchoolQualified": {"style": "form"}, "CountryQualified": {"style": "form"}, "YearQualified": {"style": "form"}, "Bio": {"style": "form"}, "Gender": {"style": "form"}, "RejectionNote": {"style": "form"}, "ImageId": {"style": "form"}, "OnlinePresenceStatusId": {"style": "form"}, "LastEditedBy": {"style": "form"}, "LastEditedDate": {"style": "form"}, "FurtherTraining": {"style": "form"}, "GdcSpecialistRegistration": {"style": "form"}, "Performer.GdcNumber": {"style": "form"}, "Performer.Name": {"style": "form"}, "Performer.PerformerProfile.GdcNumber": {"style": "form"}, "Performer.PerformerProfile.Title": {"style": "form"}, "Performer.PerformerProfile.DentalSchoolQualified": {"style": "form"}, "Performer.PerformerProfile.CountryQualified": {"style": "form"}, "Performer.PerformerProfile.YearQualified": {"style": "form"}, "Performer.PerformerProfile.Bio": {"style": "form"}, "Performer.PerformerProfile.Gender": {"style": "form"}, "Performer.PerformerProfile.RejectionNote": {"style": "form"}, "Performer.PerformerProfile.ImageId": {"style": "form"}, "Performer.PerformerProfile.OnlinePresenceStatusId": {"style": "form"}, "Performer.PerformerProfile.LastEditedBy": {"style": "form"}, "Performer.PerformerProfile.LastEditedDate": {"style": "form"}, "Performer.PerformerProfile.FurtherTraining": {"style": "form"}, "Performer.PerformerProfile.GdcSpecialistRegistration": {"style": "form"}, "Performer.PerformerProfile.Performer.GdcNumber": {"style": "form"}, "Performer.PerformerProfile.Performer.Name": {"style": "form"}, "Performer.PerformerProfile.Performer.PerformerProfile": {"style": "form"}, "Performer.PerformerProfile.Images": {"style": "form"}, "Performer.PerformerProfile.PracticePeople": {"style": "form"}, "Performer.PerformerProfile.OnlinePresenceStatus": {"style": "form"}, "Performer.PerformerProfile.Token": {"style": "form"}, "Images": {"style": "form"}, "PracticePeople": {"style": "form"}, "OnlinePresenceStatus": {"style": "form"}, "Token": {"style": "form"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/SitefinityForm": {"post": {"tags": ["SitefinityForm"], "summary": "Sends a sitefinity form to the mydentist backend.", "description": " Sample request:\r\n\r\n POST /Sitefinityform\r\n   {\r\n  \"Name\": \"Form is submitted\",\r\n  \"SiteId\": \"75c4d617-8e6d-4716-b9eb-a9ef6f2fc2ae\",\r\n  \"Item\": null,\r\n  \"OriginalEvent\": {\r\n    \"EntryId\": \"cbc0d3b7-1018-475f-be5c-896d5b925c5d\",\r\n    \"ReferralCode\": \"1\",\r\n    \"UserId\": \"c824f8d5-83fe-478e-8ed0-ade5c30bc884\",\r\n    \"Username\": \"<EMAIL>\",\r\n    \"IpAddress\": \"127.0.0.1\",\r\n    \"SubmissionTime\": \"2022-09-02T10:47:31.3136116Z\",\r\n    \"FormId\": \"9289ac17-1a2f-4251-9c11-0ec298b8c45e\",\r\n    \"FormName\": \"sf_quickcontactpmform\",\r\n    \"FormTitle\": \"TestForm\",\r\n    \"FormSubscriptionListId\": \"00000000-0000-0000-0000-000000000000\",\r\n    \"SendConfirmationEmail\": false,\r\n    \"Controls\": [\r\n      {\r\n        \"FieldControlName\": null,\r\n        \"Id\": \"ab1b06cc-1d4d-40fd-8244-503db911153f\",\r\n        \"SiblingId\": \"00000000-0000-0000-0000-000000000000\",\r\n        \"Text\": null,\r\n        \"Type\": 0,\r\n        \"Title\": \"Random\",\r\n        \"FieldName\": \"FormTextBox_C004\",\r\n        \"Value\": \"<EMAIL>\",\r\n        \"OldValue\": null\r\n      },\r\n      {\r\n        \"FieldControlName\": null,\r\n        \"Id\": \"ab1b06cc-1d4d-40fd-8244-503db911153f\",\r\n        \"SiblingId\": \"00000000-0000-0000-0000-000000000000\",\r\n        \"Text\": null,\r\n        \"Type\": 0,\r\n        \"Title\": \"Random\",\r\n        \"FieldName\": \"FormTextBox_C003\",\r\n        \"Value\": \"07777777779\",\r\n        \"OldValue\": null\r\n      },\r\n      {\r\n    \"FieldControlName\": null,\r\n        \"Id\": \"ab1b06cc-1d4d-40fd-8244-503db911153f\",\r\n        \"SiblingId\": \"00000000-0000-0000-0000-000000000000\",\r\n        \"Text\": null,\r\n        \"Type\": 0,\r\n        \"Title\": \"Random\",\r\n        \"FieldName\": \"FormTextBox_C001\",\r\n        \"Value\": null,\r\n        \"OldValue\": null\r\n      },\r\n      {\r\n    \"FieldControlName\": null,\r\n        \"Id\": \"ab1b06cc-1d4d-40fd-8244-503db911153f\",\r\n        \"SiblingId\": \"00000000-0000-0000-0000-000000000000\",\r\n        \"Text\": null,\r\n        \"Type\": 0,\r\n        \"Title\": \"Random\",\r\n        \"FieldName\": \"FormTextBox\",\r\n        \"Value\": \"General_Enquiry\",\r\n        \"OldValue\": null\r\n      },\r\n      {\r\n    \"FieldControlName\": null,\r\n        \"Id\": \"ab1b06cc-1d4d-40fd-8244-503db911153f\",\r\n        \"SiblingId\": \"00000000-0000-0000-0000-000000000000\",\r\n        \"Text\": null,\r\n        \"Type\": 0,\r\n        \"Title\": \"Random\",\r\n        \"FieldName\": \"HiddenPracticeId\",\r\n        \"Value\": \"240=>{my}dentist, Queensway, Bognor Regis\",\r\n        \"OldValue\": null\r\n      },\r\n      {\r\n    \"FieldControlName\": null,\r\n        \"Id\": \"ab1b06cc-1d4d-40fd-8244-503db911153f\",\r\n        \"SiblingId\": \"00000000-0000-0000-0000-000000000000\",\r\n        \"Text\": null,\r\n        \"Type\": 0,\r\n        \"Title\": \"Random\",\r\n        \"FieldName\": \"FormTextBox_C011\",\r\n        \"Value\": \"Tambo 2\",\r\n        \"OldValue\": null\r\n      },\r\n      {\r\n    \"FieldControlName\": null,\r\n        \"Id\": \"ab1b06cc-1d4d-40fd-8244-503db911153f\",\r\n        \"SiblingId\": \"00000000-0000-0000-0000-000000000000\",\r\n        \"Text\": null,\r\n        \"Type\": 0,\r\n        \"Title\": \"Random\",\r\n        \"FieldName\": \"FormTextBox_C010\",\r\n        \"Value\": \"Another test\",\r\n        \"OldValue\": null\r\n      },\r\n      {\r\n    \"FieldControlName\": null,\r\n        \"Id\": \"ab1b06cc-1d4d-40fd-8244-503db911153f\",\r\n        \"SiblingId\": \"00000000-0000-0000-0000-000000000000\",\r\n        \"Text\": null,\r\n        \"Type\": 0,\r\n        \"Title\": \"Random\",\r\n        \"FieldName\": \"FormTextBox_C015\",\r\n        \"Value\": \"20/07/2003\",\r\n        \"OldValue\": null\r\n      },\r\n      {\r\n    \"FieldControlName\": null,\r\n        \"Id\": \"ab1b06cc-1d4d-40fd-8244-503db911153f\",\r\n        \"SiblingId\": \"00000000-0000-0000-0000-000000000000\",\r\n        \"Text\": null,\r\n        \"Type\": 0,\r\n        \"Title\": \"Random\",\r\n        \"FieldName\": \"GAClientId\",\r\n        \"Value\": \"1373058472.1613488417\",\r\n        \"OldValue\": null\r\n      }\r\n    ]\r\n  }\r\n}", "parameters": [{"name": "X-Api<PERSON>ey", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {}, "example": {"name": "mydestist <PERSON><PERSON><PERSON>", "siteId": "23", "item": null, "originalEvent": {"entryId": "cbc0d3b7-1018-475f-be5c-896d5b925c5d", "referralCode": "2", "userId": "c824f8d5-83fe-478e-8ed0-ade5c30bc884", "username": "mydentistadmin", "ipAddress": "127.0.0.1", "submissionTime": "2025-08-01T21:19:03.1718395+01:00", "formId": "9289ac17-1a2f-4251-9c11-0ec298b8c45e", "formName": "sf_quickcontactpmform", "formTitle": "TestForm", "formSubscriptionListId": "00000000-0000-0000-0000-000000000000", "sendConfirmationEmail": false, "controls": [{"fieldControlName": "something", "id": "ab1b06cc-1d4d-40fd-8244-503db911153f", "siblingId": "00000000-0000-0000-0000-000000000000", "text": "Something", "type": 0, "title": "Random", "fieldName": "FormTextBox_C004", "value": "<EMAIL>", "oldValue": "something"}, {"fieldControlName": null, "id": "ab1b06cc-1d4d-40fd-8244-503db911153f", "siblingId": "00000000-0000-0000-0000-000000000000", "text": null, "type": 0, "title": "Random", "fieldName": "FormTextBox_C003", "value": "07737477179", "oldValue": null}, {"fieldControlName": null, "id": "ab1b06cc-1d4d-40fd-8244-503db911153f", "siblingId": "00000000-0000-0000-0000-000000000000", "text": null, "type": 0, "title": "Random", "fieldName": "FormTextBox_C001", "value": null, "oldValue": null}, {"fieldControlName": null, "id": "ab1b06cc-1d4d-40fd-8244-503db911153f", "siblingId": "00000000-0000-0000-0000-000000000000", "text": null, "type": 0, "title": "Random", "fieldName": "FormTextBox", "value": "General_Enquiry", "oldValue": null}, {"fieldControlName": null, "id": "ab1b06cc-1d4d-40fd-8244-503db911153f", "siblingId": "00000000-0000-0000-0000-000000000000", "text": null, "type": 0, "title": "Random", "fieldName": "HiddenPracticeId", "value": "240=>{my}dentist, Queensway, Bognor Regis", "oldValue": null}, {"fieldControlName": null, "id": "ab1b06cc-1d4d-40fd-8244-503db911153f", "siblingId": "00000000-0000-0000-0000-000000000000", "text": null, "type": 0, "title": "Random", "fieldName": "FormTextBox_C011", "value": "Tambo 2", "oldValue": null}, {"fieldControlName": null, "id": "ab1b06cc-1d4d-40fd-8244-503db911153f", "siblingId": "00000000-0000-0000-0000-000000000000", "text": null, "type": 0, "title": "Random", "fieldName": "FormTextBox_C010", "value": "Another test", "oldValue": null}, {"fieldControlName": null, "id": "ab1b06cc-1d4d-40fd-8244-503db911153f", "siblingId": "00000000-0000-0000-0000-000000000000", "text": null, "type": 0, "title": "Random", "fieldName": "FormTextBox_C015", "value": "20/07/2003", "oldValue": null}, {"fieldControlName": null, "id": "ab1b06cc-1d4d-40fd-8244-503db911153f", "siblingId": "00000000-0000-0000-0000-000000000000", "text": null, "type": 0, "title": "Random", "fieldName": "GAClientId", "value": "1373058472.1613488417", "oldValue": null}], "origin": null}, "originalEventType": "Testing"}}, "text/json": {"schema": {}, "example": {"name": "mydestist <PERSON><PERSON><PERSON>", "siteId": "23", "item": null, "originalEvent": {"entryId": "cbc0d3b7-1018-475f-be5c-896d5b925c5d", "referralCode": "2", "userId": "c824f8d5-83fe-478e-8ed0-ade5c30bc884", "username": "mydentistadmin", "ipAddress": "127.0.0.1", "submissionTime": "2025-08-01T21:19:03.1718395+01:00", "formId": "9289ac17-1a2f-4251-9c11-0ec298b8c45e", "formName": "sf_quickcontactpmform", "formTitle": "TestForm", "formSubscriptionListId": "00000000-0000-0000-0000-000000000000", "sendConfirmationEmail": false, "controls": [{"fieldControlName": "something", "id": "ab1b06cc-1d4d-40fd-8244-503db911153f", "siblingId": "00000000-0000-0000-0000-000000000000", "text": "Something", "type": 0, "title": "Random", "fieldName": "FormTextBox_C004", "value": "<EMAIL>", "oldValue": "something"}, {"fieldControlName": null, "id": "ab1b06cc-1d4d-40fd-8244-503db911153f", "siblingId": "00000000-0000-0000-0000-000000000000", "text": null, "type": 0, "title": "Random", "fieldName": "FormTextBox_C003", "value": "07737477179", "oldValue": null}, {"fieldControlName": null, "id": "ab1b06cc-1d4d-40fd-8244-503db911153f", "siblingId": "00000000-0000-0000-0000-000000000000", "text": null, "type": 0, "title": "Random", "fieldName": "FormTextBox_C001", "value": null, "oldValue": null}, {"fieldControlName": null, "id": "ab1b06cc-1d4d-40fd-8244-503db911153f", "siblingId": "00000000-0000-0000-0000-000000000000", "text": null, "type": 0, "title": "Random", "fieldName": "FormTextBox", "value": "General_Enquiry", "oldValue": null}, {"fieldControlName": null, "id": "ab1b06cc-1d4d-40fd-8244-503db911153f", "siblingId": "00000000-0000-0000-0000-000000000000", "text": null, "type": 0, "title": "Random", "fieldName": "HiddenPracticeId", "value": "240=>{my}dentist, Queensway, Bognor Regis", "oldValue": null}, {"fieldControlName": null, "id": "ab1b06cc-1d4d-40fd-8244-503db911153f", "siblingId": "00000000-0000-0000-0000-000000000000", "text": null, "type": 0, "title": "Random", "fieldName": "FormTextBox_C011", "value": "Tambo 2", "oldValue": null}, {"fieldControlName": null, "id": "ab1b06cc-1d4d-40fd-8244-503db911153f", "siblingId": "00000000-0000-0000-0000-000000000000", "text": null, "type": 0, "title": "Random", "fieldName": "FormTextBox_C010", "value": "Another test", "oldValue": null}, {"fieldControlName": null, "id": "ab1b06cc-1d4d-40fd-8244-503db911153f", "siblingId": "00000000-0000-0000-0000-000000000000", "text": null, "type": 0, "title": "Random", "fieldName": "FormTextBox_C015", "value": "20/07/2003", "oldValue": null}, {"fieldControlName": null, "id": "ab1b06cc-1d4d-40fd-8244-503db911153f", "siblingId": "00000000-0000-0000-0000-000000000000", "text": null, "type": 0, "title": "Random", "fieldName": "GAClientId", "value": "1373058472.1613488417", "oldValue": null}], "origin": null}, "originalEventType": "Testing"}}, "application/*+json": {"schema": {}, "example": {"name": "mydestist <PERSON><PERSON><PERSON>", "siteId": "23", "item": null, "originalEvent": {"entryId": "cbc0d3b7-1018-475f-be5c-896d5b925c5d", "referralCode": "2", "userId": "c824f8d5-83fe-478e-8ed0-ade5c30bc884", "username": "mydentistadmin", "ipAddress": "127.0.0.1", "submissionTime": "2025-08-01T21:19:03.1718395+01:00", "formId": "9289ac17-1a2f-4251-9c11-0ec298b8c45e", "formName": "sf_quickcontactpmform", "formTitle": "TestForm", "formSubscriptionListId": "00000000-0000-0000-0000-000000000000", "sendConfirmationEmail": false, "controls": [{"fieldControlName": "something", "id": "ab1b06cc-1d4d-40fd-8244-503db911153f", "siblingId": "00000000-0000-0000-0000-000000000000", "text": "Something", "type": 0, "title": "Random", "fieldName": "FormTextBox_C004", "value": "<EMAIL>", "oldValue": "something"}, {"fieldControlName": null, "id": "ab1b06cc-1d4d-40fd-8244-503db911153f", "siblingId": "00000000-0000-0000-0000-000000000000", "text": null, "type": 0, "title": "Random", "fieldName": "FormTextBox_C003", "value": "07737477179", "oldValue": null}, {"fieldControlName": null, "id": "ab1b06cc-1d4d-40fd-8244-503db911153f", "siblingId": "00000000-0000-0000-0000-000000000000", "text": null, "type": 0, "title": "Random", "fieldName": "FormTextBox_C001", "value": null, "oldValue": null}, {"fieldControlName": null, "id": "ab1b06cc-1d4d-40fd-8244-503db911153f", "siblingId": "00000000-0000-0000-0000-000000000000", "text": null, "type": 0, "title": "Random", "fieldName": "FormTextBox", "value": "General_Enquiry", "oldValue": null}, {"fieldControlName": null, "id": "ab1b06cc-1d4d-40fd-8244-503db911153f", "siblingId": "00000000-0000-0000-0000-000000000000", "text": null, "type": 0, "title": "Random", "fieldName": "HiddenPracticeId", "value": "240=>{my}dentist, Queensway, Bognor Regis", "oldValue": null}, {"fieldControlName": null, "id": "ab1b06cc-1d4d-40fd-8244-503db911153f", "siblingId": "00000000-0000-0000-0000-000000000000", "text": null, "type": 0, "title": "Random", "fieldName": "FormTextBox_C011", "value": "Tambo 2", "oldValue": null}, {"fieldControlName": null, "id": "ab1b06cc-1d4d-40fd-8244-503db911153f", "siblingId": "00000000-0000-0000-0000-000000000000", "text": null, "type": 0, "title": "Random", "fieldName": "FormTextBox_C010", "value": "Another test", "oldValue": null}, {"fieldControlName": null, "id": "ab1b06cc-1d4d-40fd-8244-503db911153f", "siblingId": "00000000-0000-0000-0000-000000000000", "text": null, "type": 0, "title": "Random", "fieldName": "FormTextBox_C015", "value": "20/07/2003", "oldValue": null}, {"fieldControlName": null, "id": "ab1b06cc-1d4d-40fd-8244-503db911153f", "siblingId": "00000000-0000-0000-0000-000000000000", "text": null, "type": 0, "title": "Random", "fieldName": "GAClientId", "value": "1373058472.1613488417", "oldValue": null}], "origin": null}, "originalEventType": "Testing"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}, "201": {"description": "Form Created Successfully"}, "500": {"description": "Form is not created"}, "400": {"description": "Bad JSON formatting"}}}}}, "components": {"schemas": {"OnlinePresenceStatus": {"enum": [0, 1, 2, 3, 4], "type": "integer", "format": "int32"}, "Performer": {"type": "object", "properties": {"gdcNumber": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "performerProfile": {"$ref": "#/components/schemas/PerformerProfile"}}, "additionalProperties": false}, "PerformerImage": {"type": "object", "properties": {"imageId": {"type": "string", "format": "uuid"}, "practiceId": {"type": "integer", "format": "int32"}, "gdcNumber": {"type": "string", "nullable": true}, "performerProfile": {"$ref": "#/components/schemas/PerformerProfile"}, "practice": {"$ref": "#/components/schemas/PracticeDetails"}}, "additionalProperties": false}, "PerformerProfile": {"type": "object", "properties": {"gdcNumber": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "dentalSchoolQualified": {"type": "string", "nullable": true}, "countryQualified": {"type": "string", "nullable": true}, "yearQualified": {"type": "string", "nullable": true}, "bio": {"type": "string", "nullable": true}, "gender": {"type": "string", "nullable": true}, "rejectionNote": {"type": "string", "nullable": true}, "imageId": {"type": "string", "format": "uuid", "nullable": true}, "onlinePresenceStatusId": {"type": "integer", "format": "int32"}, "lastEditedBy": {"type": "string", "nullable": true}, "lastEditedDate": {"type": "string", "format": "date-time", "nullable": true}, "furtherTraining": {"type": "string", "nullable": true}, "gdcSpecialistRegistration": {"type": "string", "nullable": true}, "performer": {"$ref": "#/components/schemas/Performer"}, "images": {"type": "array", "items": {"$ref": "#/components/schemas/PerformerImage"}, "nullable": true}, "practicePeople": {"type": "array", "items": {"$ref": "#/components/schemas/PracticePerson"}, "nullable": true}, "onlinePresenceStatus": {"$ref": "#/components/schemas/OnlinePresenceStatus"}, "token": {"type": "string", "description": "The token of the user who's currently approving or rejecting the clinician", "nullable": true}}, "additionalProperties": false, "description": "Represents the public details set for staff member on practice directory"}, "PracticeDetails": {"type": "object", "properties": {"practiceID": {"type": "integer", "format": "int32"}, "practiceName": {"type": "string", "nullable": true}, "pmEmail": {"type": "string", "nullable": true}, "pmName": {"type": "string", "nullable": true}, "pmFirstname": {"type": "string", "nullable": true}, "pmSurname": {"type": "string", "nullable": true}, "performerImages": {"type": "array", "items": {"$ref": "#/components/schemas/PerformerImage"}, "description": "A lazy-loaded collection of performer images which originated from the current practice", "nullable": true}, "practicePeople": {"type": "array", "items": {"$ref": "#/components/schemas/PracticePerson"}, "nullable": true}}, "additionalProperties": false}, "PracticePerson": {"type": "object", "properties": {"practicePersonId": {"type": "integer", "format": "int32"}, "practiceId": {"type": "integer", "format": "int32"}, "gdcNumber": {"type": "string", "nullable": true}, "roleId": {"type": "integer", "format": "int32"}, "performerProfile": {"$ref": "#/components/schemas/PerformerProfile"}, "role": {"$ref": "#/components/schemas/Role"}, "practicePersonLinks": {"type": "array", "items": {"$ref": "#/components/schemas/PracticePersonLink"}, "nullable": true}, "practice": {"$ref": "#/components/schemas/PracticeDetails"}}, "additionalProperties": false}, "PracticePersonLink": {"type": "object", "properties": {"practicePersonLinkId": {"type": "integer", "format": "int32"}, "practicePersonId": {"type": "integer", "format": "int32"}, "isLeaver": {"type": "boolean"}, "leaverDate": {"type": "string", "format": "date-time", "nullable": true}, "practicePerson": {"$ref": "#/components/schemas/PracticePerson"}}, "additionalProperties": false}, "PublicUser": {"required": ["password", "userId"], "type": "object", "properties": {"id": {"type": "integer", "description": "The unique record ID for the user", "format": "int32"}, "userId": {"type": "string", "description": "The username of the user"}, "password": {"type": "string", "description": "The password of the user (salted if fetched from the database)", "format": "password"}, "salt": {"type": "string", "description": "The salt for the current user", "nullable": true}, "isDeleted": {"type": "boolean", "description": "Whether the current user is deleted"}, "keyParameter": {"type": "string", "description": "The image id we're approving", "format": "uuid"}}, "additionalProperties": false}, "Role": {"type": "object", "properties": {"roleId": {"type": "integer", "format": "int32"}, "roleTypeId": {"type": "integer", "format": "int32"}, "practicePeople": {"type": "array", "items": {"$ref": "#/components/schemas/PracticePerson"}, "nullable": true}, "roleType": {"$ref": "#/components/schemas/RoleType"}}, "additionalProperties": false}, "RoleType": {"type": "object", "properties": {"roleTypeId": {"type": "integer", "format": "int32"}, "description": {"type": "string", "nullable": true}, "roles": {"type": "array", "items": {"$ref": "#/components/schemas/Role"}, "nullable": true}}, "additionalProperties": false}}}}