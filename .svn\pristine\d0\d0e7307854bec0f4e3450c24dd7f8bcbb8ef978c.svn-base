﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace MyDSitefinityAPI.Models
{
    [Table("CMS.vw_Directory_Practice_Details")]
    public class CMSPracticeDetails
    {
        [Key]
        [Column("PracticeID")]
        public int? PraticeID { get; set; }
        [Column("Name")]
        public string Name { get; set; }

        [Column("TCO_Email_Address")]
        public string TCO_Email_Address { get; set; }
        [Column("PMEmail")]
        public string PMEmail { get; set; }
    }
}
