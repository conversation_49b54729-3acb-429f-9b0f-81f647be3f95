﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <OutputType>Library</OutputType>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Connected Services\**" />
    <Content Remove="Connected Services\**" />
    <EmbeddedResource Remove="Connected Services\**" />
    <None Remove="Connected Services\**" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="Connected Services\EmailService\ConnectedService.json" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.4.0" />
    <PackageReference Include="Moq" Version="4.18.4" />
    <PackageReference Include="MSTest.TestAdapter" Version="2.2.10" />
    <PackageReference Include="MSTest.TestFramework" Version="2.2.10" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\MyDSitefinityApi.ClinicianPortalApi\MyDSitefinityApi.ClinicianPortalApi.csproj" />
    <ProjectReference Include="..\MyDSitefinityAPI.ImplementationServices\MyDSitefinityAPI.ImplementationServices.csproj" />
    <ProjectReference Include="..\MyDSitefinityAPI\MyDSitefinityAPI.csproj" />
  </ItemGroup>

  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="copy &quot;$(outDir)MyDSitefinityAPI.IntegrationTests.dll.config&quot; &quot;$(outDir)testhost.dll.config&quot;" />
  </Target>

</Project>
