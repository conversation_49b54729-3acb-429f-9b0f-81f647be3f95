﻿using Mydentist.MyDSitefinityAPI.ImplementationServices;
using MyDSitefinityAPI.DBContext;
using MyDSitefinityAPI.EmailHelpers;
using MyDSitefinityAPI.Interfaces;
using MyDSitefinityAPI.Models;
using System.Data;
using MyDSitefinityAPI.ExtensionMethods;
using System.Text.Json;
using System.Web;
using Mydentist.MyDSitefinityAPI.Domain;
using Mydentist.MyDSitefinityAPI.ImplementationServices.Interfaces;
using System.Data.Entity.Migrations;
using System.Data.Entity.Infrastructure;
using EmailService;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.Extensions.Logging;

namespace MyDSitefinityAPI.Services
{
    public class SitefinityFormService : ISitefinityFormService
    {
        private IRecruitmentWebsiteFormImplentationService _recruitmentWebsiteFormImplementationService;
        private DBWarehouseContext db { get; set; }
        private IRecruitmentHubService _recruitmentHubService { get; set; } 
        private GroupWebsitePracticeDataContext dbgroupPracticeContext { get; set; }
        private IEmailSender _emailSender { get; set; }
        private IVacancyDbService _vacancyService { get; set; }
        private IFindNearestPracticeService _findNearestPracticeService { get; set; }
        

        private ILogger<SitefinityFormService> _logger { get; set; }

        private readonly IConfiguration _configuration;

        private readonly IPracticeManagerServices _practiceManagerServices;
        private readonly IPracticeServices _practiceServices;
        public SitefinityFormService(
            IEmailSender emailSender,
            ILogger<SitefinityFormService> logger,
            IPracticeManagerServices practiceManagerServices,
            IConfiguration configuration,
            IRecruitmentWebsiteFormImplentationService recruitmentWebsiteFormImplementationService,
            IRecruitmentHubService recruitmentHubService,
            IVacancyDbService vacancyDbService,
            IFindNearestPracticeService findNearestPracticeService,
            IPracticeServices practiceServices)
        {
            db = new DBWarehouseContext();
            dbgroupPracticeContext = new GroupWebsitePracticeDataContext();
            _emailSender = emailSender;
            _logger = logger;
            _configuration = configuration;
            _practiceManagerServices = practiceManagerServices;
            _recruitmentWebsiteFormImplementationService = recruitmentWebsiteFormImplementationService;
            _recruitmentHubService = recruitmentHubService;
            _vacancyService =  vacancyDbService;
            _findNearestPracticeService = findNearestPracticeService;
            _practiceServices = practiceServices;
        }

        public async Task<bool> InsertQuickContactForm(SitefinityForm quickContactForm, string rawJson)
        {
            try
            {
                _logger.LogInformation("Begin QuickContactform insertion. EntryId: {EntryId}", quickContactForm.OriginalEvent.EntryId);
                if (_configuration.GetSection("AppSettings:RawJsonLogging").Value == "yes")
                    _logger.LogInformation("QuickContactform raw JSON. EntryId: {EntryId}, RawJson: {RawJson}", quickContactForm.OriginalEvent.EntryId, rawJson);

                sf_quickcontactpmform form = new sf_quickcontactpmform()
                {

                    Form_Text_Box = quickContactForm.OriginalEvent.Controls.Where(x => x.FieldName == "FormTextBox").FirstOrDefault()?.Value,
                    Form_Text_Box__C003 = quickContactForm.OriginalEvent.Controls.Where(x => x.FieldName == "FormTextBox_C003").FirstOrDefault()?.Value,
                    Form_Text_Box__C004 = quickContactForm.OriginalEvent.Controls.Where(x => x.FieldName == "FormTextBox_C004").FirstOrDefault()?.Value,
                    Form_Text_Box__C010 = quickContactForm.OriginalEvent.Controls.Where(x => x.FieldName == "FormTextBox_C010").FirstOrDefault()?.Value,
                    Form_Text_Box__C011 = quickContactForm.OriginalEvent.Controls.Where(x => x.FieldName == "FormTextBox_C011").FirstOrDefault()?.Value,
                    Form_Text_Box__C015 = quickContactForm.OriginalEvent.Controls.Where(x => x.FieldName == "FormTextBox_C015").FirstOrDefault()?.Value,
                    g_a_client_id = quickContactForm.OriginalEvent.Controls.Where(x => x.FieldName == "GAClientId").FirstOrDefault()?.Value,
                    Hidden_Practice_Id = quickContactForm.OriginalEvent.Controls.Where(x => x.FieldName == "HiddenPracticeId").FirstOrDefault()?.Value,
                    Processed_Date = null,
                    id =Guid.NewGuid(),
                    Error_Date = null,
                    RawJson = rawJson,
                    TreatmentOptions =  quickContactForm.GetPropertyValueByTitle("Interested In").Replace("\r\n","")
                };

                if (!string.IsNullOrEmpty(form.Hidden_Practice_Id?.ToString()))
                {
                    string trailText = "=" + form.Hidden_Practice_Id.Split(new char[] { '=' })[1];
                    form.Hidden_Practice_Id = AssessPracticeClosureRedirect(form.Hidden_Practice_Id.Split(new char[] { '=' })[0])?.PracticeId + trailText;
                }
                _logger.LogInformation("QuickContact form object created");

                if (form != null) 
                {
                    db.sf_quickcontactpmform.Add(form);
                    await db.SaveChangesAsync();
                    _logger.LogInformation("Successfully saved QuickContact form. EntryId: {EntryId}, PracticeId: {PracticeId}", quickContactForm.OriginalEvent.EntryId, form.Hidden_Practice_Id);

                    return true;
                }
                else
                {
                    _logger.LogInformation($"Did not save this QuickContact form, the form is null ");

                    return false;
                }
            }catch(Exception e)
            {
                _logger.LogError(e, "Error processing QuickContact form. EntryId: {EntryId}, ErrorMessage: {ErrorMessage}", quickContactForm.OriginalEvent.EntryId, e.Message);
                return false;
            }
           
        }

        public async Task<bool> InsertFeedBackForm(SitefinityForm feedbackForm , string rawJson)
        {
            try
            {
                //_logger.LogInformation($"Begin SFFeedback form Insert of Entry ID {feedbackForm.OriginalEvent.EntryId}");
                if (_configuration.GetSection("AppSettings:RawJsonLogging").Value == "yes")
                    _logger.LogInformation($"The sf_feedback JSON is: {rawJson}");
                #pragma warning disable CS8629 // Nullable value type may be null.

                int feedbackCategory = feedbackForm.OriginalEvent.Controls.Where(x => x.FieldName == "FormMultipleChoice_C011").FirstOrDefault()?.Value == "Compliment" ? 10 : 11;
                #pragma warning disable CS8601 // Possible null reference assignment.

                int practiceId = string.IsNullOrEmpty(feedbackForm.OriginalEvent.Controls.Where(x => x.FieldName == "PracticeName").FirstOrDefault()?.Value) == true ? 0
                                        : string.IsNullOrWhiteSpace(feedbackForm.OriginalEvent.Controls.Where(x => x.FieldName == "PracticeName").FirstOrDefault()?.Value) == true ? 0 
                                        : int.Parse(feedbackForm.OriginalEvent.Controls.Where(x => x.FieldName == "PracticeName").FirstOrDefault()?.Value.Split(new char[] { '=' })[0]);

				#pragma warning restore CS8601 // Possible null reference assignment.
#pragma warning restore CS8629 // Nullable value type may be null.
                
				CustomerFeedbackViewModel viewModel = new CustomerFeedbackViewModel
                {
                    FeedbackTitle = feedbackForm.OriginalEvent.Controls.Where(x => x.FieldName == "FormTextBox").FirstOrDefault()?.Value,
                    FeedbackType = feedbackForm.OriginalEvent.Controls.Where(x => x.FieldName == "FormMultipleChoice_C011").FirstOrDefault()?.Value,
                    FeedbackInformation = feedbackForm.OriginalEvent.Controls.Where(x => x.FieldName == "FormParagraphTextBox_C004").FirstOrDefault()?.Value,
                    PatientName = feedbackForm.OriginalEvent.Controls.Where(x => x.FieldName == "FormTextBox_C007").FirstOrDefault()?.Value + " " + feedbackForm.OriginalEvent.Controls.Where(x => x.FieldName == "FormTextBox_C007_0").FirstOrDefault()?.Value,
                    PhoneNumber = feedbackForm.OriginalEvent.Controls.Where(x => x.FieldName == "FormTextBox_C014").FirstOrDefault()?.Value,
                    Email = feedbackForm.OriginalEvent.Controls.Where(x => x.FieldName == "FormTextBox_C008").FirstOrDefault()?.Value,
                    DateSubmitted = DateTime.UtcNow,
                    PracticeName = feedbackForm.OriginalEvent.Controls.Where(x => x.FieldName == "PracticeName").FirstOrDefault()?.Value,
					PmDisplayName = GetPMDisplayName(practiceId.ToString())
				};

                string practiceName = string.Empty;
                string[]? practiceParts = feedbackForm.OriginalEvent.Controls.Where(x => x.FieldName == "PracticeName").FirstOrDefault()?.Value?.Split(new[] { "=>" }, StringSplitOptions.None);
                if (practiceParts != null && practiceParts.Length > 0)
                {
                    practiceName = practiceParts[1];
                }

                // Send email to patient support
                EmailDetail detail = new EmailDetail()
                {
                    EmailAddress = "<EMAIL>",
                    Subject = viewModel.FeedbackType == "Complaint" ? "@@@COMPLAINT" : "@@@COMPLIMENT",
                    ViewModel = viewModel,
                    TemplateLocation = viewModel.FeedbackType == "Complaint" ? "Emails/Complaint" : "Emails/Compliment"
                };

                if (viewModel.FeedbackType == null)
                {
                    detail.Subject = "@@@COMPLAINT";
                    detail.TemplateLocation = "Emails/Complaint";
				} 

				await _emailSender.SendEmail(detail);

				return true;

            }
            catch(Exception e)
            {
                _logger.LogInformation($"ERROR Found on SFFeedback Form  " + e.Message + $"on form json {rawJson}");
                return false;
            }
        }

        public string? GetPMEmail(string? pId)
        {
            _logger.LogInformation($"Fetching Email for pm at {pId}");
            int practiceId = int.Parse(pId);
            var email = db.practiceDetails.Where(x => x.PracticeID == practiceId).FirstOrDefault()?.PMEmail;
            _logger.LogInformation($"Returning email found as {email}");
            return email;
        }

		public string? GetPMDisplayName(string? pId)
		{
			_logger.LogInformation($"Fetching PM display name for pm at {pId}");
			int practiceId = int.Parse(pId);
            var practice = db.practiceDetails.Where(x => x.PracticeID == practiceId).FirstOrDefault();
            var fistname = practice?.PMFirstname ?? string.Empty;
            var surname = practice?.PMSurname ?? string.Empty;
			_logger.LogInformation($"Returning PM display name found as {fistname} {surname}");
			return $"{fistname} {surname}";
		}

		public bool ShouldEmailPM(string pId)
        {
            _logger.LogInformation($"Assessing whether to email PM at {pId}");

            int practiceId = int.Parse(pId);
            var widgetOption = dbgroupPracticeContext.WidgetOptions.Where(x => x.PracticeId == practiceId && x.Value == "True" && x.WidgetId == 1).FirstOrDefault();
            if(widgetOption != null)
            {
                _logger.LogInformation($"Widget Option at {pId} is true");
                return true;
            }
            else
            {
                _logger.LogInformation($"Widget Option at {pId} is null");
                return false;
            }
        }

        public string ProcessDates(string dateInput)
        {
            string year = dateInput.Substring(4, 4);
            string month = dateInput.Substring(2, 2);
            string day = dateInput.Substring(0, 2);
            string output = $"{day}/{month}/{year}";
            string outputReverse = $"{month}/{day}/{year}";

            DateTime date;
            bool success = DateTime.TryParse(output, out date);

            if(success) {
                return output;
            }
            else
            {
                return outputReverse;
            }

        }

        public async Task<bool> PassWebApplicationFormData(SitefinityFormV2 form, string rawJson)
        {
            if (_configuration.GetSection("AppSettings:RawJsonLogging").Value == "yes")
                _logger.LogInformation($" Logging only, not processed - The Application parent JSON is : {rawJson}");

            var jobJson= form.GetPropertyValueByTitle("PayloadJson").ToString();
            try
            {

                if (jobJson.ToString() != null)
                {
                    sf_WebJobApplication webapplicant = new()
                    {
                        CreatedDate = DateTime.Now,
                        CVUrl =  _vacancyService.GetCVUrl(form),
                        EntryId = form.OriginalEvent.EntryId,
                        ErrorDate = null,
                        ProcessedDate = null
                    };

                        _logger.LogInformation("Webjob Application saved. EntryId: {EntryId}, CVUrl: {CVUrl}", webapplicant.EntryId, webapplicant.CVUrl);
                        var applicantJson = jobJson.Replace("\\", "");
                        if (!string.IsNullOrEmpty(webapplicant.CVUrl))
                        {
                            applicantJson = "{" + "\"CVUrl\":" +  "\"" + webapplicant.CVUrl + "\"" + "," + applicantJson.Substring(1);
                        }
                        webapplicant.ApplicationJson = applicantJson;
                        var result = await _recruitmentHubService.SendWebPayload(applicantJson);
                        _logger.LogInformation("Webjob Application posted to RecruitmentHub. EntryId: {EntryId}, Result: {Result}", webapplicant.EntryId, result);
                        var resultDate = DateTime.Now;

                        if (result == true)
                        {
                            webapplicant.ProcessedDate = resultDate;
                            webapplicant.ErrorDate = null;
                        }
                        else
                        {
                            webapplicant.ErrorDate = resultDate;
                        }
                        db.webJobApplications.AddOrUpdate(webapplicant);
                        await db.SaveChangesAsync();
                        _logger.LogInformation($"(Webjob Application) Posted {webapplicant.EntryId} ");

                        return result;
                 }

                _logger.LogInformation($"(Webjob Application) There was no Job Json in the payload");

                return false;
            }catch (Exception ex)
            {
                _logger.LogInformation($"(Webjob Application) Exception {ex.Message} ");
                _logger.LogError("Error: " + ex);

                return false;
            }

        }

        public PracticeRedirectItem? AssessPracticeClosureRedirect(string practiceId)
        {
            var closedRedirectPractices = _configuration.GetSection("PracticeClosedRedirects");
            string? redirectPracticeId = closedRedirectPractices.GetChildren().Where(x => x.Key == practiceId).FirstOrDefault()?.Value?.Split(new char[] { '|' })[0];
            string? redirectPracticePostcode = closedRedirectPractices.GetChildren().Where(x => x.Key == practiceId).FirstOrDefault()?.Value?.Split(new char[] { '|' })[1];
            PracticeRedirectItem resultItem = new();
            if (string.IsNullOrEmpty(redirectPracticeId))
            {
                resultItem.PracticeId = practiceId;
            }
            else
            {
                resultItem.PracticeId = redirectPracticeId;
                resultItem.PracticePostcode = redirectPracticePostcode;
            }

            return resultItem;
        }

        public async Task<bool> ProcessGeneralInquiryForm(SitefinityForm form, string rawJson)
        {
            if (_configuration.GetSection("AppSettings:RawJsonLogging").Value == "yes")
                _logger.LogInformation($" Logging only, not processed - The General Enquiry form JSON is : {rawJson}");

            string jsonTreatments = form.GetPropertyValueByTitle("Interested in") == null ? form.GetPropertyValueByTitle("SelectedTreatment") : form.GetPropertyValueByTitle("Interested in");

            string treatmentsText = string.Join(",", HttpUtility.UrlDecode(jsonTreatments));
            //store data here

            sf_GeneralEnquiry generalEnquiry = new sf_GeneralEnquiry()
            {
               Id = Guid.NewGuid(),
               FirstName = form.GetPropertyValueByTitle("First name"),
               LastName = form.GetPropertyValueByTitle("Last name"),
               Email = form.GetPropertyValueByTitle("Email"),
               Postcode = form.GetPropertyValueByTitle("SourcePage") == "PracticePageEnquiry" ? null : form.GetPropertyValueByTitle("Postcode"),
               PhoneNumber = form.GetPropertyValueByTitle("Phone number"),
               //DOB = !form.GetPropertyValueByTitle("Date of birth (dd/mm/yyyy)").Contains("/") ? DateTime.Parse(ProcessDates(form.GetPropertyValueByTitle("Date of birth (dd/mm/yyyy)"))) :
               //         DateTime.Parse(form.GetPropertyValueByTitle("Date of birth (dd/mm/yyyy)")),
               DOB = ProcessDayMonthYearInput(form),
               GAClientId = form.GetPropertyValueByTitle("GAClientId"),
               Treatments = treatmentsText,
               ProcessedDate = null,
               ErrorDate = null,  
               RawJson = rawJson,
               CreatedDate = DateTime.Now,
               Source = form.GetPropertyValueByTitle("SourcePage"),
               PracticePostCode = form.GetPropertyValueByTitle("SelectedPostcode"),
               PracticeId = (form.GetPropertyValueByTitle("SourcePage") == "PracticePageEnquiry" || form.GetPropertyValueByTitle("SourcePage") == "PracticePageLeafletEnquiry") ? Int64.Parse(form.GetPropertyValueByTitle("SelectedPractice") ) : null
               //CustomDayMonthYear = DateTime.ParseExact(form.GetPropertyValueByTitle("CustomDayMonthYear"), "dd/MM/yyyy", null),

            };

            if (!string.IsNullOrEmpty(generalEnquiry.PracticeId?.ToString()))
            {
                PracticeRedirectItem assessedPracticeItem = AssessPracticeClosureRedirect(generalEnquiry.PracticeId?.ToString());
                generalEnquiry.PracticeId = Int64.Parse(assessedPracticeItem?.PracticeId);
                generalEnquiry.PracticePostCode = assessedPracticeItem.PracticePostcode == null ? generalEnquiry.PracticePostCode : assessedPracticeItem.PracticePostcode;
            }

            db.sf_generalEnquiry.Add(generalEnquiry);
            await db.SaveChangesAsync();
            //
            _logger.LogInformation($"(General Enquiry) Form has been saved to DB successfully");

            return true;
        }



        public async Task<bool> ProcessEmergencyTriage(SitefinityForm form, string rawJson)
        {
            if (_configuration.GetSection("AppSettings:RawJsonLogging").Value == "yes")
                _logger.LogInformation($" Logging only, not processed - The Emergency Triage form JSON is : {rawJson}");
            
            //store data here
            string EmergencyPracticeId = form.GetPropertyValueByTitle("PracticeId");
            sf_Emergencydentalappointment emergencydentalappointment = new sf_Emergencydentalappointment()
            {
                Firstname = form.GetPropertyValueByTitle("First name"),
                Lastname = form.GetPropertyValueByTitle("Last name"),
                DateOfBirth = DateTime.TryParse(form.GetPropertyValueByTitle("Date of birth"), out var tempDate) ? tempDate : default(DateTime),
                ContactNumber = form.GetPropertyValueByTitle("Contact number"),                  
                AreYouExperiencingDentalPain = (string.IsNullOrEmpty(form.GetPropertyValueByTitle("Are you experiencing dental pain?")) || form.GetPropertyValueByTitle("Are you experiencing dental pain?") == "No") ? false : true,
                DoYouHaveABrokenTooth = (string.IsNullOrEmpty(form.GetPropertyValueByTitle("Do you have a broken tooth?")) || form.GetPropertyValueByTitle("Do you have a broken tooth?") == "No") ? false : true,
                DoYouRequireInterpreter = (string.IsNullOrEmpty(form.GetPropertyValueByTitle("Do you require an interpreter for your appointment?")) || form.GetPropertyValueByTitle("Do you require an interpreter for your appointment?") == "No") ? false : true,
                InterpreterLanguage = form.GetPropertyValueByTitle("Please specify language"),
                RequiresGroundFloorAccess = (string.IsNullOrEmpty(form.GetPropertyValueByTitle("Do you have any mobility issues which requires you to be seen in a ground floor or accessible surgery?")) || form.GetPropertyValueByTitle("Do you have any mobility issues which requires you to be seen in a ground floor or accessible surgery?") == "No") ? false : true,
                PersonReceivingBenefit = (string.IsNullOrEmpty(form.GetPropertyValueByTitle("Person receiving benefit")) || form.GetPropertyValueByTitle("Person receiving benefit") == "No") ? false : true,
                NHS_MaternityExemptionCertificate = form.GetPropertyValueByTitle("NHS Maternity exemption certificate / card no."),
                EntitledToFreeNHSDentalServicesOnFirstDay =  (string.IsNullOrEmpty(form.GetPropertyValueByTitle("I am entitled to free NHS dental services because on the first day of treatment:")) || form.GetPropertyValueByTitle("I am entitled to free NHS dental services because on the first day of treatment:") == "No") ? false : true,
                EntitledToFreeNHSDentalServicesDuringTreatment = (string.IsNullOrEmpty(form.GetPropertyValueByTitle("I am entitled to free NHS dental services because during the course of treatment I get, or am included in an award (as a claimant, partner, or dependent person under 20) of:")) || form.GetPropertyValueByTitle("I am entitled to free NHS dental services because during the course of treatment I get, or am included in an award (as a claimant, partner, or dependent person under 20) of:") == "No") ? false : true,
                NameOfCollegeOrUniversity = form.GetPropertyValueByTitle("Enter name of college or university"),
                NationalInsuranceNumber = form.GetPropertyValueByTitle("Enter National Insurance Number"),
                DateOfBirthForNHSService = DateTime.TryParse(form.GetPropertyValueByTitle("Date of birth"), out var tempDOBDate) ? tempDate : default(DateTime),
                AreYouEntitledToFreeNHSDentalServices = (string.IsNullOrEmpty(form.GetPropertyValueByTitle("Are you entitled to free NHS dental services?")) || form.GetPropertyValueByTitle("Are you entitled to free NHS dental services?") == "No") ? false : true,
                DoYouHaveALostFilling = (string.IsNullOrEmpty(form.GetPropertyValueByTitle("Do you have a lost filling?")) || form.GetPropertyValueByTitle("Do you have a lost filling?") == "No") ? false : true,
                DateBabyDueOrBorn = DateTime.TryParse(form.GetPropertyValueByTitle("Date baby due/born"), out var tempDBBDate) ? tempDate : default(DateTime),
                CreatedDate = DateTime.Now,                
                PracticeId = EmergencyPracticeId           

            };
                  
            db.sf_Emergencydentalappointment.Add(emergencydentalappointment);

            await db.SaveChangesAsync();
            

            if (EmergencyPracticeId != null)
            {
                string pmEmail = _practiceServices.GetPracticeEmailAsync(EmergencyPracticeId, "Rec").Result?.Email;
                if (pmEmail != null)
                {
                    EmailDetail detail = new EmailDetail()
                    {
                        //EmailAddress ="<EMAIL>",
                        EmailAddress = pmEmail,
                        Subject = "mydentist Emergency Request",
                        ViewModel = emergencydentalappointment,
                        TemplateLocation = "Emails/EmergencyAppointment"
                    };

                    await _emailSender.SendEmail(detail);
                }
               
            }            

            _logger.LogInformation($"(Emergency Enquiry) Form has been saved to DB successfully");

            return true;
        }

        // Process New CustomDayMonthYear Input Fields
        public DateTime ProcessDayMonthYearInput(SitefinityForm form)
        {
            string day = form.GetPropertyValueByTitle("Day");
            string month = form.GetPropertyValueByTitle("Month");
            string year = form.GetPropertyValueByTitle("Year");

            // Ensure all fields are populated
            if (string.IsNullOrWhiteSpace(day) || string.IsNullOrWhiteSpace(month) || string.IsNullOrWhiteSpace(year))
            {
                throw new ArgumentException("Date of birth fields (Day, Month, Year) cannot be null or empty.");
            }

            // Try to parse the extracted values into a DateTime
            try
            {
                int dayInt = int.Parse(day);
                int monthInt = int.Parse(month);
                int yearInt = int.Parse(year);

                return new DateTime(yearInt, monthInt, dayInt);
            }
            catch (FormatException ex)
            {
                // Handle the case where the date format is invalid
                _logger.LogError($"Error parsing date from day: {day}, month: {month}, year: {year}. Exception: {ex.Message}");
                throw new ArgumentException("Invalid date format. Please provide valid day, month, and year.");
            }
            catch (ArgumentOutOfRangeException ex)
            {
                // Handle invalid dates (like February 30)
                _logger.LogError($"Invalid date values: day: {day}, month: {month}, year: {year}. Exception: {ex.Message}");
                throw new ArgumentException("Invalid date values provided. Please check the day, month, and year.");
            }
        }

        public async Task<bool> ProcessHomePageMarketingMVCForm(SitefinityForm form, string rawJson)
        {
            if (_configuration.GetSection("AppSettings:RawJsonLogging").Value == "yes")
                _logger.LogInformation($"Logging only, not processed - The consent form JSON is : {rawJson}");

            string treatmentsJson = form.GetPropertyValueByTitle("Click the treatments below to receive information on our latest dental advice and promotions");

            HomePageMarketingConsentForm homePageMarketingConsentForm = new HomePageMarketingConsentForm()
            {
                EntryId = form.OriginalEvent.EntryId,
                ChosenMarketingPromos = treatmentsJson,
                RawJson = rawJson,
                CreatedDate = DateTime.Now,
                Email = form.GetPropertyValueByTitle("Email address"),
                FirstName = form.GetPropertyValueByTitle("First name"),
                LastName = form.GetPropertyValueByTitle("Last name"),
                PhoneNumber = form.GetPropertyValueByTitle("Phone number"),
                DOB = DateTime.TryParse(form.GetPropertyValueByTitle("Date of birth"), out var dob) ? dob : (DateTime?)null,
                Postcode = form.GetPropertyValueByTitle("Postcode"),
                PracticePostcode = null, // Initialize this to null for now
                PracticeId = null // Initialize this to null for now
            };

            // If PracticeId is null then find the nearest practice postcode
            if (homePageMarketingConsentForm.PracticeId == null)
            {
                var (practiceId, nearestPracticePostcode) = await _findNearestPracticeService.FindNearestPracticePostcode(form);
                if (!string.IsNullOrEmpty(nearestPracticePostcode))
                {
                    homePageMarketingConsentForm.PracticePostcode = nearestPracticePostcode;
                    homePageMarketingConsentForm.PracticeId = practiceId; // Assigning the practiceId from the response
                }
                else
                {
                    _logger.LogWarning("No nearest practice found based on the provided postcode.");
                    return false;
                }
            }

            try
            {
                _logger.LogInformation("Home Page Marketing Form Now Adding");
                db.HomePageMarketingConsentForms.Add(homePageMarketingConsentForm);
                await db.SaveChangesAsync();
                _logger.LogInformation("Home Page Marketing Form created successfully");
            }
            catch (Exception e)
            {
                _logger.LogError($"Home Page Marketing MVC Consent Form error - {e.Message}");
                return false;
            }

            return true;
        }

        public async Task<bool> ProcessWifiForm(SitefinityForm form, string rawJson)
        {
            if (_configuration.GetSection("AppSettings:RawJsonLogging").Value == "yes")
                _logger.LogInformation($"Logging only, not processed - The wifi form JSON is : {rawJson}");

            // Map WifiForm object with form input
            WifiForm wifiForm = new WifiForm()
            {
                EntryId = form.OriginalEvent.EntryId,
                AgreedTerms = form.GetPropertyValueByTitle("Terms and conditions") == "I have read and accept the terms and conditions",
                ChosenTreatments = form.GetPropertyValueByTitle("Would you like to receive more information about our products and services?"),
                FirstName = form.GetPropertyValueByTitle("First name"),
                LastName = form.GetPropertyValueByTitle("Last name"),
                PhoneNumber = form.GetPropertyValueByTitle("Phone number"),
                Email = form.GetPropertyValueByTitle("Email"),
                DOB = !form.GetPropertyValueByTitle("Date of birth (dd/mm/yyyy)").Contains("/")
                    ? DateTime.Parse(ProcessDates(form.GetPropertyValueByTitle("Date of birth (dd/mm/yyyy)")))
                    : DateTime.Parse(form.GetPropertyValueByTitle("Date of birth (dd/mm/yyyy)")),
                Postcode = form.GetPropertyValueByTitle("SourcePage") == "PracticePageEnquiry" ? null : form.GetPropertyValueByTitle("Postcode"),
                RawJson = rawJson,
                CreatedDate = DateTime.Now,
                PracticePostcode = null,
                PracticeId = null
            };

            // If PracticeId is null then find the nearest practice postcode
            if (wifiForm.PracticeId == null)
            {
                var (practiceId, nearestPracticePostcode) = await _findNearestPracticeService.FindNearestPracticePostcode(form);
                if (!string.IsNullOrEmpty(nearestPracticePostcode))
                {
                    wifiForm.PracticePostcode = nearestPracticePostcode;
                    wifiForm.PracticeId = practiceId; // Assigning the practiceId from the response
                }
                else
                {
                    _logger.LogWarning("No nearest practice found based on the provided postcode.");
                    return false;
                }
            }

            // Save the WifiForm to the database
            try
            {
                _logger.LogInformation("Wifi Form Now Adding");
                db.WifiForms.Add(wifiForm);
                await db.SaveChangesAsync();
                _logger.LogInformation("Wifi Form Created successfully");
            }
            catch (Exception e)
            {
                _logger.LogError($"Wifi Form error - {e.Message}");
                return false;
            }

            return true;
        }

        public async Task<bool> Process507ReferralForm(SitefinityForm form, string rawJson)
        {
            if (form == null)
            {
                _logger.LogError("Form is null.");
                return false;
            }

            if (_configuration.GetSection("AppSettings:RawJsonLogging").Value == "yes")
                _logger.LogInformation($"Logging only, not processed - The 507 referral form JSON is : {rawJson}");

            var attachmentString = form.GetPropertyValueByTitle("Relevant attachments e.g. radiographs");

            // Create a new ReferralForm507 instance
            ReferralForm507 referralForm = new ReferralForm507()
            {
                EntryId = form.OriginalEvent?.EntryId,
                RawJson = rawJson,
                CreatedDate = DateTime.Now,

                // Dentist Information
                ChosenTreatments = form.GetPropertyValueByTitle("Select a treatment"),
                ReferringDentistName = form.GetPropertyValueByTitle("Referring dentist"),
                DentistAddress = form.GetPropertyValueByTitle("Address"),
                DentistTelephoneNumber = form.GetPropertyValueByTitle("Telephone number"),
                DentistMobileNumber = form.GetPropertyValueByTitle("Mobile number"),
                DentistFax = form.GetPropertyValueByTitle("Fax"),
                DentistEmail = form.GetPropertyValueByTitle("Email"),

                // Patient Information
                PatientName = form.GetPropertyValueByTitle("Patient name"),
                PatientAddress = form.GetPropertyValueByTitle("Patient address"),
                PatientTelephoneNumber = form.GetPropertyValueByTitle("Patient telephone number"),
                PatientMobileNumber = form.GetPropertyValueByTitle("Patient mobile number"),
                PatientEmail = form.GetPropertyValueByTitle("Patient email"),

                // Handle PatientDOB parsing
                PatientDOB = DateTime.TryParse(form.GetPropertyValueByTitle("Patient date of birth"), out var dob) ? dob : (DateTime?)null,

                PreviouslySeen = form.GetPropertyValueByTitle("Have we seen this patient before?") == "Yes",
                InformedOfCosts = form.GetPropertyValueByTitle("Has the patient been informed of the likely costs?") == "Yes",
                PreferredContactMethod = Enum.TryParse(form.GetPropertyValueByTitle("Patient prefers to contact by?"), out PreferredContactMethod contactMethod) ? (PreferredContactMethod?)contactMethod : null,

                // Treatment Information
                TreatmentInfoToothNumber = form.GetPropertyValueByTitle("Tooth numbers"),
                ReasonForReferral = form.GetPropertyValueByTitle("Reason for referral"),
                PostAndCoreRequired = form.GetPropertyValueByTitle("Do you wish us to do the post and core if one is required?") == "Yes",
                Pain = form.GetPropertyValueByTitle("Pain?") == "Yes",
                HowSevereIsThePain = form.GetPropertyValueByTitle("If yes, how severe is the pain?"),
                Swelling = form.GetPropertyValueByTitle("Swelling?") == "Yes",
                ToothRootFilled = form.GetPropertyValueByTitle("Has the tooth been root filled before?") == "Yes",
                ConsultationOnly = form.GetPropertyValueByTitle("Consultation only?") == "Yes",
                Treatment = form.GetPropertyValueByTitle("Treatment?") == "Yes",

                // Updated PatientSymptoms handling
                PatientSymptoms = ParsePatientSymptoms(form.GetPropertyValueByTitle("Please indicate the patients symptoms by ticking the appropriate boxes")),
                OtherSymptoms = form.GetPropertyValueByTitle("Other symptoms"),
                TreatmentUnderSedation = form.GetPropertyValueByTitle("Treatment under sedation?") == "Yes",
                RelevantMedicalHistory = form.GetPropertyValueByTitle("Any relevant medical history?"),
                RelevantAttachments = attachmentString?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries).Select(a => a.Trim()).ToList(),
            };

            // Log referralForm data for debugging
            _logger.LogInformation($"Referral Form Data: {JsonSerializer.Serialize(referralForm)}");

            // Validate the dates
            if (referralForm.CreatedDate < new DateTime(1753, 1, 1))
            {
                _logger.LogError($"Invalid CreatedDate: {referralForm.CreatedDate}");
                return false;
            }

            if (referralForm.PatientDOB.HasValue && referralForm.PatientDOB < new DateTime(1753, 1, 1))
            {
                _logger.LogError($"Invalid PatientDOB: {referralForm.PatientDOB}");
                return false;
            }

            // Save the ReferralForm507 to the database
            try
            {
                _logger.LogInformation("507 Referral Form Now Adding");
                db.ReferralForms507.Add(referralForm);
                await db.SaveChangesAsync();
                _logger.LogInformation("507 Referral Form Created successfully");
            }
            catch (DbUpdateException dbEx)
            {
                var innerException = dbEx.InnerException?.InnerException;
                _logger.LogError($"DbUpdateException: {innerException?.Message}");
                _logger.LogError($"Referral Form Data: {JsonSerializer.Serialize(referralForm)}");
                return false;
            }
            catch (Exception e)
            {
                _logger.LogError($"General exception: {e.Message}");
                return false;
            }

            return true;
        }

        private List<PatientSymptoms>? ParsePatientSymptoms(string symptomsInput)
        {
            if (string.IsNullOrWhiteSpace(symptomsInput))
                return null;

            var symptoms = symptomsInput.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                                        .Select(symptom => symptom.Trim())
                                        .Where(symptom => Enum.TryParse(symptom, out PatientSymptoms _))
                                        .Select(symptom => (PatientSymptoms)Enum.Parse(typeof(PatientSymptoms), symptom))
                                        .ToList();

            // Log the parsed symptoms for debugging
            _logger.LogInformation($"Parsed Patient Symptoms: {JsonSerializer.Serialize(symptoms)}");

            return symptoms.Count > 0 ? symptoms : null;
        }

        public async Task<bool> ProcessMarketingConsentForm(SitefinityForm form, string rawJson)
            {
            if (_configuration.GetSection("AppSettings:RawJsonLogging").Value == "yes")
                _logger.LogInformation($" Logging only, not processed - The consent form JSON is : {rawJson}");

                _logger.LogInformation($"Marketing Consent Form now adding");

                MarketingConsentForm marketingconsentForm = new MarketingConsentForm()
                {
                    EntryId = form.OriginalEvent.EntryId,
                    ChosenMarketingPromos = form.GetPropertyValueByTitle("Sign up to receive information on our latest dental advice and promotions."),
                    RawJson = rawJson,
                    CreatedDate = DateTime.Now
                };
                db.MarketingConsentForms.Add(marketingconsentForm);

            try
            {
                _logger.LogInformation($"Marketing Consent Form now adding");
                await db.SaveChangesAsync();
                _logger.LogInformation($"Marketing Consent Form added successfully");
            }
            catch (Exception e)
            {
                _logger.LogInformation($"Marketing Consent Form error - {e.Message}");
                return false;
            }

            return true;
        }

        public async Task<bool> EmailChosenRecipient(SitefinityForm form, string formName, string rawJson)
        {
            if (_configuration.GetSection("AppSettings:RawJsonLogging").Value == "yes")
                _logger.LogInformation($" Logging only, not processed - The {formName} JSON is : {rawJson}");

            return true;
        }

        public async Task<bool> InsertPatientSupportFeedBackForm(SitefinityForm patientSupprtfeedbackForm, string rawJson)
        {
            _logger.LogInformation($"Begin PatientSupport Feedback insertion EntryId {patientSupprtfeedbackForm.OriginalEvent.EntryId}");
            if (_configuration.GetSection("AppSettings:RawJsonLogging").Value == "yes")
                _logger.LogInformation($"The sf_feedbackpracticepages JSON is: {rawJson}");

            bool isExclusive = false;
            int feedbackCategory;
            int numberOfExistingFeedback;
            string? practiceId = string.Empty;
            string? practiceName = string.Empty;
            string formTitle = patientSupprtfeedbackForm.OriginalEvent.FormTitle;
            string exclusiveFeedbackFormTitle = "Feedback - Practice pages";

            isExclusive = formTitle == exclusiveFeedbackFormTitle;
            
            if (patientSupprtfeedbackForm.OriginalEvent.Controls.Where(x => x.FieldName == "HiddenPracticeId").Count() != 1)
            {
                // Practice name field comes in the form of a delimited practice ID and name
                if (patientSupprtfeedbackForm.OriginalEvent.Controls.Where(x => x.FieldName == "PracticeName").Count() == 1)
                {
                    practiceId = patientSupprtfeedbackForm.OriginalEvent.Controls.Where(x => x.FieldName == "PracticeName").FirstOrDefault()?.Value?.Split(new[] { "=>" }, StringSplitOptions.None)[0];
                }
            }
            else
            {
                practiceId = patientSupprtfeedbackForm.OriginalEvent.Controls.Where(x => x.FieldName == "HiddenPracticeId").FirstOrDefault()?.Value?.Split(new[] { "=>" }, StringSplitOptions.None)[0];
            }
            if(string.IsNullOrEmpty(practiceId))
            {
                practiceId = "0";
            }

            if (isExclusive)
            {
                _logger.LogInformation($"PatientSupport feedback formEntryId {patientSupprtfeedbackForm.OriginalEvent.EntryId} is Exclusive");
                practiceName = patientSupprtfeedbackForm.OriginalEvent.Controls.Where(x => x.FieldName == "PracticePagePracticeName_0").FirstOrDefault()?.Value;
            }
            else
            {
                _logger.LogInformation($"PatientSupport feedback formEntryId {patientSupprtfeedbackForm.OriginalEvent.EntryId} is NOT Exclusive");

                string[]? practiceParts = patientSupprtfeedbackForm.OriginalEvent.Controls.Where(x => x.FieldName == "PracticeName").FirstOrDefault()?.Value?.Split(new[] { "=>" }, StringSplitOptions.None);
                if (practiceParts != null && practiceParts.Length > 0)
                {
                    practiceName = practiceParts[1];
                }

            }

            CustomerFeedbackViewModel viewModel = new CustomerFeedbackViewModel
            {
                FeedbackTitle = patientSupprtfeedbackForm.OriginalEvent.Controls.Where(x => x.FieldName == "FormTextBox").FirstOrDefault()?.Value,
                FeedbackType = patientSupprtfeedbackForm.OriginalEvent.Controls.Where(x => x.Title == "Compliments / complaints form").FirstOrDefault()?.Type.ToString(),
                FeedbackInformation = patientSupprtfeedbackForm.OriginalEvent.Controls.Where(x => x.FieldName == "FormParagraphTextBox_C004").FirstOrDefault()?.Value,
                PatientName = patientSupprtfeedbackForm.OriginalEvent.Controls.Where(x => x.FieldName == "FormTextBox_C007").FirstOrDefault()?.Value + " " + patientSupprtfeedbackForm.OriginalEvent.Controls.Where(x => x.FieldName == "FormTextBox_C007_0").FirstOrDefault()?.Value,
                PhoneNumber = patientSupprtfeedbackForm.OriginalEvent.Controls.Where(x => x.FieldName == "FormTextBox_C014").FirstOrDefault()?.Value,
                Email = patientSupprtfeedbackForm.OriginalEvent.Controls.Where(x => x.FieldName == "FormTextBox_C008").FirstOrDefault()?.Value,
                DateSubmitted = DateTime.UtcNow,
				PmDisplayName = GetPMEmail(practiceId.ToString())
			};

            if (viewModel.FeedbackType == "Compliment")
            {
                feedbackCategory = 10;
            }
            else
            {
                feedbackCategory = 11;
            }
            _logger.LogInformation($"PatientSupport feedback feedbackCategory is {feedbackCategory} and feedbackType is {viewModel.FeedbackType}");
            string dbPatientName = viewModel.PatientName.Replace("'", "''");
            string newsql = $"select * from [Warehouse].PatientSupport.[Feedback] Where FeedbackType = 2 and PracticeId = {int.Parse(practiceId)} and Category = {feedbackCategory.ToString()} and SubCategory = 72 and Patient = '{dbPatientName}' and PatientType = 2  and Status = 1 and Deleted ='false'  and  datediff(minute, RaisedDate, '{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}') < 3 ";
            numberOfExistingFeedback = db.patientsupportfeedback.SqlQuery(newsql).Sum(x => x.PracticeId);
            _logger.LogInformation($"PatientSupport feedback formEntryId {patientSupprtfeedbackForm.OriginalEvent.EntryId} has existing Forms count {numberOfExistingFeedback}  ");

            if (numberOfExistingFeedback == 0)
            {
                try
                {
					// Send email to patient support
					EmailDetail detail = new EmailDetail()
					{
						EmailAddress = "<EMAIL>",
						Subject = viewModel.FeedbackType == "2" ? "@@@COMPLAINT" : "@@@COMPLIMENT",
						ViewModel = viewModel,
						TemplateLocation = viewModel.FeedbackType == "2" ? "Emails/Complaint" : "Emails/Compliment"
					};

					await _emailSender.SendEmail(detail);

					_logger.LogInformation($"PatientSupport feedback formEntryId {patientSupprtfeedbackForm.OriginalEvent.EntryId} transaction fully Complete");
                    return true;
                }
                catch (Exception e)
                {
                    _logger.LogInformation($"Error During PatientSupport feedback Insert formEntryId {e.Message}");
                    return false;
                }
            }
            else
            {
                _logger.LogInformation($"PatientSupport feedback formEntryId {patientSupprtfeedbackForm.OriginalEvent.EntryId} submitted too quickly after last entry, please wait 3 mins");
            }
            return false;
        }

        public async Task<int> InsertRecruitmentWebsiteForm(SitefinityForm recruitmentWebsiteForm, JsonElement json)
         {
            try
            {
                _logger.LogInformation($"Recruitment Website Form Recieved.");

                RecruitmentWebsiteFormRequest recruitmentWebsiteFormRequest = new RecruitmentWebsiteFormRequest()
                {
                    FirstName = recruitmentWebsiteForm.GetPropertyValueByTitle("First Name"),
                    LastName = recruitmentWebsiteForm.GetPropertyValueByTitle("Last Name"),
                    GdcNumber = recruitmentWebsiteForm.GetPropertyValueByTitle("Gdc Number"),
                    Email = recruitmentWebsiteForm.GetPropertyValueByTitle("Email"),
                    ContactNumber = recruitmentWebsiteForm.GetPropertyValueByTitle("Contact Number"),
                    UniversityUndergraduate = recruitmentWebsiteForm.GetPropertyValueByTitle(new string[] { "UK Dental School", "University", "UK Dental School (which you previously attended or currently attend)" }),
                    UniversityPostgraduate = recruitmentWebsiteForm.GetPropertyValueByTitle(new string[] { "UK Post Graduate Dental School (which you previously attend or currently attend)" }),
                    DesiredLocation = recruitmentWebsiteForm.GetLocationTextByTitle(new string[] { "Desired working location in the UK", "Their desired working location in the UK", "Location preferences", "Their desired working location in the UK", "Preferred working location(s)" }),
                    DesiredRole = recruitmentWebsiteForm.GetPropertyValueByTitle(new string[] { "Job role interested in?", "What type of role are they qualified for?", "I am a...", "Current role" , "Desired Role" }),
                    ReferrerContactNumber = recruitmentWebsiteForm.GetPropertyValueByTitle("Their Contact Number(s)"),
                    ReferrerEmailAddress = recruitmentWebsiteForm.GetPropertyValueByTitle("Their Email Address:"),
                    ReferrerGdcNumber = recruitmentWebsiteForm.GetPropertyValueByTitle("Their GDC Number"),
                    ReferrerName = recruitmentWebsiteForm.GetPropertyValueByTitle("Who are you referring? (Full name)"),
                    FormName = recruitmentWebsiteForm.GetPropertyValueByTitle("Form Name"),
                    PostCode = recruitmentWebsiteForm.GetPropertyValueByTitle(new string[] { "Post Code", "PostCode" }),
                    YearQualifiedUndergraduate = recruitmentWebsiteForm.GetPropertyValueByTitle(new string[] { "Grad Year", "Graduation / expected graduation year", "Year of graduation" }),
                    YearQualifiedPostgraduate = recruitmentWebsiteForm.GetPropertyValueByTitle(new string[] { "Post Graduation / expected graduation year", "Specialist year of graduation" }),
                    GAClientId = recruitmentWebsiteForm.GetPropertyValueByTitle("GAClientId"),
                    DesiredLocationLatitude = recruitmentWebsiteForm.GetLocationLatitudeByTitle(new string[] { "Desired working location in the UK", "Their desired working location in the UK", "Location preferences", "Their desired working location in the UK", "Preferred working location(s)" }),
                    DesiredLocationLongitude = recruitmentWebsiteForm.GetLocationLongitudeByTitle(new string[] { "Desired working location in the UK", "Their desired working location in the UK", "Location preferences", "Their desired working location in the UK", "Preferred working location(s)" }),
                    CountryOfQualification = recruitmentWebsiteForm.GetPropertyValueByTitle(new string[] { "Country of graduation", "Country of graduation *" }),
                    PostgraduateQualification = recruitmentWebsiteForm.GetPropertyValueByTitle("Specialist qualification"),
                    Notes = recruitmentWebsiteForm.GetPropertyValueByTitle("Which topic is of most interest to you?")
                };

                var recruitmentWebsiteFormDatabase = await _recruitmentWebsiteFormImplementationService.ProcessRequest(recruitmentWebsiteFormRequest);

                _logger.LogInformation($"Recruitment Website Form {recruitmentWebsiteFormDatabase.Id} Successfully Processed.");

                return recruitmentWebsiteFormDatabase.Id;
            }
            catch(Exception ex)
            {
                _logger.LogError($"Recruitment Website Errored.", ex);

                throw;
            }
        }

        public async Task<bool> ProcessReferralForm(SitefinityForm sitefinityForm, string jsonElement, ReferralFormType referralFormType)
        {
            if (_configuration.GetSection("AppSettings:RawJsonLogging").Value == "yes")
                _logger.LogInformation("Logging only, not processed - The referral form JSON is : {json}", jsonElement);

            var (isSuccess, referralData) = await SaveRefferalsToDb(sitefinityForm, referralFormType);
            if (!isSuccess)
            {
                await UpdateReferralErrorLog(referralData, $"could not save to db using object {sitefinityForm}");
                return false;
            }
            var practice = await _practiceManagerServices.FindNearestPracticeByPostCode(referralData.PatientPostcode);
            if (practice.PracticeId <= 0)
            {
                await UpdateReferralErrorLog(referralData, $"could not get nearest practice by postcode {referralData.PatientPostcode}");
                return false;
            }
            //todo ensure practice manager has this service below implemented in the future by ensuring the dbcontext is usable by the service
            //var practiceDetail = await _practiceManagerServices.GetPracticeDetailsById(practice.PracticeId);
            var practiceManagerEmail = GetPMEmail(practice.PracticeId.ToString());
            if (string.IsNullOrEmpty(practiceManagerEmail))
            {
                await UpdateReferralErrorLog(referralData, $"could not get practice by practice id {practice.PracticeId}");
                return false;
            }
            string mailContent;
            if (referralFormType == ReferralFormType.PatientReferralForm)
            {
                string emailTemplate = File.ReadAllText("Emails/PatientReferralForm.html");
                mailContent = _practiceManagerServices.ComposePracticeManagerMail(practiceManagerEmail, referralData, emailTemplate);
            }
            else
            {
                string emailTemplate = File.ReadAllText("Emails/ClinicianReferringForm.html");
                mailContent = _practiceManagerServices.ComposePracticeManagerMail(practiceManagerEmail, referralData, emailTemplate);
            }

            _emailSender.SendMail(new EmailDetail
            {
#if DEBUG
                EmailAddress = "<EMAIL>",
#else
                EmailAddress = practiceManagerEmail,

#endif
                Subject = "Referral",
                ViewModel = mailContent
            });
            return true;
        }

        private async Task UpdateReferralErrorLog(Referrals referralData, string message)
        {
            _logger.LogError(message);
            referralData.DateOfErrorMessage = DateTime.Now;
            referralData.ErrorMessage = message;
            db.Referrals.AddOrUpdate(referralData);
            await db.SaveChangesAsync();
        }

        private async Task<(bool isSuccess, Referrals referrals)> SaveRefferalsToDb(SitefinityForm sitefinityForm, ReferralFormType referralFormType)
        {
            Referrals referrals;

            if (referralFormType == ReferralFormType.ClinicianReferringForm)
            {
                var referralFormKeyValue = _configuration.GetSection("ReferringFormFieldKeyValue").Get<Referrals>();
                referrals = new()
                {
                    PatientTitle = sitefinityForm.GetPropertyValueByFieldName("DropdownListFieldController"),
                    PatientFirstName = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.PatientFirstName),
                    PatientLastName = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.PatientLastName),
                    PatientAddressLine1 = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.PatientAddressLine1),
                    PatientAddressLine2 = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.PatientAddressLine2),
                    PatientContactNumber = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.PatientContactNumber),
                    PatientCounty = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.PatientNhsNumber),
                    PatientPostcode = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.PatientNhsNumber),
                    PatientTown = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.PatientNhsNumber),
                    PatientNhsNumber = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.PatientNhsNumber),
                    PatientEmail = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.PatientNhsNumber),
                    PatientInterest = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.PatientInterest),
                    PatientDateOfBirth = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.PatientDateOfBirth),

                    ClinicianFirstName = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.ClinicianFirstName),
                    ClinicianLastName = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.ClinicianLastName),
                    ClinicianAddressLine1 = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.ClinicianAddressLine1),
                    ClinicianAddressLine2 = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.ClinicianAddressLine2),
                    ClinicianTown = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.ClinicianTown),
                    ClinicianPostcode = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.ClinicianPostcode),
                    ClinicianCounty = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.ClinicianCounty),
                    ClinicianGdcNumber = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.ClinicianGdcNumber),
                    IsNhsOrPrivate = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.IsNhsOrPrivate),
                    IsClinicianReferralUrgent = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.IsClinicianReferralUrgent),
                    ClinicianReasonForReferral = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.ClinicianReasonForReferral),
                    WasDptRadioTakenLastYear = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.WasDptRadioTakenLastYear),
                    ClinicianToSee = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.ClinicianToSee),
                    PracticeName = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.PracticeName),
                    ReferralFormType = ReferralFormType.ClinicianReferringForm.ToString(),
                };
            }
            else if (referralFormType == ReferralFormType.PatientReferralForm)
            {
                var referralFormKeyValue = _configuration.GetSection("ReferringFormFieldKeyValue").Get<Referrals>();
                referrals = new()
                {
                    PatientTitle = sitefinityForm.GetPropertyValueByFieldName("DropdownListFieldController"),
                    PatientFirstName = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.PatientFirstName),
                    PatientLastName = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.PatientLastName),
                    PatientAddressLine1 = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.PatientAddressLine1),
                    PatientAddressLine2 = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.PatientAddressLine2),
                    PatientContactNumber = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.PatientContactNumber),
                    PatientCounty = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.PatientCounty),
                    PatientPostcode = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.PatientPostcode),
                    PatientTown = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.PatientTown),
                    PatientNhsNumber = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.PatientNhsNumber),
                    PatientEmail = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.PatientEmail),
                    PatientInterest = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.PatientInterest),
                    PatientDateOfBirth = sitefinityForm.GetPropertyValueByFieldName(referralFormKeyValue.PatientDateOfBirth),
                    ReferralFormType = ReferralFormType.PatientReferralForm.ToString()
                };
            }

            else
            {
                _logger.LogInformation("referral form type not known");
                return (false, null);
            }
            db.Referrals.Add(referrals);
            var response = await db.SaveChangesAsync();
            return (response > 0, referrals);
        }

        public enum ReferralFormType
        {
            PatientReferralForm,
            ClinicianReferringForm
        }
    }
}
