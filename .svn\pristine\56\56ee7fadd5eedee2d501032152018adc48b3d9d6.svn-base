﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MyDSitefinityAPI.Models.PracticeDirectory
{
    [Table("PracticePerson", Schema = "Directory")]
    public class PracticePerson
    {
        [Key]
        [Column("PracticePersonId")]
        public int PracticePersonId { get; set; }

        [Column("PracticeId")]
        public int PracticeId { get; set; }

        [Column("GdcNo")]
        public string GdcNumber { get; set; }

        [Column("RoleId")]
        public int RoleId { get; set; }

        [ForeignKey("GdcNumber")]
        public virtual PerformerProfile PerformerProfile { get; set; }

        [ForeignKey("RoleId")]
        public virtual Role Role { get; set; }

        [ForeignKey("PracticePersonId")]
        public virtual List<PracticePersonLink> PracticePersonLinks { get; set; }

        [ForeignKey("PracticeId")]
        public virtual PracticeDetails Practice { get; set; }
    }
}
