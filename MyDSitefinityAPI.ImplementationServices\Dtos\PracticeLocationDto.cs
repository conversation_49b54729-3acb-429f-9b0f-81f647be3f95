﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Mydentist.MyDSitefinityAPI.ImplementationServices.Dtos
{
    public class PracticeLocationDto
    {
        [JsonPropertyName("practiceId")]
        public int PracticeId { get; set; }
        [JsonPropertyName("practiceName")]

        public string PracticeName { get; set; } = default!;
        [JsonPropertyName("postcode")]

        public string Postcode { get; set; } = default!;
        [JsonPropertyName("practiceLat")]

        public float PracticeLat { get; set; }
        [JsonPropertyName("practiceLong")]

        public float PracticeLong { get; set; }
        [JsonPropertyName("distance")]

        public float Distance { get; set; }
    }

}
