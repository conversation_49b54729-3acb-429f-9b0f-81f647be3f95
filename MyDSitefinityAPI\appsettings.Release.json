{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"DbContextWarehouse": "Data Source=172.30.1.96; User ID=formSubmitUser; Password= Password3456!; Initial Catalog=Warehouse; Encrypt=False;", "DBContextWebsiteConfig": "Data Source=172.30.1.96; User ID=formSubmitUser; Password= Password3456!;Initial Catalog=WebsiteConfig; Encrypt=False;"}, "NearestPractice": {"BaseUrl": "https://api.mydentist.co.uk/PracticeLocation.API.V1", "PracticeLocationApiKey": "119pReciSION1661*eQs"}, "ClinicianPortalSettings": {"ApiKey": "1137cb2f-891b-46fd-a13c-e4f77e78e3b6", "Url": "https://api.mydentist.co.uk/clinicianportal.api.v3/api/"}, "RecruitmentHub": {"ApiKey": "6OuOtBGlq75VhLEch5dV1IJmm", "BaseUrl": "https://apinew.mydentist.co.uk/", "WebApply": "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1.staging/api/Application/PostWebApplication", "OAuth": {"ClientId": "mydentistimport", "ClientSecret": "gKA9ruxfgX", "Username": "DataUpdate", "Password": "wZpFf847FaODf5hpXul2", "TokenUrl": "https://www.mydentist.co.uk/sitefinity/oauth/token", "GrantType": "password"}}, "AppSettings": {"RawJsonLogging": "no", "MapLogging": "no", "EmailToList": "<EMAIL>", "VacancyApplyApi": "https://api.mydentist.co.uk/Vacancy.Api.V4/api/Applications/Apply", "VacancyApiKey": "df5da1d8-620c-4636-8c52-3c327ac43a11", "EmailTemplate": "CustomerFeedbackSubmitted.cshtml", "GeneralInquiryTemplate": "GeneralInquiryEmail.cshtml", "ReferralEmailTemplate": "PatientReferralSubmitted.cshtml", "PatientSupportFeedBackUser": "WXHO\\mmckessy", "APIKey": "ec32870c8968aebacab659d03d0098317857142729baeaa3c3da3e116dba685a", "ApplicationEmail": "<EMAIL>", "EmailToOverride": "", "MarketingTeamEmail": "<EMAIL>", "PracticeViewImageApprovalUrl": "http://myd-web/PracticeView/Images/Status", "WebsiteConfigBaseUrl": "https://api.mydentist.co.uk/webconfig.API.V4/api/", "WebsiteConfigApiKey": "CT-Hvk6OuOtBGlq75VhLEch5zfA5dV1IJmrA", "EmailEndpoint": "http://172.30.1.192/EmailInterfaceServiceV2/Service1.svc", "PracticeApi": "https://apinew.mydentist.co.uk/WebPractice.API.V1", "PracticeApiKey": "11233", "PracticeLocationTreatmentsApi": "https://api.mydentist.co.uk/PracticeLocation.API.V1/api/Practice/GetNearestPracticeWithTreatments", "PracticeLocationApiKey": "1191661"}}