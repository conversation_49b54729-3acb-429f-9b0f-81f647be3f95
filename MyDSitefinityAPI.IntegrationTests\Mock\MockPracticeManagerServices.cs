using Mydentist.MyDSitefinityAPI.Domain;
using Mydentist.MyDSitefinityAPI.ImplementationServices.Dtos;
using Mydentist.MyDSitefinityAPI.ImplementationServices.Interfaces;

namespace MyDSitefinityAPI.IntegrationTests.Mock
{
    public class MockPracticeManagerServices : IPracticeManagerServices
    {
        public string ComposePracticeManagerMail(string practiceManagerEmail, Referrals referral, string template)
        {
            return "Mock email content";
        }

        public Task<PracticeLocationDto> FindNearestPracticeByPostCode(string postCode)
        {
            return Task.FromResult(new PracticeLocationDto
            {
                PracticeId = 1,
                PracticeName = "Mock Practice",
                Postcode = "M1 1AA"
            });
        }

        public Task<PracticeDetailsDto> GetPracticeDetailsById(int id)
        {
            return Task.FromResult(new PracticeDetailsDto
            {
                PracticeID = id,
                PracticeName = "Mock Practice",
                PMEmail = "<EMAIL>",
                TCOEmail = "<EMAIL>",
                PMName = "Mock Practice Manager"
            });
        }
    }
}
