﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace MyDSitefinityAPI.Models
{
    [Table("sf_marketingconsentforms", Schema = "dbo")]

    public class MarketingConsentForm
    {
        [Key]
        [Column("Id")]
        public int Id { get; set; }
        public string EntryId { get; set; }
        public string? ChosenMarketingPromos { get; set; }
        public string? RawJson { get; set; }
        public DateTime CreatedDate { get; set; }
    }
}
