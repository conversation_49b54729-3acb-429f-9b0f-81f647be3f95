﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
  </configSections>
  <connectionStrings>
    <!--Iomart Live -->
    <add name="Warehouse" connectionString="Data Source=172.30.1.96; User ID=formSubmitUser; Password= Password3456!; Initial Catalog=Warehouse;" providerName="System.Data.SqlClient"/>
    <add name="ClinicianIntegrationMultiRole" connectionString="Data Source=172.30.1.96; User ID=formSubmitUser; Password= Password3456!; Initial Catalog=ClinicianIntegrationMultiRole;" providerName="System.Data.SqlClient"/>
    <add name="GroupWebsitePracticeData" connectionString="Data Source=172.30.1.96; User ID=formSubmitUser; Password= Password3456!; Initial Catalog=GroupWebsitePracticeData;" providerName="System.Data.SqlClient"/>
    <add name="Staging" connectionString="Data Source=172.30.1.119; User ID=formSubmitUser; Password= Password3456!;Initial Catalog=Staging;" providerName="System.Data.SqlClient"/>
  </connectionStrings>
  <appSettings>
    <add key="sitefinityUsername" value="DataUpdate"/>
    <add key="sitefinityPassword" value="wZpFf847FaODf5hpXul2"/>
    <add key="LogLocation" value="C:\\Logs\\FormSubmission.API\\1.0.0\\log.txt"/>
    <add key="VacancyApplyApi" value="https://api.mydentist.co.uk/Vacancy.Api.V4/api/Applications/Apply"/>
    <add key="EmailToList" value="<EMAIL>"/>
    <add key="VacancyApiKey" value="df5da1d8-620c-4636-8c52-3c327ac43a11"/>
    <add key="EmailTemplate" value="CustomerFeedbackSubmitted.cshtml"/>
    <add key="ReferralEmailTemplate" value="PatientReferralSubmitted.cshtml"/>
    <add key="PatientSupportFeedBackUser" value="WXHO\\mmckessy"/>
    <add key="APIKey" value="ec32870c8968aebacab659d03d0098317857142729baeaa3c3da3e116dba685a"/>
    <add key="RawJsonLogging" value="no"/>
    <add key="MapLogging" value="no"/>
    <add key="applicationEmail" value="<EMAIL>"/>
    <add key="EmailToOverride" value=""/>
    <add key="MarketingTeamEmail" value="<EMAIL>"/>
    <add key="PracticeViewImageApprovalUrl" value="http://myd-web/PracticeView/Images/Status"/>
	<add key="websiteConfigBaseUrl" value="https://api.mydentist.co.uk/webconfig.API.V4/api/"/>
	<add key="websiteConfigApiKey" value="CT-Hvk6OuOtBGlq75VhLEch5zfA5dV1IJmrA"/>
	<add key="EmailEndpoint" value="http://************/EmailInterfaceServiceV2/Service1.svc"/>
	<add key ="SitefinityCloudImportAddress" value="https://mydentist.co.uk/import/mydentistcloudImport.asmx"/>

  </appSettings>
  <entityFramework>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer"/>
    </providers>
  </entityFramework>
	<system.serviceModel>
		<bindings>
			<basicHttpBinding>
				<binding name="BasicHttpBinding_IService1" />
			</basicHttpBinding>
		</bindings>
		<client>
			<endpoint address="http://***********/EmailInterfaceServiceV2/Service1.svc"
					  binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IService1"
					  contract="EmailService.Service1Client" name="BasicHttpBinding_IService1" />
		</client>
	</system.serviceModel>
</configuration>