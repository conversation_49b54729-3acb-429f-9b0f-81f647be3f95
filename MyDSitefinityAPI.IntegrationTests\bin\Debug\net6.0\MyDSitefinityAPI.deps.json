{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"MyDSitefinityAPI/1.0.0": {"dependencies": {"EntityFramework": "6.4.4", "Microsoft.EntityFrameworkCore.Design": "7.0.3", "Mydentist.MyDSitefinityAPI.ImplementationServices": "1.0.0", "NLog.Web.AspNetCore": "6.0.2", "Newtonsoft.Json.Bson": "1.0.2", "Serilog.Extensions.Logging.File": "3.0.0", "SitefinityServiceCaller.Core": "1.0.0", "Swashbuckle.AspNetCore": "6.2.3", "Swashbuckle.AspNetCore.Filters": "7.0.5", "Swashbuckle.AspNetCore.Swagger": "6.4.0", "System.ServiceModel.Duplex": "4.10.3", "System.ServiceModel.Federation": "4.10.3", "System.ServiceModel.Http": "4.10.3", "System.ServiceModel.NetTcp": "4.10.3", "System.ServiceModel.Security": "4.10.3", "System.Text.Json": "7.0.3", "Mydentist.MyDSitefinityApi.ClinicianPortalApi.Reference": "*******", "Mydentist.MyDSitefinityAPI.Domain.Reference": "*******", "Mydentist.MyDSitefinityAPI.ImplementationServices.Reference": "*******", "Mydentist.MyDSitefinityAPI.Persistence.Reference": "*******", "Mydentist.MyDSitefinityAPI.WebConfigApi.Reference": "*******"}, "runtime": {"MyDSitefinityAPI.dll": {}}}, "AutoMapper/12.0.0": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.dll": {"assemblyVersion": "1*******", "fileVersion": "1*******"}}}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.0": {"dependencies": {"AutoMapper": "12.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.Extensions.Microsoft.DependencyInjection.dll": {"assemblyVersion": "1*******", "fileVersion": "1*******"}}}, "Azure.Core/1.25.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "7.0.0", "System.Text.Json": "7.0.3", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net5.0/Azure.Core.dll": {"assemblyVersion": "1.25.0.0", "fileVersion": "1.2500.22.33004"}}}, "Azure.Identity/1.7.0": {"dependencies": {"Azure.Core": "1.25.0", "Microsoft.Identity.Client": "4.47.2", "Microsoft.Identity.Client.Extensions.Msal": "2.19.3", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "7.0.0", "System.Text.Json": "7.0.3", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.7.0.0", "fileVersion": "1.700.22.46903"}}}, "EntityFramework/6.4.4": {"dependencies": {"Microsoft.CSharp": "4.7.0", "System.CodeDom": "4.7.0", "System.ComponentModel.Annotations": "4.7.0", "System.Configuration.ConfigurationManager": "7.0.0", "System.Data.SqlClient": "4.8.1"}, "runtime": {"lib/netstandard2.1/EntityFramework.SqlServer.dll": {"assemblyVersion": "*******", "fileVersion": "6.400.420.21404"}, "lib/netstandard2.1/EntityFramework.dll": {"assemblyVersion": "*******", "fileVersion": "6.400.420.21404"}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Data.SqlClient/5.1.0": {"dependencies": {"Azure.Identity": "1.7.0", "Microsoft.Data.SqlClient.SNI.runtime": "5.1.0", "Microsoft.Identity.Client": "4.47.2", "Microsoft.IdentityModel.JsonWebTokens": "6.24.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.24.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "7.0.0", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Runtime.Caching": "6.0.0", "System.Security.Cryptography.Cng": "5.0.0", "System.Security.Principal.Windows": "5.0.0", "System.Text.Encoding.CodePages": "6.0.0", "System.Text.Encodings.Web": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "*******"}, "runtimes/win/lib/net6.0/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.0": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*******"}}}, "Microsoft.EntityFrameworkCore/7.0.4": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "7.0.4", "Microsoft.EntityFrameworkCore.Analyzers": "7.0.4", "Microsoft.Extensions.Caching.Memory": "7.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.423.11504"}}}, "Microsoft.EntityFrameworkCore.Abstractions/7.0.4": {"runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.423.11504"}}}, "Microsoft.EntityFrameworkCore.Analyzers/7.0.4": {}, "Microsoft.EntityFrameworkCore.Design/7.0.3": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.EntityFrameworkCore.Relational": "7.0.4", "Microsoft.Extensions.DependencyModel": "7.0.0", "Mono.TextTemplating": "2.2.1"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "7.0.3.0", "fileVersion": "7.0.323.6302"}}}, "Microsoft.EntityFrameworkCore.Relational/7.0.4": {"dependencies": {"Microsoft.EntityFrameworkCore": "7.0.4", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.423.11504"}}}, "Microsoft.EntityFrameworkCore.SqlServer/7.0.4": {"dependencies": {"Microsoft.Data.SqlClient": "5.1.0", "Microsoft.EntityFrameworkCore.Relational": "7.0.4"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.423.11504"}}}, "Microsoft.Extensions.ApiDescription.Server/3.0.0": {}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Binder/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyModel/7.0.0": {"dependencies": {"System.Text.Encodings.Web": "7.0.0", "System.Text.Json": "7.0.3"}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Http/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.ObjectPool/5.0.10": {}, "Microsoft.Extensions.Options/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Primitives/8.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Identity.Client/4.47.2": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.24.0"}, "runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.47.2.0", "fileVersion": "4.47.2.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/2.19.3": {"dependencies": {"Microsoft.Identity.Client": "4.47.2", "System.Security.Cryptography.ProtectedData": "7.0.0"}, "runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "2.19.3.0", "fileVersion": "2.19.3.0"}}}, "Microsoft.IdentityModel.Abstractions/6.24.0": {"runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "6.24.0.0", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.24.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.24.0", "System.Text.Encoding": "4.3.0", "System.Text.Json": "7.0.3"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "6.24.0.0", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.Logging/6.24.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.24.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "6.24.0.0", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.Protocols/6.24.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "6.24.0", "Microsoft.IdentityModel.Tokens": "6.24.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "6.24.0.0", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.24.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "6.24.0", "System.IdentityModel.Tokens.Jwt": "6.24.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "6.24.0.0", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.Protocols.WsTrust/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Tokens.Saml": "6.8.0", "Microsoft.IdentityModel.Xml": "6.8.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.WsTrust.dll": {"assemblyVersion": "6.8.0.0", "fileVersion": "6.8.0.11018"}}}, "Microsoft.IdentityModel.Tokens/6.24.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Microsoft.IdentityModel.Logging": "6.24.0", "System.Security.Cryptography.Cng": "5.0.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "6.24.0.0", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.Tokens.Saml/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.24.0", "Microsoft.IdentityModel.Xml": "6.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.Saml.dll": {"assemblyVersion": "6.8.0.0", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Xml/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.24.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Xml.dll": {"assemblyVersion": "6.8.0.0", "fileVersion": "6.8.0.11012"}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.OpenApi/1.3.1": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "Microsoft.Win32.SystemEvents/7.0.0": {"runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Mono.TextTemplating/2.2.1": {"dependencies": {"System.CodeDom": "4.7.0"}, "runtime": {"lib/netstandard2.0/Mono.TextTemplating.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "Newtonsoft.Json.Bson/1.0.2": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.2.22727"}}}, "NLog/6.0.2": {"runtime": {"lib/netstandard2.1/NLog.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.2.4328"}}}, "NLog.Extensions.Logging/6.0.2": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "NLog": "6.0.2"}, "runtime": {"lib/net6.0/NLog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.2.729"}}}, "NLog.Web.AspNetCore/6.0.2": {"dependencies": {"NLog.Extensions.Logging": "6.0.2"}, "runtime": {"lib/net6.0/NLog.Web.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.2.1322"}}}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "Scrutor/3.3.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyModel": "7.0.0"}, "runtime": {"lib/netcoreapp3.1/Scrutor.dll": {"assemblyVersion": "3.0.2.0", "fileVersion": "3.0.2.0"}}}, "Serilog/2.10.0": {"runtime": {"lib/netstandard2.1/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "2.10.0.0"}}}, "Serilog.Extensions.Logging/3.1.0": {"dependencies": {"Microsoft.Extensions.Logging": "8.0.0", "Serilog": "2.10.0"}, "runtime": {"lib/netstandard2.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "3.1.0.0"}}}, "Serilog.Extensions.Logging.File/3.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "6.0.0", "Serilog": "2.10.0", "Serilog.Extensions.Logging": "3.1.0", "Serilog.Formatting.Compact": "1.1.0", "Serilog.Sinks.Async": "1.5.0", "Serilog.Sinks.RollingFile": "3.3.0"}, "runtime": {"lib/net6.0/Serilog.Extensions.Logging.File.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.0.0"}}}, "Serilog.Formatting.Compact/1.1.0": {"dependencies": {"Serilog": "2.10.0"}, "runtime": {"lib/netstandard2.0/Serilog.Formatting.Compact.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.1.0.0"}}}, "Serilog.Sinks.Async/1.5.0": {"dependencies": {"Serilog": "2.10.0"}, "runtime": {"lib/netstandard2.0/Serilog.Sinks.Async.dll": {"assemblyVersion": "1.5.0.0", "fileVersion": "1.5.0.0"}}}, "Serilog.Sinks.File/3.2.0": {"dependencies": {"Serilog": "2.10.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Timer": "4.0.1"}, "runtime": {"lib/netstandard1.3/Serilog.Sinks.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.RollingFile/3.3.0": {"dependencies": {"Serilog.Sinks.File": "3.2.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0"}, "runtime": {"lib/netstandard1.3/Serilog.Sinks.RollingFile.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore/6.2.3": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "3.0.0", "Swashbuckle.AspNetCore.Swagger": "6.4.0", "Swashbuckle.AspNetCore.SwaggerGen": "6.2.3", "Swashbuckle.AspNetCore.SwaggerUI": "6.2.3"}}, "Swashbuckle.AspNetCore.Filters/7.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.OpenApi": "1.3.1", "Scrutor": "3.3.0", "Swashbuckle.AspNetCore.Filters.Abstractions": "7.0.5", "Swashbuckle.AspNetCore.SwaggerGen": "6.2.3"}, "runtime": {"lib/net5.0/Swashbuckle.AspNetCore.Filters.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.Filters.Abstractions/7.0.5": {"runtime": {"lib/net5.0/Swashbuckle.AspNetCore.Filters.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.Swagger/6.4.0": {"dependencies": {"Microsoft.OpenApi": "1.3.1"}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.2.3": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.4.0"}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "6.2.3.0", "fileVersion": "6.2.3.0"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.2.3": {"runtime": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "6.2.3.0", "fileVersion": "6.2.3.0"}}}, "System.CodeDom/4.7.0": {"runtime": {"lib/netstandard2.0/System.CodeDom.dll": {"assemblyVersion": "4.0.3.0", "fileVersion": "4.700.19.56404"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.ComponentModel.Annotations/4.7.0": {}, "System.Configuration.ConfigurationManager/7.0.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "7.0.0", "System.Security.Permissions": "7.0.0"}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Data.SqlClient/4.8.1": {"dependencies": {"Microsoft.Win32.Registry": "4.7.0", "System.Security.Principal.Windows": "5.0.0", "runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "runtime": {"lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.6702"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.20.6702"}, "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.20.6702"}}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Drawing.Common/7.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "7.0.0"}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Formats.Asn1/6.0.0": {}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/6.24.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.24.0", "Microsoft.IdentityModel.Tokens": "6.24.0"}, "runtime": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "6.24.0.0", "fileVersion": "6.24.0.31013"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Memory/4.5.4": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "7.0.0", "System.Text.Json": "7.0.3"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.221.20802"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Private.ServiceModel/4.10.3": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "Microsoft.Extensions.ObjectPool": "5.0.10", "System.Numerics.Vectors": "4.5.0", "System.Reflection.DispatchProxy": "4.7.1", "System.Security.Cryptography.Xml": "6.0.1", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Private.ServiceModel.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}, "resources": {"lib/netstandard2.0/cs/System.Private.ServiceModel.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/System.Private.ServiceModel.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/System.Private.ServiceModel.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/System.Private.ServiceModel.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/System.Private.ServiceModel.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/System.Private.ServiceModel.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/System.Private.ServiceModel.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/System.Private.ServiceModel.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/System.Private.ServiceModel.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/System.Private.ServiceModel.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/System.Private.ServiceModel.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/System.Private.ServiceModel.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/System.Private.ServiceModel.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.DispatchProxy/4.7.1": {}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Caching/6.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "7.0.0"}, "runtime": {"lib/net6.0/System.Runtime.Caching.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Cryptography.Cng/5.0.0": {"dependencies": {"System.Formats.Asn1": "6.0.0"}}, "System.Security.Cryptography.Pkcs/6.0.1": {"dependencies": {"System.Formats.Asn1": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.522.21309"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.522.21309"}}}, "System.Security.Cryptography.ProtectedData/7.0.0": {"runtime": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Security.Cryptography.Xml/6.0.1": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Cryptography.Pkcs": "6.0.1"}, "runtime": {"lib/net6.0/System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.822.36306"}}}, "System.Security.Permissions/7.0.0": {"dependencies": {"System.Windows.Extensions": "7.0.0"}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.ServiceModel.Duplex/4.10.3": {"dependencies": {"System.Private.ServiceModel": "4.10.3", "System.ServiceModel.Primitives": "4.10.3"}, "runtime": {"lib/net6.0/System.ServiceModel.Duplex.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}}, "System.ServiceModel.Federation/4.10.3": {"dependencies": {"Microsoft.IdentityModel.Protocols.WsTrust": "6.8.0", "System.ServiceModel.Http": "4.10.3", "System.ServiceModel.Primitives": "4.10.3", "System.ServiceModel.Security": "4.10.3"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Federation.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}, "resources": {"lib/netstandard2.0/cs/System.ServiceModel.Federation.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/System.ServiceModel.Federation.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/System.ServiceModel.Federation.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/System.ServiceModel.Federation.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/System.ServiceModel.Federation.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/System.ServiceModel.Federation.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/System.ServiceModel.Federation.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/System.ServiceModel.Federation.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/System.ServiceModel.Federation.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/System.ServiceModel.Federation.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/System.ServiceModel.Federation.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/System.ServiceModel.Federation.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/System.ServiceModel.Federation.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.ServiceModel.Http/4.10.3": {"dependencies": {"System.Private.ServiceModel": "4.10.3", "System.ServiceModel.Primitives": "4.10.3"}, "runtime": {"lib/net6.0/System.ServiceModel.Http.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}}, "System.ServiceModel.NetTcp/4.10.3": {"dependencies": {"System.Private.ServiceModel": "4.10.3", "System.ServiceModel.Primitives": "4.10.3"}, "runtime": {"lib/net6.0/System.ServiceModel.NetTcp.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}}, "System.ServiceModel.Primitives/4.10.3": {"dependencies": {"System.Private.ServiceModel": "4.10.3"}, "runtime": {"lib/net6.0/System.ServiceModel.Primitives.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}, "lib/net6.0/System.ServiceModel.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.1000.323.51101"}}}, "System.ServiceModel.Security/4.10.3": {"dependencies": {"System.Private.ServiceModel": "4.10.3", "System.ServiceModel.Primitives": "4.10.3"}, "runtime": {"lib/net6.0/System.ServiceModel.Security.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/7.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Text.Json/7.0.3": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "7.0.0"}, "runtime": {"lib/net6.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.723.27404"}}}, "System.Text.RegularExpressions/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Threading.Timer/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Windows.Extensions/7.0.0": {"dependencies": {"System.Drawing.Common": "7.0.0"}, "runtime": {"lib/net6.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.5.4"}}, "System.Xml.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "Mydentist.MyDSitefinityApi.ClinicianPortalApi/1.0.0": {"dependencies": {"Microsoft.Extensions.Options": "8.0.0", "Newtonsoft.Json.Bson": "1.0.2"}, "runtime": {"Mydentist.MyDSitefinityApi.ClinicianPortalApi.dll": {"fileVersion": "0.0.0.0"}}}, "Mydentist.MyDSitefinityAPI.Domain/1.0.0": {"runtime": {"Mydentist.MyDSitefinityAPI.Domain.dll": {"fileVersion": "0.0.0.0"}}}, "Mydentist.MyDSitefinityAPI.ImplementationServices/1.0.0": {"dependencies": {"AutoMapper.Extensions.Microsoft.DependencyInjection": "12.0.0", "Microsoft.Extensions.Http": "8.0.0", "Mydentist.MyDSitefinityAPI.Domain": "1.0.0", "Mydentist.MyDSitefinityAPI.Persistence": "1.0.0", "Mydentist.MyDSitefinityAPI.WebConfigApi": "1.0.0", "Mydentist.MyDSitefinityApi.ClinicianPortalApi": "1.0.0"}, "runtime": {"Mydentist.MyDSitefinityAPI.ImplementationServices.dll": {"fileVersion": "0.0.0.0"}}}, "Mydentist.MyDSitefinityAPI.Persistence/1.0.0": {"dependencies": {"EntityFramework": "6.4.4", "Microsoft.Data.SqlClient": "5.1.0", "Microsoft.EntityFrameworkCore": "7.0.4", "Microsoft.EntityFrameworkCore.SqlServer": "7.0.4", "Mydentist.MyDSitefinityAPI.Domain": "1.0.0"}, "runtime": {"Mydentist.MyDSitefinityAPI.Persistence.dll": {"fileVersion": "0.0.0.0"}}}, "Mydentist.MyDSitefinityAPI.WebConfigApi/1.0.0": {"dependencies": {"Microsoft.Data.SqlClient": "5.1.0", "Microsoft.EntityFrameworkCore": "7.0.4", "Microsoft.EntityFrameworkCore.SqlServer": "7.0.4", "Mydentist.MyDSitefinityAPI.Domain": "1.0.0", "Newtonsoft.Json": "13.0.3", "System.Configuration.ConfigurationManager": "7.0.0"}, "runtime": {"Mydentist.MyDSitefinityAPI.WebConfigApi.dll": {"fileVersion": "0.0.0.0"}}}, "SitefinityServiceCaller.Core/1.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "7.0.0", "System.ServiceModel.Duplex": "4.10.3", "System.ServiceModel.Federation": "4.10.3", "System.ServiceModel.Http": "4.10.3", "System.ServiceModel.NetTcp": "4.10.3", "System.ServiceModel.Security": "4.10.3"}, "runtime": {"SitefinityServiceCaller.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Mydentist.MyDSitefinityApi.ClinicianPortalApi.Reference/*******": {"runtime": {"Mydentist.MyDSitefinityApi.ClinicianPortalApi.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Mydentist.MyDSitefinityAPI.Domain.Reference/*******": {"runtime": {"Mydentist.MyDSitefinityAPI.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Mydentist.MyDSitefinityAPI.ImplementationServices.Reference/*******": {"runtime": {"Mydentist.MyDSitefinityAPI.ImplementationServices.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Mydentist.MyDSitefinityAPI.Persistence.Reference/*******": {"runtime": {"Mydentist.MyDSitefinityAPI.Persistence.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Mydentist.MyDSitefinityAPI.WebConfigApi.Reference/*******": {"runtime": {"Mydentist.MyDSitefinityAPI.WebConfigApi.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"MyDSitefinityAPI/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AutoMapper/12.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0Rmg0zI5AFu1O/y//o9VGyhxKjhggWpk9mOA1tp0DEVx40c61bs+lnQv+0jUq8XbniF7FKgIVvI1perqiMtLrA==", "path": "automapper/12.0.0", "hashPath": "automapper.12.0.0.nupkg.sha512"}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-XCJ4E3oKrbRl1qY9Mr+7uyC0xZj1+bqQjmQRWTiTKiVuuXTny+7YFWHi20tPjwkMukLbicN6yGlDy5PZ4wyi1w==", "path": "automapper.extensions.microsoft.dependencyinjection/12.0.0", "hashPath": "automapper.extensions.microsoft.dependencyinjection.12.0.0.nupkg.sha512"}, "Azure.Core/1.25.0": {"type": "package", "serviceable": true, "sha512": "sha512-X8Dd4sAggS84KScWIjEbFAdt2U1KDolQopTPoHVubG2y3CM54f9l6asVrP5Uy384NWXjsspPYaJgz5xHc+KvTA==", "path": "azure.core/1.25.0", "hashPath": "azure.core.1.25.0.nupkg.sha512"}, "Azure.Identity/1.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-eHEiCO/8+MfNc9nH5dVew/+FvxdaGrkRL4OMNwIz0W79+wtJyEoeRlXJ3SrXhoy9XR58geBYKmzMR83VO7bcAw==", "path": "azure.identity/1.7.0", "hashPath": "azure.identity.1.7.0.nupkg.sha512"}, "EntityFramework/6.4.4": {"type": "package", "serviceable": true, "sha512": "sha512-yj1+/4tci7Panu3jKDHYizxwVm0Jvm7b7m057b5h4u8NUHGCR8WIWirBTw+8EptRffwftIWPBeIRGNKD1ewEMQ==", "path": "entityframework/6.4.4", "hashPath": "entityframework.6.4.4.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-W8<PERSON>QjkMScOMTtJbPwmPyj9c3zYSFGawDW3jwlBOOsnY+EzZFLgNQ/UMkK35JmkNOVPdCyPr2Tw7Vv9N+KA3ZQ==", "path": "microsoft.bcl.asyncinterfaces/5.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.5.0.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-wA+a+7niwImfbAsPo6cDiQaqbpDzVGRDuZtQpSBiO3JPOTPQkR8xNYKH+c16Nz9RE2AyjtyvQdGokOrhxORBKQ==", "path": "microsoft.data.sqlclient/5.1.0", "hashPath": "microsoft.data.sqlclient.5.1.0.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-jVsElisM5sfBzaaV9kdq2NXZLwIbytetnsOIlJ0cQGgQP4zFNBmkfHBnpwtmKrtBJBEV9+9PVQPVrcCVhDgcIg==", "path": "microsoft.data.sqlclient.sni.runtime/5.1.0", "hashPath": "microsoft.data.sqlclient.sni.runtime.5.1.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/7.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-eNcsY3rft5ERJJcen80Jyg57EScjWZmvhwmFLYXmEOTdVqHG+wQZiMOXnO1b5RH3u2qTQq+Tpci7KGfLAG5Gtg==", "path": "microsoft.entityframeworkcore/7.0.4", "hashPath": "microsoft.entityframeworkcore.7.0.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/7.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-6GbYvs4L5oFpYpMzwF05kdDgvX09UmMX7MpDtDlGI5ymijFwquwv+yvdijbtodOuu0yLUpc4n71x6eBdJ8v1xQ==", "path": "microsoft.entityframeworkcore.abstractions/7.0.4", "hashPath": "microsoft.entityframeworkcore.abstractions.7.0.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/7.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-YRD4bViuaEPEsaBIL52DzXGzLCt3jYoE3wztYEW1QZYDl89hQ+ca0nvBO2mnMHmCXpU/2wlErrUyDp4x5B/3mg==", "path": "microsoft.entityframeworkcore.analyzers/7.0.4", "hashPath": "microsoft.entityframeworkcore.analyzers.7.0.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/7.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-Nv0Y2Zh8d919qKq8Q1bvbWQbFeb4JQ7jCuakajSVtip5JIwt4hTIWetVIapJ2vOQDDZuAHCzkjimMOlHH5LVsQ==", "path": "microsoft.entityframeworkcore.design/7.0.3", "hashPath": "microsoft.entityframeworkcore.design.7.0.3.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/7.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-L41+VonK6L0IurFHopoe5yY+m3MD26OMocKLPPR/XKxnazzZUcGPz0IGJpVnwpZyKVPfEIAnD5vmm60meYr1NA==", "path": "microsoft.entityframeworkcore.relational/7.0.4", "hashPath": "microsoft.entityframeworkcore.relational.7.0.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.SqlServer/7.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-3yUw/jaMhM65pfRqJVfLY3cOSV0qqh4dnl+VXuCxgUYcAtBaEcSEa12QvJWz9FFGh3fDN/kbm560oXcl2lDNyQ==", "path": "microsoft.entityframeworkcore.sqlserver/7.0.4", "hashPath": "microsoft.entityframeworkcore.sqlserver.7.0.4.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LH4OE/76F6sOCslif7+Xh3fS/wUUrE5ryeXAMcoCnuwOQGT5Smw0p57IgDh/pHgHaGz/e+AmEQb7pRgb++wt0w==", "path": "microsoft.extensions.apidescription.server/3.0.0", "hashPath": "microsoft.extensions.apidescription.server.3.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IeimUd0TNbhB4ded3AbgBLQv2SnsiVugDyGV1MvspQFVlA07nDC7Zul7kcwH5jWN3JiTcp/ySE83AIJo8yfKjg==", "path": "microsoft.extensions.caching.abstractions/7.0.0", "hashPath": "microsoft.extensions.caching.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xpidBs2KCE2gw1JrD0quHE72kvCaI3xFql5/Peb2GRtUuZX+dYPoK/NTdVMiM67Svym0M0Df9A3xyU0FbMQhHw==", "path": "microsoft.extensions.caching.memory/7.0.0", "hashPath": "microsoft.extensions.caching.memory.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-b3<PERSON>rKzND8LIC7o08QAVlKfaEIYEvLJbtmVbFZVBRXeu9YkKfSSzLZfR1SUfQPBIy9mKLhEtJgGYImkcMNaKE0A==", "path": "microsoft.extensions.configuration.binder/6.0.0", "hashPath": "microsoft.extensions.configuration.binder.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "path": "microsoft.extensions.dependencyinjection/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-oONNYd71J3LzkWc4fUHl3SvMfiQMYUCo/mDHDEu76hYYxdhdrPYv6fvGv9nnKVyhE9P0h20AU8RZB5OOWQcAXg==", "path": "microsoft.extensions.dependencymodel/7.0.0", "hashPath": "microsoft.extensions.dependencymodel.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cWz4caHwvx0emoYe7NkHPxII/KkTI8R/LC9qdqJqnKv2poTJ4e2qqPGQqvRoQ5kaSA4FU5IV3qFAuLuOhoqULQ==", "path": "microsoft.extensions.http/8.0.0", "hashPath": "microsoft.extensions.http.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "path": "microsoft.extensions.logging/8.0.0", "hashPath": "microsoft.extensions.logging.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/5.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-pp9tbGqIhdEXL6Q1yJl+zevAJSq4BsxqhS1GXzBvEsEz9DDNu9GLNzgUy2xyFc4YjB4m4Ff2YEWTnvQvVYdkvQ==", "path": "microsoft.extensions.objectpool/5.0.10", "hashPath": "microsoft.extensions.objectpool.5.0.10.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "path": "microsoft.extensions.options/8.0.0", "hashPath": "microsoft.extensions.options.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.47.2": {"type": "package", "serviceable": true, "sha512": "sha512-SPgesZRbXoDxg8Vv7k5Ou0ee7uupVw0E8ZCc4GKw25HANRLz1d5OSr0fvTVQRnEswo5Obk8qD4LOapYB+n5kzQ==", "path": "microsoft.identity.client/4.47.2", "hashPath": "microsoft.identity.client.4.47.2.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/2.19.3": {"type": "package", "serviceable": true, "sha512": "sha512-zVVZjn8aW7W79rC1crioDgdOwaFTQorsSO6RgVlDDjc7MvbEGz071wSNrjVhzR0CdQn6Sefx7Abf1o7vasmrLg==", "path": "microsoft.identity.client.extensions.msal/2.19.3", "hashPath": "microsoft.identity.client.extensions.msal.2.19.3.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-X6aBK56Ot15qKyG7X37KsPnrwah+Ka55NJWPppWVTDi8xWq7CJgeNw2XyaeHgE1o/mW4THwoabZkBbeG2TPBiw==", "path": "microsoft.identitymodel.abstractions/6.24.0", "hashPath": "microsoft.identitymodel.abstractions.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-XDWrkThcxfuWp79AvAtg5f+uRS1BxkIbJnsG/e8VPzOWkYYuDg33emLjp5EWcwXYYIDsHnVZD/00kM/PYFQc/g==", "path": "microsoft.identitymodel.jsonwebtokens/6.24.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-qLYWDOowM/zghmYKXw1yfYKlHOdS41i8t4hVXr9bSI90zHqhyhQh9GwVy8pENzs5wHeytU23DymluC9NtgYv7w==", "path": "microsoft.identitymodel.logging/6.24.0", "hashPath": "microsoft.identitymodel.logging.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-+NzKCkvsQ8X1r/Ff74V7CFr9OsdMRaB6DsV+qpH7NNLdYJ8O4qHbmTnNEsjFcDmk/gVNDwhoL2gN5pkPVq0lwQ==", "path": "microsoft.identitymodel.protocols/6.24.0", "hashPath": "microsoft.identitymodel.protocols.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-a/2RRrc8C9qaw8qdD9hv1ES9YKFgxaqr/SnwMSLbwQZJSUQDd4qx1K4EYgWaQWs73R+VXLyKSxN0f/uE9CsBiQ==", "path": "microsoft.identitymodel.protocols.openidconnect/6.24.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.WsTrust/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-KiRRp/pAtbJtyK7wPIiUW1FMna1T0IEizDWFL9R0C60jk6t66U95fo0SPl3ztOkmnS4v7uF1zWjfAEgZ/i+Zhg==", "path": "microsoft.identitymodel.protocols.wstrust/6.8.0", "hashPath": "microsoft.identitymodel.protocols.wstrust.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZPqHi86UYuqJXJ7bLnlEctHKkPKT4lGUFbotoCNiXNCSL02emYlcxzGYsRGWWmbFEcYDMi2dcTLLYNzHqWOTsw==", "path": "microsoft.identitymodel.tokens/6.24.0", "hashPath": "microsoft.identitymodel.tokens.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens.Saml/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-zRbtJ7Kvr2RMcXi4g4ta3og/wX0GZpLGfb/h7aohwNAaUtCooRpx7Gl8Cv7tn4FDAp6MZwBQL/w0jMeyVTkjPQ==", "path": "microsoft.identitymodel.tokens.saml/6.8.0", "hashPath": "microsoft.identitymodel.tokens.saml.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Xml/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-p2DOCNVVOQpxfx9c3FW0kJve2jAAZB/zecWsi9S5fpznbJ/VcH0zxKdz6wIXjDQgwf2xg/u/k58uHiS/o+0qiA==", "path": "microsoft.identitymodel.xml/6.8.0", "hashPath": "microsoft.identitymodel.xml.6.8.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.OpenApi/1.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-2X5CCFJCnx6v86fnpOg4TKlU1ba7MSf1yakeT7VI4846s7i6fOkERwStX94Rcq8wJsLyQYsUitd6vR38viePeA==", "path": "microsoft.openapi/1.3.1", "hashPath": "microsoft.openapi.1.3.1.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2nXPrhdAyAzir0gLl8Yy8S5Mnm/uBSQQA7jEsILOS1MTyS7DbmV1NgViMtvV1sfCD1ebITpNwb1NIinKeJgUVQ==", "path": "microsoft.win32.systemevents/7.0.0", "hashPath": "microsoft.win32.systemevents.7.0.0.nupkg.sha512"}, "Mono.TextTemplating/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-KZYeKBET/2Z0gY1WlTAK7+RHTl7GSbtvTLDXEZZojUdAPqpQNDL6tHv7VUpqfX5VEOh+uRGKaZXkuD253nEOBQ==", "path": "mono.texttemplating/2.2.1", "hashPath": "mono.texttemplating.2.2.1.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "path": "newtonsoft.json.bson/1.0.2", "hashPath": "newtonsoft.json.bson.1.0.2.nupkg.sha512"}, "NLog/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-kfjfjcjh/hHXLJ0TbEUH6ajb2jQFmwk/23nyYW9iPZ6cj5769SyeDTbUwGI7LSVUk5iTRJoC6CTKKmWrXK79oA==", "path": "nlog/6.0.2", "hashPath": "nlog.6.0.2.nupkg.sha512"}, "NLog.Extensions.Logging/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-H+kC/F52BF6EEzj2qYdPOEwH2EvnP1A2NZsvMBjWdnsjeRH1Bf1i/Mk2G+M+DXutJ/AFd+yV6gF0rbp3KLzk9w==", "path": "nlog.extensions.logging/6.0.2", "hashPath": "nlog.extensions.logging.6.0.2.nupkg.sha512"}, "NLog.Web.AspNetCore/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-8Ei0wkF6kD8nJMuqMIaMyiZwluERatPLzx7NCK7wdgriXLOrEGGHt+nXLLsVKBZWfbuvjXxKwGKORsVgFR/EkA==", "path": "nlog.web.aspnetcore/6.0.2", "hashPath": "nlog.web.aspnetcore.6.0.2.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "Scrutor/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BwqCnFzp2/Z+pq17iztxlIkR/ZANyPRR4PdE57WL1w/JW4AM/2imoxBWTL3+G+YXA46ce4s9OUgwWqTXYrtI8A==", "path": "scrutor/3.3.0", "hashPath": "scrutor.3.3.0.nupkg.sha512"}, "Serilog/2.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-+QX0hmf37a0/OZLxM3wL7V6/ADvC1XihXN4Kq/p6d8lCPfgkRdiuhbWlMaFjR9Av0dy5F0+MBeDmDdRZN/YwQA==", "path": "serilog/2.10.0", "hashPath": "serilog.2.10.0.nupkg.sha512"}, "Serilog.Extensions.Logging/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-IWfem7wfrFbB3iw1OikqPFNPEzfayvDuN4WP7Ue1AVFskalMByeWk3QbtUXQR34SBkv1EbZ3AySHda/ErDgpcg==", "path": "serilog.extensions.logging/3.1.0", "hashPath": "serilog.extensions.logging.3.1.0.nupkg.sha512"}, "Serilog.Extensions.Logging.File/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bUYjMHn7NhpK+/8HDftG7+G5hpWzD49XTSvLoUFZGgappDa6FoseqFOsLrjLRjwe1zM+igH5mySFJv3ntb+qcg==", "path": "serilog.extensions.logging.file/3.0.0", "hashPath": "serilog.extensions.logging.file.3.0.0.nupkg.sha512"}, "Serilog.Formatting.Compact/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-pNroKVjo+rDqlxNG5PXkRLpfSCuDOBY0ri6jp9PLe505ljqwhwZz8ospy2vWhQlFu5GkIesh3FcDs4n7sWZODA==", "path": "serilog.formatting.compact/1.1.0", "hashPath": "serilog.formatting.compact.1.1.0.nupkg.sha512"}, "Serilog.Sinks.Async/1.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-csHYIqAwI4Gy9oAhXYRwxGrQEAtBg3Ep7WaCzsnA1cZuBZjVAU0n7hWaJhItjO7hbLHh/9gRVxALCUB4Dv+gZw==", "path": "serilog.sinks.async/1.5.0", "hashPath": "serilog.sinks.async.1.5.0.nupkg.sha512"}, "Serilog.Sinks.File/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-VHbo68pMg5hwSWrzLEdZv5b/rYmIgHIRhd4d5rl8GnC5/a8Fr+RShT5kWyeJOXax1el6mNJ+dmHDOVgnNUQxaw==", "path": "serilog.sinks.file/3.2.0", "hashPath": "serilog.sinks.file.3.2.0.nupkg.sha512"}, "Serilog.Sinks.RollingFile/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-2lT5X1r3GH4P0bRWJfhA7etGl8Q2Ipw9AACvtAHWRUSpYZ42NGVyHoVs2ALBZ/cAkkS+tA4jl80Zie144eLQPg==", "path": "serilog.sinks.rollingfile/3.3.0", "hashPath": "serilog.sinks.rollingfile.3.3.0.nupkg.sha512"}, "Swashbuckle.AspNetCore/6.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-cnzQDn0Le+hInsw2SYwlOhOCPXpYi/szcvnyqZJ12v+QyrLBwAmWXBg6RIyHB18s/mLeywC+Rg2O9ndz0IUNYQ==", "path": "swashbuckle.aspnetcore/6.2.3", "hashPath": "swashbuckle.aspnetcore.6.2.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.Filters/7.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-AVvG44LqDjB765pVldSbRPpH6iTIo6xmyoZE9aP8FcREMMUj7WB1cSMF+bWtUCkeEVwVTr8iGDxLfrCS03uIuQ==", "path": "swashbuckle.aspnetcore.filters/7.0.5", "hashPath": "swashbuckle.aspnetcore.filters.7.0.5.nupkg.sha512"}, "Swashbuckle.AspNetCore.Filters.Abstractions/7.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-iY3FK5URcNqdESPFDWL0YAe7X2sxZJj7YM5gTCIHvtu4d/h9NurhBLII96+obQghbJ7i45TVlPeYo4jb9WmVGg==", "path": "swashbuckle.aspnetcore.filters.abstractions/7.0.5", "hashPath": "swashbuckle.aspnetcore.filters.abstractions.7.0.5.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/6.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-nl4SBgGM+cmthUcpwO/w1lUjevdDHAqRvfUoe4Xp/Uvuzt9mzGUwyFCqa3ODBAcZYBiFoKvrYwz0rabslJvSmQ==", "path": "swashbuckle.aspnetcore.swagger/6.4.0", "hashPath": "swashbuckle.aspnetcore.swagger.6.4.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/6.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-+Xq7WdMCCfcXlnbLJVFNgY8ITdP2TRYIlpbt6IKzDw5FwFxdi9lBfNDtcT+/wkKwX70iBBFmXldnnd02/VO72A==", "path": "swashbuckle.aspnetcore.swaggergen/6.2.3", "hashPath": "swashbuckle.aspnetcore.swaggergen.6.2.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/6.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-bCRI87uKJVb4G+KURWm8LQrL64St04dEFZcF6gIM67Zc0Sr/N47EO83ybLMYOvfNdO1DCv8xwPcrz9J/VEhQ5g==", "path": "swashbuckle.aspnetcore.swaggerui/6.2.3", "hashPath": "swashbuckle.aspnetcore.swaggerui.6.2.3.nupkg.sha512"}, "System.CodeDom/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-Hs9pw/kmvH3lXaZ1LFKj3pLQsiGfj2xo3sxSzwiLlRL6UcMZUTeCfoJ9Udalvn3yq5dLlPEZzYegrTQ1/LhPOQ==", "path": "system.codedom/4.7.0", "hashPath": "system.codedom.4.7.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.ComponentModel.Annotations/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-0YFqjhp/mYkDGpU0Ye1GjE53HMp9UVfGN7seGpAMttAC0C40v5gw598jCgpbBLMmCo0E5YRLBv5Z2doypO49ZQ==", "path": "system.componentmodel.annotations/4.7.0", "hashPath": "system.componentmodel.annotations.4.7.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WvRUdlL1lB0dTRZSs5XcQOd5q9MYNk90GkbmRmiCvRHThWiojkpGqWdmEDJdXyHbxG/BhE5hmVbMfRLXW9FJVA==", "path": "system.configuration.configurationmanager/7.0.0", "hashPath": "system.configuration.configurationmanager.7.0.0.nupkg.sha512"}, "System.Data.SqlClient/4.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-HKLykcv6eZLbLnSMnlQ6Os4+UAmFE+AgYm92CTvJYeTOBtOYusX3qu8OoGhFrnKZax91UcLcDo5vPrqvJUTSNQ==", "path": "system.data.sqlclient/4.8.1", "hashPath": "system.data.sqlclient.4.8.1.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-frQDfv0rl209cKm1lnwTgFPzNigy2EKk1BS3uAvHvlBVKe5cymGyHO+Sj+NLv5VF/AhHsqPIUUwya5oV4CHMUw==", "path": "system.diagnostics.diagnosticsource/6.0.0", "hashPath": "system.diagnostics.diagnosticsource.6.0.0.nupkg.sha512"}, "System.Drawing.Common/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-K<PERSON>+oBU38pxkKPxvLcLfIkOV5Ien8ReN78wro7OF5/erwcmortzeFx+iBswlh2Vz6gVne0khocQudGwaO1Ey6A==", "path": "system.drawing.common/7.0.0", "hashPath": "system.drawing.common.7.0.0.nupkg.sha512"}, "System.Formats.Asn1/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T6fD00dQ3NTbPDy31m4eQUwKW84s03z0N2C8HpOklyeaDgaJPa/TexP4/SkORMSOwc7WhKifnA6Ya33AkzmafA==", "path": "system.formats.asn1/6.0.0", "hashPath": "system.formats.asn1.6.0.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-Qibsj9MPWq8S/C0FgvmsLfIlHLE7ay0MJIaAmK94ivN3VyDdglqReed5qMvdQhSL0BzK6v0Z1wB/sD88zVu6Jw==", "path": "system.identitymodel.tokens.jwt/6.24.0", "hashPath": "system.identitymodel.tokens.jwt.6.24.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Private.ServiceModel/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-BcUV7OERlLqGxDXZuIyIMMmk1PbqBblLRbAoigmzIUx/M8A+8epvyPyXRpbgoucKH7QmfYdQIev04Phx2Co08A==", "path": "system.private.servicemodel/4.10.3", "hashPath": "system.private.servicemodel.4.10.3.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.DispatchProxy/4.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-C1sMLwIG6ILQ2bmOT4gh62V6oJlyF4BlHcVMrOoor49p0Ji2tA8QAoqyMcIhAdH6OHKJ8m7BU+r4LK2CUEOKqw==", "path": "system.reflection.dispatchproxy/4.7.1", "hashPath": "system.reflection.dispatchproxy.4.7.1.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Caching/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-E0e03kUp5X2k+UAoVl6efmI7uU7JRBWi5EIdlQ7cr0NpBGjHG4fWII35PgsBY9T4fJQ8E4QPsL0rKksU9gcL5A==", "path": "system.runtime.caching/6.0.0", "hashPath": "system.runtime.caching.6.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jIMXsKn94T9JY7PvPq/tMfqa6GAaHpElRDpmG+SuL+D3+sTw2M8VhnibKnN8Tq+4JqbPJ/f+BwtLeDMEnzAvRg==", "path": "system.security.cryptography.cng/5.0.0", "hashPath": "system.security.cryptography.cng.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ynmbW2GjIGg9K1wXmVIRs4IlyDolf0JXNpzFQ8JCVgwM+myUC2JeUggl2PwQig2PNVMegKmN1aAx7WPQ8tI3vA==", "path": "system.security.cryptography.pkcs/6.0.1", "hashPath": "system.security.cryptography.pkcs.6.0.1.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xSPiLNlHT6wAHtugASbKAJwV5GVqQK351crnILAucUioFqqieDN79evO1rku1ckt/GfjIn+b17UaSskoY03JuA==", "path": "system.security.cryptography.protecteddata/7.0.0", "hashPath": "system.security.cryptography.protecteddata.7.0.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5e5bI28T0x73AwTsbuFP4qSRzthmU2C0Gqgg3AZ3KTxmSyA+Uhk31puA3srdaeWaacVnHhLdJywCzqOiEpbO/w==", "path": "system.security.cryptography.xml/6.0.1", "hashPath": "system.security.cryptography.xml.6.0.1.nupkg.sha512"}, "System.Security.Permissions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vmp0iRmCEno9BWiskOW5pxJ3d9n+jUqKxvX4GhLwFhnQaySZmBN2FuC0N5gjFHgyFMUjC5sfIJ8KZfoJwkcMmA==", "path": "system.security.permissions/7.0.0", "hashPath": "system.security.permissions.7.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.ServiceModel.Duplex/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-IZ8ZahvTenWML7/jGUXSCm6jHlxpMbcb+Hy+h5p1WP9YVtb+Er7FHRRGizqQMINEdK6HhWpD6rzr5PdxNyusdg==", "path": "system.servicemodel.duplex/4.10.3", "hashPath": "system.servicemodel.duplex.4.10.3.nupkg.sha512"}, "System.ServiceModel.Federation/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-VFQgE+XFdUXvxShtdQwNJ0AxfYr0ZVS0TlR3yRwCZxtcRPD0rnkSNfTbIiLxjH0j+O+IrtNZh9EDAQCLEiSNMA==", "path": "system.servicemodel.federation/4.10.3", "hashPath": "system.servicemodel.federation.4.10.3.nupkg.sha512"}, "System.ServiceModel.Http/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-hodkn0rPTYmoZ9EIPwcleUrOi1gZBPvU0uFvzmJbyxl1lIpVM5GxTrs/pCETStjOXCiXhBDoZQYajquOEfeW/w==", "path": "system.servicemodel.http/4.10.3", "hashPath": "system.servicemodel.http.4.10.3.nupkg.sha512"}, "System.ServiceModel.NetTcp/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-tP7GN7ehqSIQEz7yOJEtY8ziTpfavf2IQMPKa7r9KGQ75+uEW6/wSlWez7oKQwGYuAHbcGhpJvdG6WoVMKYgkw==", "path": "system.servicemodel.nettcp/4.10.3", "hashPath": "system.servicemodel.nettcp.4.10.3.nupkg.sha512"}, "System.ServiceModel.Primitives/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-aNcdry95wIP1J+/HcLQM/f/AA73LnBQDNc2uCoZ+c1//KpVRp8nMZv5ApMwK+eDNVdCK8G0NLInF+xG3mfQL+g==", "path": "system.servicemodel.primitives/4.10.3", "hashPath": "system.servicemodel.primitives.4.10.3.nupkg.sha512"}, "System.ServiceModel.Security/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-vqelKb7DvP2inb6LDJ5Igi8wpOYdtLXn5luDW5qEaqkV2sYO1pKlVYBpr6g6m5SevzbdZlVNu67dQiD/H6EdGQ==", "path": "system.servicemodel.security/4.10.3", "hashPath": "system.servicemodel.security.4.10.3.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "path": "system.text.encoding.codepages/6.0.0", "hashPath": "system.text.encoding.codepages.6.0.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OP6umVGxc0Z0MvZQBVigj4/U31Pw72ITihDWP9WiWDm+q5aoe0GaJivsfYGq53o6dxH7DcXWiCTl7+0o2CGdmg==", "path": "system.text.encodings.web/7.0.0", "hashPath": "system.text.encodings.web.7.0.0.nupkg.sha512"}, "System.Text.Json/7.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-AyjhwXN1zTFeIibHimfJn6eAsZ7rTBib79JQpzg8WAuR/HKDu9JGNHTuu3nbbXQ/bgI+U4z6HtZmCHNXB1QXrQ==", "path": "system.text.json/7.0.3", "hashPath": "system.text.json.7.0.3.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RpT2DA+L660cBt1FssIE9CAGpLFdFPuheB7pLpKpn6ZXNby7jDERe8Ua/Ne2xGiwLVG2JOqziiaVCGDon5sKFA==", "path": "system.text.regularexpressions/4.3.0", "hashPath": "system.text.regularexpressions.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Threading.Timer/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-saGfUV8uqVW6LeURiqxcGhZ24PzuRNaUBtbhVeuUAvky1naH395A/1nY0P2bWvrw/BreRtIB/EzTDkGBpqCwEw==", "path": "system.threading.timer/4.0.1", "hashPath": "system.threading.timer.4.0.1.nupkg.sha512"}, "System.Windows.Extensions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bR4qdCmssMMbo9Fatci49An5B1UaVJZHKNq70PRgzoLYIlitb8Tj7ns/Xt5Pz1CkERiTjcVBDU2y1AVrPBYkaw==", "path": "system.windows.extensions/7.0.0", "hashPath": "system.windows.extensions.7.0.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lJ8AxvkX7GQxpC6GFCeBj8ThYVyQczx2+f/cWHJU8tjS7YfI6Cv6bon70jVEgs2CiFbmmM8b9j1oZVx0dSI2Ww==", "path": "system.xml.xmldocument/4.3.0", "hashPath": "system.xml.xmldocument.4.3.0.nupkg.sha512"}, "Mydentist.MyDSitefinityApi.ClinicianPortalApi/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Mydentist.MyDSitefinityAPI.Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Mydentist.MyDSitefinityAPI.ImplementationServices/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Mydentist.MyDSitefinityAPI.Persistence/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Mydentist.MyDSitefinityAPI.WebConfigApi/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "SitefinityServiceCaller.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Mydentist.MyDSitefinityApi.ClinicianPortalApi.Reference/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Mydentist.MyDSitefinityAPI.Domain.Reference/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Mydentist.MyDSitefinityAPI.ImplementationServices.Reference/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Mydentist.MyDSitefinityAPI.Persistence.Reference/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Mydentist.MyDSitefinityAPI.WebConfigApi.Reference/*******": {"type": "reference", "serviceable": false, "sha512": ""}}}