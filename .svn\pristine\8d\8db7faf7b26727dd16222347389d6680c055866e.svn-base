﻿using System.Text.Json;

namespace MyDSitefinityAPI.ExtensionMethods
{
    public static class SiteFinityFormExtensionMethods
    {
        public static string GetPropertyValueByTitle(this SitefinityForm sitefinityForm, string propertyName)
        {
            return sitefinityForm?.OriginalEvent?.Controls?.Where(x => x.Title?.ToLower() == propertyName.ToLower())?.FirstOrDefault()?.Value;
        }

        public static JsonElement GetPropertyValueByTitle(this SitefinityFormV2 sitefinityForm, string propertyName)
        {
            return (JsonElement)(sitefinityForm?.OriginalEvent?.Controls?.Where(x => x.Title?.ToLower() == propertyName.ToLower())?.FirstOrDefault()?.Value);
        }

        public static string GetPropertyValueByTitle(this SitefinityForm sitefinityForm, string[] propertyNames)
        {
            return sitefinityForm?.OriginalEvent?.Controls?.Where(x => propertyNames.Select(pn => pn?.ToLower()).Contains(x?.Title?.ToLower()))?.FirstOrDefault()?.Value;
        }

        public static string GetLocationTextByTitle(this SitefinityForm sitefinityForm, string[] propertyNames)
        {
            string inputValue = sitefinityForm?.OriginalEvent?.Controls?.Where(x => propertyNames.Contains(x?.Title))?.FirstOrDefault()?.Value;

            if (string.IsNullOrEmpty(inputValue) || inputValue.Count(c => c == '|') != 2)
                return null;

            string[] locationInputs = inputValue.Split("|");

            return locationInputs[0];
        }

        public static string GetLocationLatitudeByTitle(this SitefinityForm sitefinityForm, string[] propertyNames)
        {
            string inputValue = sitefinityForm?.OriginalEvent?.Controls?.Where(x => propertyNames.Contains(x?.Title))?.FirstOrDefault()?.Value;

            if (string.IsNullOrEmpty(inputValue) || inputValue.Count(c => c == '|') != 2)
                return null;

            string[] locationInputs = inputValue.Split("|");

            return locationInputs[1];
        }

        public static string GetLocationLongitudeByTitle(this SitefinityForm sitefinityForm, string[] propertyNames)
        {
            string inputValue = sitefinityForm?.OriginalEvent?.Controls?.Where(x => propertyNames.Contains(x?.Title))?.FirstOrDefault()?.Value;

            if (string.IsNullOrEmpty(inputValue) || inputValue.Count(c => c == '|') != 2)
                return null;

            string[] locationInputs = inputValue.Split("|");

            return locationInputs[2];
        }

        public static string GetPropertyValueByFieldName(this SitefinityForm sitefinityForm, string fieldName)
        {
            return sitefinityForm?.OriginalEvent?.Controls?.FirstOrDefault(x => x.FieldName?.ToLower() == fieldName.ToLower())?.Value ?? "";
        }


        public static object GetPropertyValueByFieldName(this SitefinityFormV2 sitefinityForm, string fieldName)
        {
            return sitefinityForm?.OriginalEvent?.Controls?.FirstOrDefault(x => x.FieldName?.ToLower() == fieldName.ToLower())?.Value ?? "";
        }
    }
}
