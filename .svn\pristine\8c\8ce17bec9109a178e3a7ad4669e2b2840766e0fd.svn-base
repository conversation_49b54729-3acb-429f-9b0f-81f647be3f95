﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Mydentist.MyDSitefinityAPI.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class SimplificationOfRecruitmentForm : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Channel",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm");

            migrationBuilder.DropColumn(
                name: "ClinicianType",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm");

            migrationBuilder.DropColumn(
                name: "Location",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm");

            migrationBuilder.DropColumn(
                name: "NonClinicianType",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm");

            migrationBuilder.DropColumn(
                name: "Role",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm");

            migrationBuilder.AlterColumn<int>(
                name: "University",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                type: "int",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "DesiredWorkingLocation",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                type: "int",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "DesiredRole",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                type: "int",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "University",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "DesiredWorkingLocation",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "DesiredRole",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Channel",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ClinicianType",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Location",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NonClinicianType",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Role",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                type: "nvarchar(max)",
                nullable: true);
        }
    }
}
