using MyDSitefinityAPI.Interfaces;
using MyDSitefinityAPI.Models;

namespace MyDSitefinityAPI.IntegrationTests.Mock
{
    public class MockFindNearestPracticeService : IFindNearestPracticeService
    {
        public Task<(int? practiceId, string postcode)> FindNearestPracticePostcode(SitefinityForm form)
        {
            // Mock implementation - returns a mock practice
            return Task.FromResult<(int?, string)>((1, "M1 1AA"));
        }
    }
}
