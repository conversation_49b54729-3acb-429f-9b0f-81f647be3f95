﻿using Microsoft.Extensions.Logging;
using Mydentist.MyDSitefinityApi.ClinicianPortalApi;
using Mydentist.MyDSitefinityAPI.Domain;
using Mydentist.MyDSitefinityAPI.Persistence;
using Mydentist.MyDSitefinityAPI.WebConfigApi;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mydentist.MyDSitefinityAPI.ImplementationServices
{
    public class RecruitmentFormTransformerService: IRecruitmentFormTransformerService
    {
        private readonly ISharedListMappingService _sharedListMappingService;
        private readonly ILogger _logger;
        private readonly IWebsiteConfigRoleService _websiteConfigRoleService;


        public RecruitmentFormTransformerService(ISharedListMappingService sharedListMappingService,
                                                ILogger logger,
                                                IWebsiteConfigRoleService websiteConfigRoleService)
        {
            _sharedListMappingService = sharedListMappingService;
            _logger = logger;
            _websiteConfigRoleService = websiteConfigRoleService;

        }

        public async Task<RecruitmentWebsiteFormModel> InjectSharedListValuesAndSetChannel(RecruitmentWebsiteFormRequest recruitmentWebsiteFormRequest)
        {
            RecruitmentWebsiteFormModel recruitmentWebsiteFormModel = new RecruitmentWebsiteFormModel();

            if (!string.IsNullOrEmpty(recruitmentWebsiteFormRequest.UniversityUndergraduate))
                recruitmentWebsiteFormModel.UniversityUndergraduate = await GetListValue("University", recruitmentWebsiteFormRequest.UniversityUndergraduate);

            if (!string.IsNullOrEmpty(recruitmentWebsiteFormRequest.UniversityPostgraduate))
                recruitmentWebsiteFormModel.UniversityPostgraduate = await GetListValue("University", recruitmentWebsiteFormRequest.UniversityPostgraduate);

            if (!string.IsNullOrEmpty(recruitmentWebsiteFormRequest.DesiredRole))
                recruitmentWebsiteFormModel = await SetChannelAndInternalRoleBasedOnExternalRole(recruitmentWebsiteFormModel, recruitmentWebsiteFormRequest.DesiredRole);

            if (!string.IsNullOrEmpty(recruitmentWebsiteFormRequest.DesiredRole))
                recruitmentWebsiteFormModel.PostgraduateQualification = await TranslateWebsiteRoleToQualification(recruitmentWebsiteFormRequest.DesiredRole);

            if (!string.IsNullOrEmpty(recruitmentWebsiteFormRequest.CountryOfQualification))
                recruitmentWebsiteFormModel.CountryOfQualification = await GetListValue("Country", recruitmentWebsiteFormRequest.CountryOfQualification);

            return recruitmentWebsiteFormModel;
        }

        public async Task<RecruitmentWebsiteFormModel> SetChannelAndInternalRoleBasedOnExternalRole(RecruitmentWebsiteFormModel recruitmentWebsiteFormModel, string desiredRole)
        {
            var websiteConfigRole = await GetWebsiteRole(desiredRole);

            if (websiteConfigRole == null)
                return recruitmentWebsiteFormModel;

            recruitmentWebsiteFormModel.DesiredRole = websiteConfigRole.DesiredRoleId;
            recruitmentWebsiteFormModel.ChannelId = websiteConfigRole.ChannelId;

            return recruitmentWebsiteFormModel;
        }

        public async Task<WebsiteConfigModel> GetWebsiteRole(string websiteRoleName)
        {
            try
            {
                WebsiteConfigModel websiteConfigModel = await _websiteConfigRoleService.GetDesiredRoleConfig(websiteRoleName);

                if (websiteConfigModel == null)
                {
                    _logger.LogWarning($"No list values found for Role {websiteRoleName} from the website.");

                    return null;
                }

                if (websiteConfigModel.DesiredRoleId == null)
                {
                    _logger.LogWarning($"No matching list values found for Role {websiteRoleName} from the website.");
                }

                _logger.LogWarning($"Role {websiteRoleName} found websiteConfig matched to {websiteConfigModel.DesiredRoleId} with Channel {websiteConfigModel.ChannelId}.");

                return websiteConfigModel;
            }
            catch(Exception)
            {
                _logger.LogWarning($"No matching list values found for Role {websiteRoleName} from the website.");
            }

            return null;
        }

        public async Task<string> TranslateWebsiteRoleToQualification(string websiteRoleName)
        {
            IEnumerable<RolePostgraduateLookup> rolePostgraduateLookups =   await _websiteConfigRoleService.GetRolePostgraduates();

            if (rolePostgraduateLookups == null || !rolePostgraduateLookups.Any())
            {
                _logger.LogWarning($"No Role Postgraduate Lookups found.");

                return null;
            }

            RolePostgraduateLookup matchingRolePostGraduateLookup = rolePostgraduateLookups.SingleOrDefault(l => l.WebsiteRole.ToLower() == websiteRoleName.ToLower());

            if (matchingRolePostGraduateLookup == null)
            {
                _logger.LogWarning($"No Role Postgraduate Lookup found to match websiteRoleName {websiteRoleName}.");

                return null;
            }

            return matchingRolePostGraduateLookup.InternalPostgraduateDefinition;
        }


        public async Task<int?> GetListValue(string listName, string listValue)
        {
            IEnumerable<SharedListItemViewModel> sharedListItems = await _sharedListMappingService.GetList(listName);

            if (!sharedListItems.Any()) 
            {
                _logger.LogWarning($"No list values found in Clinician Portal Api for List {listName}");
                return null;
            }

            SharedListItemViewModel sharedListItem = sharedListItems.FirstOrDefault(li => li.DisplayName == listValue);

            if (sharedListItem == null)
            {
                _logger.LogWarning($"No list value found in Clinician Portal Api for List {listName}, Value {listValue}");
                return null;
            }

            return sharedListItem.Id;
        }
    }
}
