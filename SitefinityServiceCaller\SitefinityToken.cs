﻿using System;
using System.Collections.Specialized;

namespace SitefinityAuthentication
{
    public class SitefinityToken
    {
        public SitefinityToken(string token, TimeSpan timeUntilExpiration)
        {
            Token = token;
            ExpirationDate = DateTime.Now.AddMilliseconds(timeUntilExpiration.Milliseconds);
        }

        public SitefinityToken(string token, DateTime expirationDate)
        {
            Token = token;
            ExpirationDate = expirationDate;
        }

        public NameValueCollection GetRequestHeaders()
        {
            return new NameValueCollection
            {
                {"Authorization", $"WRAP access_token=\"{Token}\""}
            };
        }

        public string Token { get; }
        public DateTime ExpirationDate { get; }
    }
}
