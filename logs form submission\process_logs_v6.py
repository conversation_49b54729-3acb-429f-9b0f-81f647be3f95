

import json
import os
import re

def find_json_payloads(content):
    """
    Finds and extracts complete JSON objects that follow the specific marker string.
    It works by counting curly braces to correctly handle nested structures.
    """
    payloads = []
    # Find all occurrences of our starting marker
    marker = 'The raw json at endpoint is:'
    start_indices = [m.end() for m in re.finditer(re.escape(marker), content)]

    for start_index in start_indices:
        # Find the first opening brace after the marker
        first_brace_index = content.find('{', start_index)
        if first_brace_index == -1:
            continue

        open_braces = 1
        # Start scanning from the character after the first brace
        for i in range(first_brace_index + 1, len(content)):
            char = content[i]
            if char == '{':
                open_braces += 1
            elif char == '}':
                open_braces -= 1

            # If open_braces is 0, we have found the matching closing brace
            if open_braces == 0:
                payload_str = content[first_brace_index : i + 1]
                payloads.append(payload_str)
                break  # Exit the inner loop and look for the next marker
    return payloads

def main():
    log_dir = r'C:\Dev\MyDSitefinityAPI\logs form submission'
    unique_structures = {}

    log_files = sorted([f for f in os.listdir(log_dir) if f.startswith('log-') and f.endswith('.txt')])

    for log_file in log_files:
        file_path = os.path.join(log_dir, log_file)
        print(f"Processing {log_file}...\r", end="")
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
        except Exception as e:
            print(f"\nError reading file {log_file}: {e}")
            continue

        json_payloads = find_json_payloads(content)

        for payload_str in json_payloads:
            try:
                outer_payload = json.loads(payload_str)
                inner_json_str = outer_payload.get("Item", {}).get("ParagraphTextFieldController")

                if not inner_json_str or not isinstance(inner_json_str, str):
                    continue

                inner_payload = json.loads(inner_json_str)

                # The structure is defined by the sorted tuple of its keys
                structure_key = tuple(sorted(inner_payload.keys()))

                if structure_key not in unique_structures:
                    unique_structures[structure_key] = {
                        "structure": list(structure_key),
                        "example_payload": inner_payload,
                        "source_file": log_file
                    }
            except (json.JSONDecodeError, AttributeError, TypeError):
                continue

    output_list = list(unique_structures.values())
    output_file = os.path.join(log_dir, 'unique_form_structures.json')
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output_list, f, indent=2)

    print(f"\nProcessed {len(log_files)} log files.")
    print(f"Found {len(output_list)} unique form structures.")
    print(f"Output with examples written to {output_file}")

if __name__ == "__main__":
    main()

