﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Mydentist.MyDSitefinityAPI.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class YearQualifiedToDate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn("YearQualified", "RecruitmentWebsiteForm", "Recruitment");

            migrationBuilder.AddColumn<string>(
                    name: "YearQualified",
                    schema: "Recruitment",
                    table: "RecruitmentWebsiteForm",
                    type: "date",
                    nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }
    }
}
