﻿using Microsoft.IdentityModel.Protocols;
using Mydentist.MyDSitefinityAPI.Domain;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;


namespace Mydentist.MyDSitefinityAPI.WebConfigApi
{
    public class WebsiteConfigWrapperService : IWebsiteConfigWrapperService
    {
        private string _apiKey;
        private string _baseUrl;

        public WebsiteConfigWrapperService()
        {
            _apiKey = ConfigurationManager.AppSettings["websiteConfigApiKey"];
            _baseUrl = ConfigurationManager.AppSettings["websiteConfigBaseUrl"];
        }

        public async Task<T> GetApiResponse<T>(string url)
        {
            HttpClientHandler handler = new HttpClientHandler();

            using (var httpClient = new HttpClient(handler))
            {
                httpClient.DefaultRequestHeaders.Add("Cache-Control", "no-cache");
                httpClient.DefaultRequestHeaders.Add("X-Api-Key", _apiKey);

                Uri uri = new Uri(_baseUrl + url);

                var response = await httpClient.GetAsync(uri);

                var content = await response.Content.ReadAsStringAsync();

                if (response.StatusCode == HttpStatusCode.BadRequest)
                    throw new WebException(content);

                if (!response.IsSuccessStatusCode)
                    throw new WebException(
                        $"Unsuccessful result ({response.StatusCode}) when calling {uri} from Api {response.ReasonPhrase} when communicating with API as user {Environment.UserName}. {content}");

                if (content == null || string.IsNullOrEmpty(content))
                    throw new InvalidDataException(
                        $"Expected a result from GetApiResponse but {content} value is determined");

                return JsonConvert.DeserializeObject<T>(content);
            }
        }
    }

    public interface IWebsiteConfigWrapperService
    {
        Task<T> GetApiResponse<T>(string url);
    }
}
