2025-07-31T09:10:42.9210974+01:00  [WRN] Using an in-memory repository. Keys will not be persisted to storage. (28e83010)
2025-07-31T09:10:42.9244232+01:00  [WRN] Neither user profile nor HKLM registry available. Using an ephemeral key repository. Protected data will be unavailable when application exits. (54f66960)
2025-07-31T09:10:42.9661661+01:00  [WRN] No XML encryptor configured. Key {1dba9b84-6735-4e73-8bd9-3b8c150e2ee7} may be persisted to storage in unencrypted form. (9ca7e61e)
2025-07-31T09:10:43.0483522+01:00 800167ff-0000-6a00-b63f-84710c7967bb [WRN] Failed to determine the https port for redirect. (ca76cc21)
2025-07-31T09:10:43.3999158+01:00 800167ff-0000-6a00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4227\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Divyesh\",\"lastName\":\"Sonigra\",\"email\":\"<EMAIL>\",\"phone\":\"7450260656\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"WF1 2FH\",\"CurrentlyLive\":\"GBR\",\"GraduateFrom\":\"GBR\",\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"3\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"2\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"271818\"}],\"ParentQuestionId\":3}]}]}",
    "FileFieldController": [
      {
        "Id": "9825c726-03fc-4757-a297-e780f93bb71d",
        "ChildItemId": "564434eb-9b0f-4856-97f4-7b8b31e50269",
        "ParentItemId": "6fe75c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4227_Divyesh_Sonigra.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T08:10:41.753Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "6fe75c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4227_Divyesh_Sonigra.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "6fe75c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6029",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T08:10:41.7381321Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4227\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Divyesh\",\"lastName\":\"Sonigra\",\"email\":\"<EMAIL>\",\"phone\":\"7450260656\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"WF1 2FH\",\"CurrentlyLive\":\"GBR\",\"GraduateFrom\":\"GBR\",\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"3\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"2\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"271818\"}],\"ParentQuestionId\":3}]}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "9825c726-03fc-4757-a297-e780f93bb71d",
            "ChildItemId": "564434eb-9b0f-4856-97f4-7b8b31e50269",
            "ParentItemId": "6fe75c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4227_Divyesh_Sonigra.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T08:10:41.753Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "6fe75c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4227_Divyesh_Sonigra.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (93a9f126)
2025-07-31T09:10:43.4910742+01:00 800167ff-0000-6a00-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4227\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Divyesh\",\"lastName\":\"Sonigra\",\"email\":\"<EMAIL>\",\"phone\":\"7450260656\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"WF1 2FH\",\"CurrentlyLive\":\"GBR\",\"GraduateFrom\":\"GBR\",\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"3\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"2\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"271818\"}],\"ParentQuestionId\":3}]}]}",
    "FileFieldController": [
      {
        "Id": "9825c726-03fc-4757-a297-e780f93bb71d",
        "ChildItemId": "564434eb-9b0f-4856-97f4-7b8b31e50269",
        "ParentItemId": "6fe75c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4227_Divyesh_Sonigra.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T08:10:41.753Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "6fe75c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4227_Divyesh_Sonigra.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "6fe75c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6029",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T08:10:41.7381321Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4227\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Divyesh\",\"lastName\":\"Sonigra\",\"email\":\"<EMAIL>\",\"phone\":\"7450260656\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"WF1 2FH\",\"CurrentlyLive\":\"GBR\",\"GraduateFrom\":\"GBR\",\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"3\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"2\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"271818\"}],\"ParentQuestionId\":3}]}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "9825c726-03fc-4757-a297-e780f93bb71d",
            "ChildItemId": "564434eb-9b0f-4856-97f4-7b8b31e50269",
            "ParentItemId": "6fe75c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4227_Divyesh_Sonigra.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T08:10:41.753Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "6fe75c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4227_Divyesh_Sonigra.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (924caf93)
2025-07-31T09:10:43.5765515+01:00 800167ff-0000-6a00-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 6fe75c9e-cae7-6322-9971-ff0200979a84 (a68a83d6)
2025-07-31T09:10:43.9852165+01:00 800167ff-0000-6a00-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-31T09:10:44.2241726+01:00 800167ff-0000-6a00-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-31T09:10:44.2254662+01:00 800167ff-0000-6a00-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-31T09:10:45.0449920+01:00 800167ff-0000-6a00-b63f-84710c7967bb [INF] Received HTTP response headers after 813.9858ms - 201 (f0742c1f)
2025-07-31T09:10:45.0457222+01:00 800167ff-0000-6a00-b63f-84710c7967bb [INF] End processing HTTP request after 831.6022ms - 201 (7656b38e)
2025-07-31T09:10:45.0469891+01:00 800167ff-0000-6a00-b63f-84710c7967bb [INF] (Webjob Application) Posted 6fe75c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is True (1398f5bf)
2025-07-31T09:10:47.7629466+01:00 800167ff-0000-6a00-b63f-84710c7967bb [INF] (Webjob Application) Posted 6fe75c9e-cae7-6322-9971-ff0200979a84  (f70a6664)
2025-07-31T09:11:34.9078275+01:00 80328587-0002-d900-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4227\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Divyesh\",\"lastName\":\"Sonigra\",\"email\":\"<EMAIL>\",\"phone\":\"07450260656\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"WF12FH\",\"CurrentlyLive\":\"GBR\",\"GraduateFrom\":\"GBR\",\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"3\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"2\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"271818\"}],\"ParentQuestionId\":3}]}]}",
    "FileFieldController": [
      {
        "Id": "fa7d5f5a-2e53-4e1e-86ee-6264c6431c13",
        "ChildItemId": "564434eb-9b0f-4856-97f4-7b8b31e50269",
        "ParentItemId": "70e75c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4227_Divyesh_Sonigra.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T08:11:34.777Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "70e75c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4227_Divyesh_Sonigra.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "70e75c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6030",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T08:11:34.7601291Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4227\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Divyesh\",\"lastName\":\"Sonigra\",\"email\":\"<EMAIL>\",\"phone\":\"07450260656\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"WF12FH\",\"CurrentlyLive\":\"GBR\",\"GraduateFrom\":\"GBR\",\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"3\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"2\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"271818\"}],\"ParentQuestionId\":3}]}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "fa7d5f5a-2e53-4e1e-86ee-6264c6431c13",
            "ChildItemId": "564434eb-9b0f-4856-97f4-7b8b31e50269",
            "ParentItemId": "70e75c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4227_Divyesh_Sonigra.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T08:11:34.777Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "70e75c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4227_Divyesh_Sonigra.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (4fdd6880)
2025-07-31T09:11:34.9089355+01:00 80328587-0002-d900-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4227\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Divyesh\",\"lastName\":\"Sonigra\",\"email\":\"<EMAIL>\",\"phone\":\"07450260656\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"WF12FH\",\"CurrentlyLive\":\"GBR\",\"GraduateFrom\":\"GBR\",\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"3\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"2\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"271818\"}],\"ParentQuestionId\":3}]}]}",
    "FileFieldController": [
      {
        "Id": "fa7d5f5a-2e53-4e1e-86ee-6264c6431c13",
        "ChildItemId": "564434eb-9b0f-4856-97f4-7b8b31e50269",
        "ParentItemId": "70e75c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4227_Divyesh_Sonigra.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T08:11:34.777Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "70e75c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4227_Divyesh_Sonigra.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "70e75c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6030",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T08:11:34.7601291Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4227\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Divyesh\",\"lastName\":\"Sonigra\",\"email\":\"<EMAIL>\",\"phone\":\"07450260656\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"WF12FH\",\"CurrentlyLive\":\"GBR\",\"GraduateFrom\":\"GBR\",\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"3\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"2\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"271818\"}],\"ParentQuestionId\":3}]}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "fa7d5f5a-2e53-4e1e-86ee-6264c6431c13",
            "ChildItemId": "564434eb-9b0f-4856-97f4-7b8b31e50269",
            "ParentItemId": "70e75c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4227_Divyesh_Sonigra.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T08:11:34.777Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "70e75c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4227_Divyesh_Sonigra.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (6dac357c)
2025-07-31T09:11:34.9091830+01:00 80328587-0002-d900-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 70e75c9e-cae7-6322-9971-ff0200979a84 (edec1b43)
2025-07-31T09:11:35.1282878+01:00 80328587-0002-d900-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-31T09:11:35.1928571+01:00 80328587-0002-d900-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-31T09:11:35.1929205+01:00 80328587-0002-d900-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-31T09:11:35.7704201+01:00 80328587-0002-d900-b63f-84710c7967bb [INF] Received HTTP response headers after 577.4234ms - 500 (f0742c1f)
2025-07-31T09:11:35.7705212+01:00 80328587-0002-d900-b63f-84710c7967bb [INF] End processing HTTP request after 577.7526ms - 500 (7656b38e)
2025-07-31T09:11:35.7729655+01:00 80328587-0002-d900-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-31T09:11:35.7730402+01:00 80328587-0002-d900-b63f-84710c7967bb [INF] (Webjob Application) Posted 70e75c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (06615dff)
2025-07-31T09:11:35.8486494+01:00 80328587-0002-d900-b63f-84710c7967bb [INF] (Webjob Application) Posted 70e75c9e-cae7-6322-9971-ff0200979a84  (4aab2cb2)
2025-07-31T09:11:37.0793006+01:00 80225ce6-0003-b400-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4227\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Divyesh\",\"lastName\":\"Sonigra\",\"email\":\"<EMAIL>\",\"phone\":\"07450260656\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"WF12FH\",\"CurrentlyLive\":\"GBR\",\"GraduateFrom\":\"GBR\",\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"3\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"2\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"271818\"}],\"ParentQuestionId\":3}]}]}",
    "FileFieldController": [
      {
        "Id": "fa7d5f5a-2e53-4e1e-86ee-6264c6431c13",
        "ChildItemId": "564434eb-9b0f-4856-97f4-7b8b31e50269",
        "ParentItemId": "70e75c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4227_Divyesh_Sonigra.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T08:11:34.777Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "70e75c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4227_Divyesh_Sonigra.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "70e75c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6030",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T08:11:34.7601291Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4227\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Divyesh\",\"lastName\":\"Sonigra\",\"email\":\"<EMAIL>\",\"phone\":\"07450260656\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"WF12FH\",\"CurrentlyLive\":\"GBR\",\"GraduateFrom\":\"GBR\",\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"3\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"2\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"271818\"}],\"ParentQuestionId\":3}]}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "fa7d5f5a-2e53-4e1e-86ee-6264c6431c13",
            "ChildItemId": "564434eb-9b0f-4856-97f4-7b8b31e50269",
            "ParentItemId": "70e75c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4227_Divyesh_Sonigra.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T08:11:34.777Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "70e75c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4227_Divyesh_Sonigra.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (4fdd6880)
2025-07-31T09:11:37.0797590+01:00 80225ce6-0003-b400-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4227\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Divyesh\",\"lastName\":\"Sonigra\",\"email\":\"<EMAIL>\",\"phone\":\"07450260656\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"WF12FH\",\"CurrentlyLive\":\"GBR\",\"GraduateFrom\":\"GBR\",\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"3\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"2\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"271818\"}],\"ParentQuestionId\":3}]}]}",
    "FileFieldController": [
      {
        "Id": "fa7d5f5a-2e53-4e1e-86ee-6264c6431c13",
        "ChildItemId": "564434eb-9b0f-4856-97f4-7b8b31e50269",
        "ParentItemId": "70e75c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4227_Divyesh_Sonigra.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T08:11:34.777Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "70e75c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4227_Divyesh_Sonigra.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "70e75c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6030",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T08:11:34.7601291Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4227\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Divyesh\",\"lastName\":\"Sonigra\",\"email\":\"<EMAIL>\",\"phone\":\"07450260656\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"WF12FH\",\"CurrentlyLive\":\"GBR\",\"GraduateFrom\":\"GBR\",\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"3\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"2\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"271818\"}],\"ParentQuestionId\":3}]}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "fa7d5f5a-2e53-4e1e-86ee-6264c6431c13",
            "ChildItemId": "564434eb-9b0f-4856-97f4-7b8b31e50269",
            "ParentItemId": "70e75c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4227_Divyesh_Sonigra.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T08:11:34.777Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "70e75c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4227_Divyesh_Sonigra.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (6dac357c)
2025-07-31T09:11:37.0799458+01:00 80225ce6-0003-b400-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 70e75c9e-cae7-6322-9971-ff0200979a84 (edec1b43)
2025-07-31T09:11:37.2339520+01:00 80225ce6-0003-b400-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-31T09:11:37.3155500+01:00 80225ce6-0003-b400-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-31T09:11:37.3156141+01:00 80225ce6-0003-b400-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-31T09:11:37.7502722+01:00 80225ce6-0003-b400-b63f-84710c7967bb [INF] Received HTTP response headers after 434.5741ms - 500 (f0742c1f)
2025-07-31T09:11:37.7503634+01:00 80225ce6-0003-b400-b63f-84710c7967bb [INF] End processing HTTP request after 434.8805ms - 500 (7656b38e)
2025-07-31T09:11:37.7506783+01:00 80225ce6-0003-b400-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-31T09:11:37.7507192+01:00 80225ce6-0003-b400-b63f-84710c7967bb [INF] (Webjob Application) Posted 70e75c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (06615dff)
2025-07-31T09:11:37.8419448+01:00 80225ce6-0003-b400-b63f-84710c7967bb [INF] (Webjob Application) Posted 70e75c9e-cae7-6322-9971-ff0200979a84  (4aab2cb2)
2025-07-31T09:11:41.8864247+01:00 801e8d44-0003-c200-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4227\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Divyesh\",\"lastName\":\"Sonigra\",\"email\":\"<EMAIL>\",\"phone\":\"07450260656\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"WF12FH\",\"CurrentlyLive\":\"GBR\",\"GraduateFrom\":\"GBR\",\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"3\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"2\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"271818\"}],\"ParentQuestionId\":3}]}]}",
    "FileFieldController": [
      {
        "Id": "fa7d5f5a-2e53-4e1e-86ee-6264c6431c13",
        "ChildItemId": "564434eb-9b0f-4856-97f4-7b8b31e50269",
        "ParentItemId": "70e75c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4227_Divyesh_Sonigra.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T08:11:34.777Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "70e75c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4227_Divyesh_Sonigra.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "70e75c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6030",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T08:11:34.7601291Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4227\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Divyesh\",\"lastName\":\"Sonigra\",\"email\":\"<EMAIL>\",\"phone\":\"07450260656\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"WF12FH\",\"CurrentlyLive\":\"GBR\",\"GraduateFrom\":\"GBR\",\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"3\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"2\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"271818\"}],\"ParentQuestionId\":3}]}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "fa7d5f5a-2e53-4e1e-86ee-6264c6431c13",
            "ChildItemId": "564434eb-9b0f-4856-97f4-7b8b31e50269",
            "ParentItemId": "70e75c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4227_Divyesh_Sonigra.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T08:11:34.777Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "70e75c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4227_Divyesh_Sonigra.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (4fdd6880)
2025-07-31T09:11:41.8868702+01:00 801e8d44-0003-c200-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4227\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Divyesh\",\"lastName\":\"Sonigra\",\"email\":\"<EMAIL>\",\"phone\":\"07450260656\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"WF12FH\",\"CurrentlyLive\":\"GBR\",\"GraduateFrom\":\"GBR\",\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"3\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"2\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"271818\"}],\"ParentQuestionId\":3}]}]}",
    "FileFieldController": [
      {
        "Id": "fa7d5f5a-2e53-4e1e-86ee-6264c6431c13",
        "ChildItemId": "564434eb-9b0f-4856-97f4-7b8b31e50269",
        "ParentItemId": "70e75c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4227_Divyesh_Sonigra.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T08:11:34.777Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "70e75c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4227_Divyesh_Sonigra.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "70e75c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6030",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T08:11:34.7601291Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4227\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Divyesh\",\"lastName\":\"Sonigra\",\"email\":\"<EMAIL>\",\"phone\":\"07450260656\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"WF12FH\",\"CurrentlyLive\":\"GBR\",\"GraduateFrom\":\"GBR\",\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"3\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"2\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"271818\"}],\"ParentQuestionId\":3}]}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "fa7d5f5a-2e53-4e1e-86ee-6264c6431c13",
            "ChildItemId": "564434eb-9b0f-4856-97f4-7b8b31e50269",
            "ParentItemId": "70e75c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4227_Divyesh_Sonigra.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T08:11:34.777Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "70e75c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4227_Divyesh_Sonigra.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (6dac357c)
2025-07-31T09:11:41.8870554+01:00 801e8d44-0003-c200-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 70e75c9e-cae7-6322-9971-ff0200979a84 (edec1b43)
2025-07-31T09:11:42.0228028+01:00 801e8d44-0003-c200-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-31T09:11:42.1065045+01:00 801e8d44-0003-c200-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-31T09:11:42.1065599+01:00 801e8d44-0003-c200-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-31T09:11:42.7192007+01:00 801e8d44-0003-c200-b63f-84710c7967bb [INF] Received HTTP response headers after 612.5779ms - 500 (f0742c1f)
2025-07-31T09:11:42.7192685+01:00 801e8d44-0003-c200-b63f-84710c7967bb [INF] End processing HTTP request after 612.8401ms - 500 (7656b38e)
2025-07-31T09:11:42.7196186+01:00 801e8d44-0003-c200-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-31T09:11:42.7196571+01:00 801e8d44-0003-c200-b63f-84710c7967bb [INF] (Webjob Application) Posted 70e75c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (06615dff)
2025-07-31T09:11:42.7928064+01:00 801e8d44-0003-c200-b63f-84710c7967bb [INF] (Webjob Application) Posted 70e75c9e-cae7-6322-9971-ff0200979a84  (4aab2cb2)
2025-07-31T09:11:51.8344489+01:00 80170416-0000-a500-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4227\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Divyesh\",\"lastName\":\"Sonigra\",\"email\":\"<EMAIL>\",\"phone\":\"07450260656\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"WF12FH\",\"CurrentlyLive\":\"GBR\",\"GraduateFrom\":\"GBR\",\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"3\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"2\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"271818\"}],\"ParentQuestionId\":3}]}]}",
    "FileFieldController": [
      {
        "Id": "fa7d5f5a-2e53-4e1e-86ee-6264c6431c13",
        "ChildItemId": "564434eb-9b0f-4856-97f4-7b8b31e50269",
        "ParentItemId": "70e75c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4227_Divyesh_Sonigra.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T08:11:34.777Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "70e75c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4227_Divyesh_Sonigra.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "70e75c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6030",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T08:11:34.7601291Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4227\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Divyesh\",\"lastName\":\"Sonigra\",\"email\":\"<EMAIL>\",\"phone\":\"07450260656\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"WF12FH\",\"CurrentlyLive\":\"GBR\",\"GraduateFrom\":\"GBR\",\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"3\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"2\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"271818\"}],\"ParentQuestionId\":3}]}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "fa7d5f5a-2e53-4e1e-86ee-6264c6431c13",
            "ChildItemId": "564434eb-9b0f-4856-97f4-7b8b31e50269",
            "ParentItemId": "70e75c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4227_Divyesh_Sonigra.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T08:11:34.777Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "70e75c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4227_Divyesh_Sonigra.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (4fdd6880)
2025-07-31T09:11:51.8348285+01:00 80170416-0000-a500-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4227\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Divyesh\",\"lastName\":\"Sonigra\",\"email\":\"<EMAIL>\",\"phone\":\"07450260656\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"WF12FH\",\"CurrentlyLive\":\"GBR\",\"GraduateFrom\":\"GBR\",\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"3\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"2\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"271818\"}],\"ParentQuestionId\":3}]}]}",
    "FileFieldController": [
      {
        "Id": "fa7d5f5a-2e53-4e1e-86ee-6264c6431c13",
        "ChildItemId": "564434eb-9b0f-4856-97f4-7b8b31e50269",
        "ParentItemId": "70e75c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4227_Divyesh_Sonigra.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T08:11:34.777Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "70e75c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4227_Divyesh_Sonigra.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "70e75c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6030",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T08:11:34.7601291Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4227\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Divyesh\",\"lastName\":\"Sonigra\",\"email\":\"<EMAIL>\",\"phone\":\"07450260656\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"WF12FH\",\"CurrentlyLive\":\"GBR\",\"GraduateFrom\":\"GBR\",\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"3\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"2\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"271818\"}],\"ParentQuestionId\":3}]}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "fa7d5f5a-2e53-4e1e-86ee-6264c6431c13",
            "ChildItemId": "564434eb-9b0f-4856-97f4-7b8b31e50269",
            "ParentItemId": "70e75c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4227_Divyesh_Sonigra.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T08:11:34.777Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "70e75c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4227_Divyesh_Sonigra.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (6dac357c)
2025-07-31T09:11:51.8350384+01:00 80170416-0000-a500-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 70e75c9e-cae7-6322-9971-ff0200979a84 (edec1b43)
2025-07-31T09:11:52.0207571+01:00 80170416-0000-a500-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-31T09:11:52.1091510+01:00 80170416-0000-a500-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-31T09:11:52.1091948+01:00 80170416-0000-a500-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-31T09:11:52.6610441+01:00 80170416-0000-a500-b63f-84710c7967bb [INF] Received HTTP response headers after 551.795ms - 500 (f0742c1f)
2025-07-31T09:11:52.6610873+01:00 80170416-0000-a500-b63f-84710c7967bb [INF] End processing HTTP request after 551.9923ms - 500 (7656b38e)
2025-07-31T09:11:52.6663966+01:00 80170416-0000-a500-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-31T09:11:52.6664720+01:00 80170416-0000-a500-b63f-84710c7967bb [INF] (Webjob Application) Posted 70e75c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (06615dff)
2025-07-31T09:11:52.8014996+01:00 80170416-0000-a500-b63f-84710c7967bb [INF] (Webjob Application) Posted 70e75c9e-cae7-6322-9971-ff0200979a84  (4aab2cb2)
2025-07-31T09:12:08.8471844+01:00 80225dd0-0003-b400-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4227\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Divyesh\",\"lastName\":\"Sonigra\",\"email\":\"<EMAIL>\",\"phone\":\"07450260656\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"WF12FH\",\"CurrentlyLive\":\"GBR\",\"GraduateFrom\":\"GBR\",\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"3\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"2\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"271818\"}],\"ParentQuestionId\":3}]}]}",
    "FileFieldController": [
      {
        "Id": "fa7d5f5a-2e53-4e1e-86ee-6264c6431c13",
        "ChildItemId": "564434eb-9b0f-4856-97f4-7b8b31e50269",
        "ParentItemId": "70e75c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4227_Divyesh_Sonigra.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T08:11:34.777Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "70e75c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4227_Divyesh_Sonigra.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "70e75c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6030",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T08:11:34.7601291Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4227\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Divyesh\",\"lastName\":\"Sonigra\",\"email\":\"<EMAIL>\",\"phone\":\"07450260656\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"WF12FH\",\"CurrentlyLive\":\"GBR\",\"GraduateFrom\":\"GBR\",\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"3\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"2\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"271818\"}],\"ParentQuestionId\":3}]}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "fa7d5f5a-2e53-4e1e-86ee-6264c6431c13",
            "ChildItemId": "564434eb-9b0f-4856-97f4-7b8b31e50269",
            "ParentItemId": "70e75c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4227_Divyesh_Sonigra.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T08:11:34.777Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "70e75c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4227_Divyesh_Sonigra.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (4fdd6880)
2025-07-31T09:12:08.8649305+01:00 80225dd0-0003-b400-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4227\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Divyesh\",\"lastName\":\"Sonigra\",\"email\":\"<EMAIL>\",\"phone\":\"07450260656\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"WF12FH\",\"CurrentlyLive\":\"GBR\",\"GraduateFrom\":\"GBR\",\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"3\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"2\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"271818\"}],\"ParentQuestionId\":3}]}]}",
    "FileFieldController": [
      {
        "Id": "fa7d5f5a-2e53-4e1e-86ee-6264c6431c13",
        "ChildItemId": "564434eb-9b0f-4856-97f4-7b8b31e50269",
        "ParentItemId": "70e75c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4227_Divyesh_Sonigra.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T08:11:34.777Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "70e75c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4227_Divyesh_Sonigra.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "70e75c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6030",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T08:11:34.7601291Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4227\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Divyesh\",\"lastName\":\"Sonigra\",\"email\":\"<EMAIL>\",\"phone\":\"07450260656\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"WF12FH\",\"CurrentlyLive\":\"GBR\",\"GraduateFrom\":\"GBR\",\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"3\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"2\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"271818\"}],\"ParentQuestionId\":3}]}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "fa7d5f5a-2e53-4e1e-86ee-6264c6431c13",
            "ChildItemId": "564434eb-9b0f-4856-97f4-7b8b31e50269",
            "ParentItemId": "70e75c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4227_Divyesh_Sonigra.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T08:11:34.777Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "70e75c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4227_Divyesh_Sonigra.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4227_divyesh_sonigra.docx?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (6dac357c)
2025-07-31T09:12:08.8651480+01:00 80225dd0-0003-b400-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 70e75c9e-cae7-6322-9971-ff0200979a84 (edec1b43)
2025-07-31T09:12:09.0253901+01:00 80225dd0-0003-b400-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-31T09:12:09.1504549+01:00 80225dd0-0003-b400-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-31T09:12:09.1505123+01:00 80225dd0-0003-b400-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-31T09:12:09.6516102+01:00 80225dd0-0003-b400-b63f-84710c7967bb [INF] Received HTTP response headers after 501.0388ms - 500 (f0742c1f)
2025-07-31T09:12:09.6516539+01:00 80225dd0-0003-b400-b63f-84710c7967bb [INF] End processing HTTP request after 501.2785ms - 500 (7656b38e)
2025-07-31T09:12:09.6520081+01:00 80225dd0-0003-b400-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-31T09:12:09.6520411+01:00 80225dd0-0003-b400-b63f-84710c7967bb [INF] (Webjob Application) Posted 70e75c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (06615dff)
2025-07-31T09:12:09.7814590+01:00 80225dd0-0003-b400-b63f-84710c7967bb [INF] (Webjob Application) Posted 70e75c9e-cae7-6322-9971-ff0200979a84  (4aab2cb2)
2025-07-31T09:48:17.1347913+01:00  [WRN] Using an in-memory repository. Keys will not be persisted to storage. (28e83010)
2025-07-31T09:48:17.1381948+01:00  [WRN] Neither user profile nor HKLM registry available. Using an ephemeral key repository. Protected data will be unavailable when application exits. (54f66960)
2025-07-31T09:48:17.1795535+01:00  [WRN] No XML encryptor configured. Key {1408f602-08b5-484d-b29d-9a8a6db1a9d5} may be persisted to storage in unencrypted form. (9ca7e61e)
2025-07-31T09:48:17.2619525+01:00 801a5c0e-0000-8700-b63f-84710c7967bb [WRN] Failed to determine the https port for redirect. (ca76cc21)
2025-07-31T09:48:17.6154345+01:00 801a5c0e-0000-8700-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"2549\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Mohamed\",\"lastName\":\"Widaa\",\"email\":\"<EMAIL>\",\"phone\":\"*********\",\"Phonenumbercode\":\"SAU_966\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"13337\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.43653934\"},\"jobQuestions\":[{\"QuestionId\":\"6\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"4\",\"FollowupQuestion\":\"Please select the option that best describes your GDC registration status.\\n\\nNote:  This position is only available to candidates with a GDC registration, or a valid application for registration in process, and we will only be able to take your application forward if you are able to evidence this.\",\"FollowupQuestionType\":\"Multiple Choice - Single Select\",\"followupAnswers\":[{\"AnswerId\":\"8\",\"AnswerValue\":\"8\"}],\"ParentQuestionId\":6},{\"FollowupQuestionId\":\"5\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"326764\"}],\"ParentQuestionId\":6}]}]}",
    "FileFieldController": [
      {
        "Id": "7ae57be6-d4c3-4e06-910d-461e8c55860a",
        "ChildItemId": "e6981fd9-8b9f-4ef2-80cd-a023e8b58d20",
        "ParentItemId": "7fe75c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "2549_Mohamed_Widaa.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/2549_mohamed_widaa.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T08:48:15.927Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "7fe75c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "2549_Mohamed_Widaa.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/2549_mohamed_widaa.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "7fe75c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6031",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-31T08:48:15.9103068Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"2549\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Mohamed\",\"lastName\":\"Widaa\",\"email\":\"<EMAIL>\",\"phone\":\"*********\",\"Phonenumbercode\":\"SAU_966\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"13337\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.43653934\"},\"jobQuestions\":[{\"QuestionId\":\"6\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"4\",\"FollowupQuestion\":\"Please select the option that best describes your GDC registration status.\\n\\nNote:  This position is only available to candidates with a GDC registration, or a valid application for registration in process, and we will only be able to take your application forward if you are able to evidence this.\",\"FollowupQuestionType\":\"Multiple Choice - Single Select\",\"followupAnswers\":[{\"AnswerId\":\"8\",\"AnswerValue\":\"8\"}],\"ParentQuestionId\":6},{\"FollowupQuestionId\":\"5\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"326764\"}],\"ParentQuestionId\":6}]}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "7ae57be6-d4c3-4e06-910d-461e8c55860a",
            "ChildItemId": "e6981fd9-8b9f-4ef2-80cd-a023e8b58d20",
            "ParentItemId": "7fe75c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "2549_Mohamed_Widaa.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/2549_mohamed_widaa.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T08:48:15.927Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "7fe75c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "2549_Mohamed_Widaa.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/2549_mohamed_widaa.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (c8c77b62)
2025-07-31T09:48:17.7086924+01:00 801a5c0e-0000-8700-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"2549\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Mohamed\",\"lastName\":\"Widaa\",\"email\":\"<EMAIL>\",\"phone\":\"*********\",\"Phonenumbercode\":\"SAU_966\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"13337\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.43653934\"},\"jobQuestions\":[{\"QuestionId\":\"6\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"4\",\"FollowupQuestion\":\"Please select the option that best describes your GDC registration status.\\n\\nNote:  This position is only available to candidates with a GDC registration, or a valid application for registration in process, and we will only be able to take your application forward if you are able to evidence this.\",\"FollowupQuestionType\":\"Multiple Choice - Single Select\",\"followupAnswers\":[{\"AnswerId\":\"8\",\"AnswerValue\":\"8\"}],\"ParentQuestionId\":6},{\"FollowupQuestionId\":\"5\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"326764\"}],\"ParentQuestionId\":6}]}]}",
    "FileFieldController": [
      {
        "Id": "7ae57be6-d4c3-4e06-910d-461e8c55860a",
        "ChildItemId": "e6981fd9-8b9f-4ef2-80cd-a023e8b58d20",
        "ParentItemId": "7fe75c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "2549_Mohamed_Widaa.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/2549_mohamed_widaa.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T08:48:15.927Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "7fe75c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "2549_Mohamed_Widaa.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/2549_mohamed_widaa.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "7fe75c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6031",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-31T08:48:15.9103068Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"2549\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Mohamed\",\"lastName\":\"Widaa\",\"email\":\"<EMAIL>\",\"phone\":\"*********\",\"Phonenumbercode\":\"SAU_966\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"13337\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.43653934\"},\"jobQuestions\":[{\"QuestionId\":\"6\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"4\",\"FollowupQuestion\":\"Please select the option that best describes your GDC registration status.\\n\\nNote:  This position is only available to candidates with a GDC registration, or a valid application for registration in process, and we will only be able to take your application forward if you are able to evidence this.\",\"FollowupQuestionType\":\"Multiple Choice - Single Select\",\"followupAnswers\":[{\"AnswerId\":\"8\",\"AnswerValue\":\"8\"}],\"ParentQuestionId\":6},{\"FollowupQuestionId\":\"5\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"326764\"}],\"ParentQuestionId\":6}]}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "7ae57be6-d4c3-4e06-910d-461e8c55860a",
            "ChildItemId": "e6981fd9-8b9f-4ef2-80cd-a023e8b58d20",
            "ParentItemId": "7fe75c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "2549_Mohamed_Widaa.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/2549_mohamed_widaa.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T08:48:15.927Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "7fe75c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "2549_Mohamed_Widaa.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/2549_mohamed_widaa.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (cd007685)
2025-07-31T09:48:17.7943205+01:00 801a5c0e-0000-8700-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 7fe75c9e-cae7-6322-9971-ff0200979a84 (019504fd)
2025-07-31T09:48:18.1435752+01:00 801a5c0e-0000-8700-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-31T09:48:18.4199967+01:00 801a5c0e-0000-8700-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-31T09:48:18.4213076+01:00 801a5c0e-0000-8700-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-31T09:48:19.4799015+01:00 801a5c0e-0000-8700-b63f-84710c7967bb [INF] Received HTTP response headers after 1053.0204ms - 201 (f0742c1f)
2025-07-31T09:48:19.4806621+01:00 801a5c0e-0000-8700-b63f-84710c7967bb [INF] End processing HTTP request after 1070.7717ms - 201 (7656b38e)
2025-07-31T09:48:19.4844326+01:00 801a5c0e-0000-8700-b63f-84710c7967bb [INF] (Webjob Application) Posted 7fe75c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is True (a82f7fc1)
2025-07-31T09:48:21.6569107+01:00 801a5c0e-0000-8700-b63f-84710c7967bb [INF] (Webjob Application) Posted 7fe75c9e-cae7-6322-9971-ff0200979a84  (72a4f602)
2025-07-31T09:59:13.1006377+01:00 801b4283-0001-df00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4346\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Mohamed\",\"lastName\":\"Widaa\",\"email\":\"<EMAIL>\",\"phone\":\"*********\",\"Phonenumbercode\":\"SAU_966\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"13337\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.43653934\"},\"jobQuestions\":[{\"QuestionId\":\"6\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"5\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"326764\"}],\"ParentQuestionId\":6}]}]}",
    "FileFieldController": [
      {
        "Id": "f5da4a01-3e8a-4f7f-91ba-4ebf00d96f29",
        "ChildItemId": "850650ef-b756-40d6-9c91-f7e06d77f62e",
        "ParentItemId": "4fe85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4346_Mohamed_Widaa.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4346_mohamed_widaa.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T08:59:12.863Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "4fe85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4346_Mohamed_Widaa.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4346_mohamed_widaa.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "4fe85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6032",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-31T08:59:12.84625Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4346\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Mohamed\",\"lastName\":\"Widaa\",\"email\":\"<EMAIL>\",\"phone\":\"*********\",\"Phonenumbercode\":\"SAU_966\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"13337\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.43653934\"},\"jobQuestions\":[{\"QuestionId\":\"6\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"5\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"326764\"}],\"ParentQuestionId\":6}]}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "f5da4a01-3e8a-4f7f-91ba-4ebf00d96f29",
            "ChildItemId": "850650ef-b756-40d6-9c91-f7e06d77f62e",
            "ParentItemId": "4fe85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4346_Mohamed_Widaa.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4346_mohamed_widaa.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T08:59:12.863Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "4fe85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4346_Mohamed_Widaa.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4346_mohamed_widaa.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (67b52922)
2025-07-31T09:59:13.1078531+01:00 801b4283-0001-df00-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4346\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Mohamed\",\"lastName\":\"Widaa\",\"email\":\"<EMAIL>\",\"phone\":\"*********\",\"Phonenumbercode\":\"SAU_966\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"13337\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.43653934\"},\"jobQuestions\":[{\"QuestionId\":\"6\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"5\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"326764\"}],\"ParentQuestionId\":6}]}]}",
    "FileFieldController": [
      {
        "Id": "f5da4a01-3e8a-4f7f-91ba-4ebf00d96f29",
        "ChildItemId": "850650ef-b756-40d6-9c91-f7e06d77f62e",
        "ParentItemId": "4fe85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4346_Mohamed_Widaa.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4346_mohamed_widaa.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T08:59:12.863Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "4fe85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4346_Mohamed_Widaa.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4346_mohamed_widaa.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "4fe85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6032",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-31T08:59:12.84625Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4346\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Mohamed\",\"lastName\":\"Widaa\",\"email\":\"<EMAIL>\",\"phone\":\"*********\",\"Phonenumbercode\":\"SAU_966\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"13337\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.43653934\"},\"jobQuestions\":[{\"QuestionId\":\"6\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"5\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"326764\"}],\"ParentQuestionId\":6}]}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "f5da4a01-3e8a-4f7f-91ba-4ebf00d96f29",
            "ChildItemId": "850650ef-b756-40d6-9c91-f7e06d77f62e",
            "ParentItemId": "4fe85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4346_Mohamed_Widaa.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4346_mohamed_widaa.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T08:59:12.863Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "4fe85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4346_Mohamed_Widaa.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4346_mohamed_widaa.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (23f91a54)
2025-07-31T09:59:13.1081005+01:00 801b4283-0001-df00-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 4fe85c9e-cae7-6322-9971-ff0200979a84 (19916337)
2025-07-31T09:59:13.2831244+01:00 801b4283-0001-df00-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-31T09:59:13.4911885+01:00 801b4283-0001-df00-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-31T09:59:13.4912749+01:00 801b4283-0001-df00-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-31T09:59:14.1643331+01:00 801b4283-0001-df00-b63f-84710c7967bb [INF] Received HTTP response headers after 672.9417ms - 201 (f0742c1f)
2025-07-31T09:59:14.1644192+01:00 801b4283-0001-df00-b63f-84710c7967bb [INF] End processing HTTP request after 673.3065ms - 201 (7656b38e)
2025-07-31T09:59:14.1645395+01:00 801b4283-0001-df00-b63f-84710c7967bb [INF] (Webjob Application) Posted 4fe85c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is True (aecc5c2f)
2025-07-31T09:59:14.2791717+01:00 801b4283-0001-df00-b63f-84710c7967bb [INF] (Webjob Application) Posted 4fe85c9e-cae7-6322-9971-ff0200979a84  (e4981e5c)
2025-07-31T10:01:50.6479353+01:00 8012e69b-0002-8b00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4346\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Mohamed\",\"lastName\":\"Widaa\",\"email\":\"<EMAIL>\",\"phone\":\"*********\",\"Phonenumbercode\":\"SAU_966\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"13337\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.43653934\"},\"jobQuestions\":[{\"QuestionId\":\"6\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"5\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"326764\"}],\"ParentQuestionId\":6}]}]}",
    "FileFieldController": [
      {
        "Id": "43e4fea8-09b2-41cb-9be9-6ad252d8e6c7",
        "ChildItemId": "850650ef-b756-40d6-9c91-f7e06d77f62e",
        "ParentItemId": "f8e75c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4346_Mohamed_Widaa.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4346_mohamed_widaa.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:01:50.523Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "f8e75c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4346_Mohamed_Widaa.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4346_mohamed_widaa.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "f8e75c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6033",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T09:01:50.5074187Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4346\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Mohamed\",\"lastName\":\"Widaa\",\"email\":\"<EMAIL>\",\"phone\":\"*********\",\"Phonenumbercode\":\"SAU_966\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"13337\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.43653934\"},\"jobQuestions\":[{\"QuestionId\":\"6\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"5\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"326764\"}],\"ParentQuestionId\":6}]}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "43e4fea8-09b2-41cb-9be9-6ad252d8e6c7",
            "ChildItemId": "850650ef-b756-40d6-9c91-f7e06d77f62e",
            "ParentItemId": "f8e75c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4346_Mohamed_Widaa.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4346_mohamed_widaa.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:01:50.523Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "f8e75c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4346_Mohamed_Widaa.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4346_mohamed_widaa.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (46f4a261)
2025-07-31T10:01:50.6481759+01:00 8012e69b-0002-8b00-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4346\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Mohamed\",\"lastName\":\"Widaa\",\"email\":\"<EMAIL>\",\"phone\":\"*********\",\"Phonenumbercode\":\"SAU_966\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"13337\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.43653934\"},\"jobQuestions\":[{\"QuestionId\":\"6\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"5\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"326764\"}],\"ParentQuestionId\":6}]}]}",
    "FileFieldController": [
      {
        "Id": "43e4fea8-09b2-41cb-9be9-6ad252d8e6c7",
        "ChildItemId": "850650ef-b756-40d6-9c91-f7e06d77f62e",
        "ParentItemId": "f8e75c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4346_Mohamed_Widaa.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4346_mohamed_widaa.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:01:50.523Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "f8e75c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4346_Mohamed_Widaa.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4346_mohamed_widaa.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "f8e75c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6033",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T09:01:50.5074187Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4346\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Mohamed\",\"lastName\":\"Widaa\",\"email\":\"<EMAIL>\",\"phone\":\"*********\",\"Phonenumbercode\":\"SAU_966\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"13337\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.43653934\"},\"jobQuestions\":[{\"QuestionId\":\"6\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"5\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"326764\"}],\"ParentQuestionId\":6}]}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "43e4fea8-09b2-41cb-9be9-6ad252d8e6c7",
            "ChildItemId": "850650ef-b756-40d6-9c91-f7e06d77f62e",
            "ParentItemId": "f8e75c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4346_Mohamed_Widaa.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4346_mohamed_widaa.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:01:50.523Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "f8e75c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4346_Mohamed_Widaa.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4346_mohamed_widaa.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (1e0f55f0)
2025-07-31T10:01:50.6483251+01:00 8012e69b-0002-8b00-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId f8e75c9e-cae7-6322-9971-ff0200979a84 (d3eafb42)
2025-07-31T10:01:50.8906072+01:00 8012e69b-0002-8b00-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-31T10:01:51.1822871+01:00 8012e69b-0002-8b00-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-31T10:01:51.1823313+01:00 8012e69b-0002-8b00-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-31T10:01:51.6244976+01:00 8012e69b-0002-8b00-b63f-84710c7967bb [INF] Received HTTP response headers after 442.1132ms - 201 (f0742c1f)
2025-07-31T10:01:51.6245616+01:00 8012e69b-0002-8b00-b63f-84710c7967bb [INF] End processing HTTP request after 442.334ms - 201 (7656b38e)
2025-07-31T10:01:51.6246370+01:00 8012e69b-0002-8b00-b63f-84710c7967bb [INF] (Webjob Application) Posted f8e75c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is True (52398a81)
2025-07-31T10:01:51.7367391+01:00 8012e69b-0002-8b00-b63f-84710c7967bb [INF] (Webjob Application) Posted f8e75c9e-cae7-6322-9971-ff0200979a84  (d68ddfb9)
2025-07-31T10:55:07.8774374+01:00  [WRN] Using an in-memory repository. Keys will not be persisted to storage. (28e83010)
2025-07-31T10:55:07.8808149+01:00  [WRN] Neither user profile nor HKLM registry available. Using an ephemeral key repository. Protected data will be unavailable when application exits. (54f66960)
2025-07-31T10:55:07.9237854+01:00  [WRN] No XML encryptor configured. Key {c84ae6c3-eeb7-4228-ad5b-965c6b58603e} may be persisted to storage in unencrypted form. (9ca7e61e)
2025-07-31T10:55:08.0081763+01:00 80228f88-0003-b400-b63f-84710c7967bb [WRN] Failed to determine the https port for redirect. (ca76cc21)
2025-07-31T10:55:08.3666470+01:00 80228f88-0003-b400-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"9\",\"AnswerValue\":\"9\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "ba66ab0e-67df-4ca9-98cc-ab6402bd10ce",
        "ChildItemId": "81ecb38c-b69d-40e4-8301-682fff24f9c4",
        "ParentItemId": "74e85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3007_Sarah _Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:55:06.713Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "74e85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3007_Sarah _Allen.07.2025.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "74e85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6034",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T09:55:06.6968048Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"9\",\"AnswerValue\":\"9\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "ba66ab0e-67df-4ca9-98cc-ab6402bd10ce",
            "ChildItemId": "81ecb38c-b69d-40e4-8301-682fff24f9c4",
            "ParentItemId": "74e85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3007_Sarah _Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:55:06.713Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "74e85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3007_Sarah _Allen.07.2025.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (d3d2b85f)
2025-07-31T10:55:08.4579526+01:00 80228f88-0003-b400-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"9\",\"AnswerValue\":\"9\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "ba66ab0e-67df-4ca9-98cc-ab6402bd10ce",
        "ChildItemId": "81ecb38c-b69d-40e4-8301-682fff24f9c4",
        "ParentItemId": "74e85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3007_Sarah _Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:55:06.713Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "74e85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3007_Sarah _Allen.07.2025.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "74e85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6034",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T09:55:06.6968048Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"9\",\"AnswerValue\":\"9\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "ba66ab0e-67df-4ca9-98cc-ab6402bd10ce",
            "ChildItemId": "81ecb38c-b69d-40e4-8301-682fff24f9c4",
            "ParentItemId": "74e85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3007_Sarah _Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:55:06.713Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "74e85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3007_Sarah _Allen.07.2025.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (47e7b0ae)
2025-07-31T10:55:08.5443956+01:00 80228f88-0003-b400-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 74e85c9e-cae7-6322-9971-ff0200979a84 (0385b676)
2025-07-31T10:55:08.8810171+01:00 80228f88-0003-b400-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-31T10:55:09.3998822+01:00 80228f88-0003-b400-b63f-84710c7967bb [ERR] Error processing CV: Response status code does not indicate success: 404 (Not Found). (db13d13f)
2025-07-31T10:55:09.4003164+01:00 80228f88-0003-b400-b63f-84710c7967bb [INF] (Webjob Application) Posted 74e85c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (75985297)
2025-07-31T10:55:12.0494011+01:00 80228f88-0003-b400-b63f-84710c7967bb [INF] (Webjob Application) Posted 74e85c9e-cae7-6322-9971-ff0200979a84  (16a703e4)
2025-07-31T10:55:13.1189391+01:00 800b7b22-0001-a600-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"9\",\"AnswerValue\":\"9\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "ba66ab0e-67df-4ca9-98cc-ab6402bd10ce",
        "ChildItemId": "81ecb38c-b69d-40e4-8301-682fff24f9c4",
        "ParentItemId": "74e85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3007_Sarah _Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:55:06.713Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "74e85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3007_Sarah _Allen.07.2025.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "74e85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6034",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T09:55:06.6968048Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"9\",\"AnswerValue\":\"9\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "ba66ab0e-67df-4ca9-98cc-ab6402bd10ce",
            "ChildItemId": "81ecb38c-b69d-40e4-8301-682fff24f9c4",
            "ParentItemId": "74e85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3007_Sarah _Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:55:06.713Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "74e85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3007_Sarah _Allen.07.2025.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (d3d2b85f)
2025-07-31T10:55:13.1260703+01:00 800b7b22-0001-a600-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"9\",\"AnswerValue\":\"9\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "ba66ab0e-67df-4ca9-98cc-ab6402bd10ce",
        "ChildItemId": "81ecb38c-b69d-40e4-8301-682fff24f9c4",
        "ParentItemId": "74e85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3007_Sarah _Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:55:06.713Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "74e85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3007_Sarah _Allen.07.2025.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "74e85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6034",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T09:55:06.6968048Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"9\",\"AnswerValue\":\"9\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "ba66ab0e-67df-4ca9-98cc-ab6402bd10ce",
            "ChildItemId": "81ecb38c-b69d-40e4-8301-682fff24f9c4",
            "ParentItemId": "74e85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3007_Sarah _Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:55:06.713Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "74e85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3007_Sarah _Allen.07.2025.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (47e7b0ae)
2025-07-31T10:55:13.1263441+01:00 800b7b22-0001-a600-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 74e85c9e-cae7-6322-9971-ff0200979a84 (0385b676)
2025-07-31T10:55:13.4606080+01:00 800b7b22-0001-a600-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-31T10:55:13.8179886+01:00 800b7b22-0001-a600-b63f-84710c7967bb [ERR] Error processing CV: Response status code does not indicate success: 404 (Not Found). (db13d13f)
2025-07-31T10:55:13.8180705+01:00 800b7b22-0001-a600-b63f-84710c7967bb [INF] (Webjob Application) Posted 74e85c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (75985297)
2025-07-31T10:55:13.9433872+01:00 800b7b22-0001-a600-b63f-84710c7967bb [INF] (Webjob Application) Posted 74e85c9e-cae7-6322-9971-ff0200979a84  (16a703e4)
2025-07-31T10:55:18.0040531+01:00 800afbd4-0003-7800-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"9\",\"AnswerValue\":\"9\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "ba66ab0e-67df-4ca9-98cc-ab6402bd10ce",
        "ChildItemId": "81ecb38c-b69d-40e4-8301-682fff24f9c4",
        "ParentItemId": "74e85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3007_Sarah _Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:55:06.713Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "74e85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3007_Sarah _Allen.07.2025.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "74e85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6034",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T09:55:06.6968048Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"9\",\"AnswerValue\":\"9\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "ba66ab0e-67df-4ca9-98cc-ab6402bd10ce",
            "ChildItemId": "81ecb38c-b69d-40e4-8301-682fff24f9c4",
            "ParentItemId": "74e85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3007_Sarah _Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:55:06.713Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "74e85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3007_Sarah _Allen.07.2025.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (d3d2b85f)
2025-07-31T10:55:18.0044304+01:00 800afbd4-0003-7800-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"9\",\"AnswerValue\":\"9\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "ba66ab0e-67df-4ca9-98cc-ab6402bd10ce",
        "ChildItemId": "81ecb38c-b69d-40e4-8301-682fff24f9c4",
        "ParentItemId": "74e85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3007_Sarah _Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:55:06.713Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "74e85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3007_Sarah _Allen.07.2025.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "74e85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6034",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T09:55:06.6968048Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"9\",\"AnswerValue\":\"9\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "ba66ab0e-67df-4ca9-98cc-ab6402bd10ce",
            "ChildItemId": "81ecb38c-b69d-40e4-8301-682fff24f9c4",
            "ParentItemId": "74e85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3007_Sarah _Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:55:06.713Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "74e85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3007_Sarah _Allen.07.2025.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (47e7b0ae)
2025-07-31T10:55:18.0046135+01:00 800afbd4-0003-7800-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 74e85c9e-cae7-6322-9971-ff0200979a84 (0385b676)
2025-07-31T10:55:18.1466353+01:00 800afbd4-0003-7800-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-31T10:55:18.3641533+01:00 800afbd4-0003-7800-b63f-84710c7967bb [ERR] Error processing CV: Response status code does not indicate success: 404 (Not Found). (db13d13f)
2025-07-31T10:55:18.3643046+01:00 800afbd4-0003-7800-b63f-84710c7967bb [INF] (Webjob Application) Posted 74e85c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (75985297)
2025-07-31T10:55:18.5072207+01:00 800afbd4-0003-7800-b63f-84710c7967bb [INF] (Webjob Application) Posted 74e85c9e-cae7-6322-9971-ff0200979a84  (16a703e4)
2025-07-31T10:55:27.5436111+01:00 800afc0e-0003-7800-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"9\",\"AnswerValue\":\"9\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "ba66ab0e-67df-4ca9-98cc-ab6402bd10ce",
        "ChildItemId": "81ecb38c-b69d-40e4-8301-682fff24f9c4",
        "ParentItemId": "74e85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3007_Sarah _Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:55:06.713Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "74e85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3007_Sarah _Allen.07.2025.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "74e85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6034",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T09:55:06.6968048Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"9\",\"AnswerValue\":\"9\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "ba66ab0e-67df-4ca9-98cc-ab6402bd10ce",
            "ChildItemId": "81ecb38c-b69d-40e4-8301-682fff24f9c4",
            "ParentItemId": "74e85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3007_Sarah _Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:55:06.713Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "74e85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3007_Sarah _Allen.07.2025.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (d3d2b85f)
2025-07-31T10:55:27.5438423+01:00 800afc0e-0003-7800-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"9\",\"AnswerValue\":\"9\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "ba66ab0e-67df-4ca9-98cc-ab6402bd10ce",
        "ChildItemId": "81ecb38c-b69d-40e4-8301-682fff24f9c4",
        "ParentItemId": "74e85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3007_Sarah _Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:55:06.713Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "74e85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3007_Sarah _Allen.07.2025.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "74e85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6034",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T09:55:06.6968048Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"9\",\"AnswerValue\":\"9\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "ba66ab0e-67df-4ca9-98cc-ab6402bd10ce",
            "ChildItemId": "81ecb38c-b69d-40e4-8301-682fff24f9c4",
            "ParentItemId": "74e85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3007_Sarah _Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:55:06.713Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "74e85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3007_Sarah _Allen.07.2025.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (47e7b0ae)
2025-07-31T10:55:27.5439482+01:00 800afc0e-0003-7800-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 74e85c9e-cae7-6322-9971-ff0200979a84 (0385b676)
2025-07-31T10:55:27.7661470+01:00 800afc0e-0003-7800-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-31T10:55:28.0145543+01:00 800afc0e-0003-7800-b63f-84710c7967bb [ERR] Error processing CV: Response status code does not indicate success: 404 (Not Found). (db13d13f)
2025-07-31T10:55:28.0146406+01:00 800afc0e-0003-7800-b63f-84710c7967bb [INF] (Webjob Application) Posted 74e85c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (75985297)
2025-07-31T10:55:28.0820322+01:00 800afc0e-0003-7800-b63f-84710c7967bb [INF] (Webjob Application) Posted 74e85c9e-cae7-6322-9971-ff0200979a84  (16a703e4)
2025-07-31T10:55:44.1186708+01:00 80079d18-0003-db00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"9\",\"AnswerValue\":\"9\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "ba66ab0e-67df-4ca9-98cc-ab6402bd10ce",
        "ChildItemId": "81ecb38c-b69d-40e4-8301-682fff24f9c4",
        "ParentItemId": "74e85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3007_Sarah _Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:55:06.713Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "74e85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3007_Sarah _Allen.07.2025.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "74e85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6034",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T09:55:06.6968048Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"9\",\"AnswerValue\":\"9\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "ba66ab0e-67df-4ca9-98cc-ab6402bd10ce",
            "ChildItemId": "81ecb38c-b69d-40e4-8301-682fff24f9c4",
            "ParentItemId": "74e85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3007_Sarah _Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:55:06.713Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "74e85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3007_Sarah _Allen.07.2025.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (d3d2b85f)
2025-07-31T10:55:44.1189639+01:00 80079d18-0003-db00-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"9\",\"AnswerValue\":\"9\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "ba66ab0e-67df-4ca9-98cc-ab6402bd10ce",
        "ChildItemId": "81ecb38c-b69d-40e4-8301-682fff24f9c4",
        "ParentItemId": "74e85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3007_Sarah _Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:55:06.713Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "74e85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3007_Sarah _Allen.07.2025.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "74e85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6034",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T09:55:06.6968048Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"9\",\"AnswerValue\":\"9\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "ba66ab0e-67df-4ca9-98cc-ab6402bd10ce",
            "ChildItemId": "81ecb38c-b69d-40e4-8301-682fff24f9c4",
            "ParentItemId": "74e85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3007_Sarah _Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:55:06.713Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "74e85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3007_Sarah _Allen.07.2025.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah-_allen.07.2025.docx?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (47e7b0ae)
2025-07-31T10:55:44.1190766+01:00 80079d18-0003-db00-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 74e85c9e-cae7-6322-9971-ff0200979a84 (0385b676)
2025-07-31T10:55:44.2824052+01:00 80079d18-0003-db00-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-31T10:55:44.6155327+01:00 80079d18-0003-db00-b63f-84710c7967bb [ERR] Error processing CV: Response status code does not indicate success: 404 (Not Found). (db13d13f)
2025-07-31T10:55:44.6156147+01:00 80079d18-0003-db00-b63f-84710c7967bb [INF] (Webjob Application) Posted 74e85c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (75985297)
2025-07-31T10:55:44.6806229+01:00 80079d18-0003-db00-b63f-84710c7967bb [INF] (Webjob Application) Posted 74e85c9e-cae7-6322-9971-ff0200979a84  (16a703e4)
2025-07-31T10:55:56.2868674+01:00 80112f94-0000-a200-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "1cd86f93-5537-45cd-a1f9-eec34de42801",
        "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
        "ParentItemId": "0be85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:55:56.173Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "0be85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3007_Sarah_Allen.07.2025.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "0be85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6035",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T09:55:56.1581386Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "1cd86f93-5537-45cd-a1f9-eec34de42801",
            "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
            "ParentItemId": "0be85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:55:56.173Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "0be85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3007_Sarah_Allen.07.2025.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (55187b82)
2025-07-31T10:55:56.2871763+01:00 80112f94-0000-a200-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "1cd86f93-5537-45cd-a1f9-eec34de42801",
        "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
        "ParentItemId": "0be85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:55:56.173Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "0be85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3007_Sarah_Allen.07.2025.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "0be85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6035",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T09:55:56.1581386Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "1cd86f93-5537-45cd-a1f9-eec34de42801",
            "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
            "ParentItemId": "0be85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:55:56.173Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "0be85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3007_Sarah_Allen.07.2025.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (2be10f5c)
2025-07-31T10:55:56.2873343+01:00 80112f94-0000-a200-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 0be85c9e-cae7-6322-9971-ff0200979a84 (f9527335)
2025-07-31T10:55:56.4922751+01:00 80112f94-0000-a200-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-31T10:55:57.0055280+01:00 80112f94-0000-a200-b63f-84710c7967bb [ERR] Error processing CV: Response status code does not indicate success: 404 (Not Found). (db13d13f)
2025-07-31T10:55:57.0056050+01:00 80112f94-0000-a200-b63f-84710c7967bb [INF] (Webjob Application) Posted 0be85c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (ae78bf9d)
2025-07-31T10:55:57.0895607+01:00 80112f94-0000-a200-b63f-84710c7967bb [INF] (Webjob Application) Posted 0be85c9e-cae7-6322-9971-ff0200979a84  (dc15baae)
2025-07-31T10:55:58.1119162+01:00 800afd08-0003-7800-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "1cd86f93-5537-45cd-a1f9-eec34de42801",
        "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
        "ParentItemId": "0be85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:55:56.173Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "0be85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3007_Sarah_Allen.07.2025.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "0be85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6035",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T09:55:56.1581386Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "1cd86f93-5537-45cd-a1f9-eec34de42801",
            "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
            "ParentItemId": "0be85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:55:56.173Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "0be85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3007_Sarah_Allen.07.2025.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (55187b82)
2025-07-31T10:55:58.1122090+01:00 800afd08-0003-7800-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "1cd86f93-5537-45cd-a1f9-eec34de42801",
        "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
        "ParentItemId": "0be85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:55:56.173Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "0be85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3007_Sarah_Allen.07.2025.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "0be85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6035",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T09:55:56.1581386Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "1cd86f93-5537-45cd-a1f9-eec34de42801",
            "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
            "ParentItemId": "0be85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:55:56.173Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "0be85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3007_Sarah_Allen.07.2025.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (2be10f5c)
2025-07-31T10:55:58.1123288+01:00 800afd08-0003-7800-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 0be85c9e-cae7-6322-9971-ff0200979a84 (f9527335)
2025-07-31T10:55:58.2769523+01:00 800afd08-0003-7800-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-31T10:55:58.7318273+01:00 800afd08-0003-7800-b63f-84710c7967bb [ERR] Error processing CV: Response status code does not indicate success: 404 (Not Found). (db13d13f)
2025-07-31T10:55:58.7318963+01:00 800afd08-0003-7800-b63f-84710c7967bb [INF] (Webjob Application) Posted 0be85c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (ae78bf9d)
2025-07-31T10:55:58.8192884+01:00 800afd08-0003-7800-b63f-84710c7967bb [INF] (Webjob Application) Posted 0be85c9e-cae7-6322-9971-ff0200979a84  (dc15baae)
2025-07-31T10:56:02.8793839+01:00 800b7c3c-0001-a600-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "1cd86f93-5537-45cd-a1f9-eec34de42801",
        "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
        "ParentItemId": "0be85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:55:56.173Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "0be85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3007_Sarah_Allen.07.2025.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "0be85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6035",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T09:55:56.1581386Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "1cd86f93-5537-45cd-a1f9-eec34de42801",
            "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
            "ParentItemId": "0be85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:55:56.173Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "0be85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3007_Sarah_Allen.07.2025.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (55187b82)
2025-07-31T10:56:02.8795958+01:00 800b7c3c-0001-a600-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "1cd86f93-5537-45cd-a1f9-eec34de42801",
        "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
        "ParentItemId": "0be85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:55:56.173Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "0be85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3007_Sarah_Allen.07.2025.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "0be85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6035",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T09:55:56.1581386Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "1cd86f93-5537-45cd-a1f9-eec34de42801",
            "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
            "ParentItemId": "0be85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:55:56.173Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "0be85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3007_Sarah_Allen.07.2025.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (2be10f5c)
2025-07-31T10:56:02.8796966+01:00 800b7c3c-0001-a600-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 0be85c9e-cae7-6322-9971-ff0200979a84 (f9527335)
2025-07-31T10:56:03.1330481+01:00 800b7c3c-0001-a600-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-31T10:56:03.7135075+01:00 800b7c3c-0001-a600-b63f-84710c7967bb [ERR] Error processing CV: Response status code does not indicate success: 404 (Not Found). (db13d13f)
2025-07-31T10:56:03.7135700+01:00 800b7c3c-0001-a600-b63f-84710c7967bb [INF] (Webjob Application) Posted 0be85c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (ae78bf9d)
2025-07-31T10:56:03.7817940+01:00 800b7c3c-0001-a600-b63f-84710c7967bb [INF] (Webjob Application) Posted 0be85c9e-cae7-6322-9971-ff0200979a84  (dc15baae)
2025-07-31T10:56:06.4020728+01:00 801742fd-0002-bf00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "d9bb8029-491a-4056-9992-c5fd9a992d09",
        "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
        "ParentItemId": "76e85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:56:06.25Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "76e85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3007_Sarah_Allen.07.2025.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "76e85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6036",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-31T09:56:06.2495669Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "d9bb8029-491a-4056-9992-c5fd9a992d09",
            "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
            "ParentItemId": "76e85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:56:06.25Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "76e85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3007_Sarah_Allen.07.2025.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (7731f6af)
2025-07-31T10:56:06.4023147+01:00 801742fd-0002-bf00-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "d9bb8029-491a-4056-9992-c5fd9a992d09",
        "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
        "ParentItemId": "76e85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:56:06.25Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "76e85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3007_Sarah_Allen.07.2025.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "76e85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6036",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-31T09:56:06.2495669Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "d9bb8029-491a-4056-9992-c5fd9a992d09",
            "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
            "ParentItemId": "76e85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:56:06.25Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "76e85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3007_Sarah_Allen.07.2025.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (62615477)
2025-07-31T10:56:06.4024826+01:00 801742fd-0002-bf00-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 76e85c9e-cae7-6322-9971-ff0200979a84 (d79cc654)
2025-07-31T10:56:06.5710993+01:00 801742fd-0002-bf00-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-31T10:56:06.9409421+01:00 801742fd-0002-bf00-b63f-84710c7967bb [ERR] Error processing CV: Response status code does not indicate success: 404 (Not Found). (db13d13f)
2025-07-31T10:56:06.9410291+01:00 801742fd-0002-bf00-b63f-84710c7967bb [INF] (Webjob Application) Posted 76e85c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (f11ae414)
2025-07-31T10:56:07.0389821+01:00 801742fd-0002-bf00-b63f-84710c7967bb [INF] (Webjob Application) Posted 76e85c9e-cae7-6322-9971-ff0200979a84  (23211e27)
2025-07-31T10:56:08.0759743+01:00 801e5320-0003-9c00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "d9bb8029-491a-4056-9992-c5fd9a992d09",
        "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
        "ParentItemId": "76e85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:56:06.25Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "76e85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3007_Sarah_Allen.07.2025.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "76e85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6036",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-31T09:56:06.2495669Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "d9bb8029-491a-4056-9992-c5fd9a992d09",
            "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
            "ParentItemId": "76e85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:56:06.25Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "76e85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3007_Sarah_Allen.07.2025.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (7731f6af)
2025-07-31T10:56:08.0761454+01:00 801e5320-0003-9c00-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "d9bb8029-491a-4056-9992-c5fd9a992d09",
        "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
        "ParentItemId": "76e85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:56:06.25Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "76e85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3007_Sarah_Allen.07.2025.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "76e85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6036",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-31T09:56:06.2495669Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "d9bb8029-491a-4056-9992-c5fd9a992d09",
            "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
            "ParentItemId": "76e85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:56:06.25Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "76e85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3007_Sarah_Allen.07.2025.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (62615477)
2025-07-31T10:56:08.0762404+01:00 801e5320-0003-9c00-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 76e85c9e-cae7-6322-9971-ff0200979a84 (d79cc654)
2025-07-31T10:56:08.2475697+01:00 801e5320-0003-9c00-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-31T10:56:08.5477706+01:00 801e5320-0003-9c00-b63f-84710c7967bb [ERR] Error processing CV: Response status code does not indicate success: 404 (Not Found). (db13d13f)
2025-07-31T10:56:08.5478520+01:00 801e5320-0003-9c00-b63f-84710c7967bb [INF] (Webjob Application) Posted 76e85c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (f11ae414)
2025-07-31T10:56:08.6224806+01:00 801e5320-0003-9c00-b63f-84710c7967bb [INF] (Webjob Application) Posted 76e85c9e-cae7-6322-9971-ff0200979a84  (23211e27)
2025-07-31T10:56:12.6653602+01:00 800d3112-0001-bd00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "d9bb8029-491a-4056-9992-c5fd9a992d09",
        "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
        "ParentItemId": "76e85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:56:06.25Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "76e85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3007_Sarah_Allen.07.2025.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "76e85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6036",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-31T09:56:06.2495669Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "d9bb8029-491a-4056-9992-c5fd9a992d09",
            "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
            "ParentItemId": "76e85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:56:06.25Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "76e85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3007_Sarah_Allen.07.2025.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (7731f6af)
2025-07-31T10:56:12.6657457+01:00 800d3112-0001-bd00-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "d9bb8029-491a-4056-9992-c5fd9a992d09",
        "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
        "ParentItemId": "76e85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:56:06.25Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "76e85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3007_Sarah_Allen.07.2025.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "76e85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6036",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-31T09:56:06.2495669Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "d9bb8029-491a-4056-9992-c5fd9a992d09",
            "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
            "ParentItemId": "76e85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:56:06.25Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "76e85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3007_Sarah_Allen.07.2025.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (62615477)
2025-07-31T10:56:12.6660237+01:00 800d3112-0001-bd00-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 76e85c9e-cae7-6322-9971-ff0200979a84 (d79cc654)
2025-07-31T10:56:12.8098202+01:00 802290e2-0003-b400-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "1cd86f93-5537-45cd-a1f9-eec34de42801",
        "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
        "ParentItemId": "0be85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:55:56.173Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "0be85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3007_Sarah_Allen.07.2025.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "0be85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6035",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T09:55:56.1581386Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "1cd86f93-5537-45cd-a1f9-eec34de42801",
            "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
            "ParentItemId": "0be85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:55:56.173Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "0be85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3007_Sarah_Allen.07.2025.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (55187b82)
2025-07-31T10:56:12.8099972+01:00 802290e2-0003-b400-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "1cd86f93-5537-45cd-a1f9-eec34de42801",
        "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
        "ParentItemId": "0be85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:55:56.173Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "0be85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3007_Sarah_Allen.07.2025.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "0be85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6035",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T09:55:56.1581386Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "1cd86f93-5537-45cd-a1f9-eec34de42801",
            "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
            "ParentItemId": "0be85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:55:56.173Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "0be85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3007_Sarah_Allen.07.2025.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (2be10f5c)
2025-07-31T10:56:12.8100894+01:00 802290e2-0003-b400-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 0be85c9e-cae7-6322-9971-ff0200979a84 (f9527335)
2025-07-31T10:56:12.8406092+01:00 800d3112-0001-bd00-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-31T10:56:12.9939909+01:00 802290e2-0003-b400-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-31T10:56:13.1401376+01:00 800d3112-0001-bd00-b63f-84710c7967bb [ERR] Error processing CV: Response status code does not indicate success: 404 (Not Found). (db13d13f)
2025-07-31T10:56:13.1402000+01:00 800d3112-0001-bd00-b63f-84710c7967bb [INF] (Webjob Application) Posted 76e85c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (f11ae414)
2025-07-31T10:56:13.2490368+01:00 800d3112-0001-bd00-b63f-84710c7967bb [INF] (Webjob Application) Posted 76e85c9e-cae7-6322-9971-ff0200979a84  (23211e27)
2025-07-31T10:56:13.3973884+01:00 802290e2-0003-b400-b63f-84710c7967bb [ERR] Error processing CV: Response status code does not indicate success: 404 (Not Found). (db13d13f)
2025-07-31T10:56:13.3974555+01:00 802290e2-0003-b400-b63f-84710c7967bb [INF] (Webjob Application) Posted 0be85c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (ae78bf9d)
2025-07-31T10:56:13.4792596+01:00 802290e2-0003-b400-b63f-84710c7967bb [INF] (Webjob Application) Posted 0be85c9e-cae7-6322-9971-ff0200979a84  (dc15baae)
2025-07-31T10:56:22.3355558+01:00 80124001-0002-9d00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "d9bb8029-491a-4056-9992-c5fd9a992d09",
        "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
        "ParentItemId": "76e85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:56:06.25Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "76e85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3007_Sarah_Allen.07.2025.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "76e85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6036",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-31T09:56:06.2495669Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "d9bb8029-491a-4056-9992-c5fd9a992d09",
            "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
            "ParentItemId": "76e85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:56:06.25Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "76e85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3007_Sarah_Allen.07.2025.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (7731f6af)
2025-07-31T10:56:22.3357409+01:00 80124001-0002-9d00-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "d9bb8029-491a-4056-9992-c5fd9a992d09",
        "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
        "ParentItemId": "76e85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:56:06.25Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "76e85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3007_Sarah_Allen.07.2025.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "76e85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6036",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-31T09:56:06.2495669Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "d9bb8029-491a-4056-9992-c5fd9a992d09",
            "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
            "ParentItemId": "76e85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:56:06.25Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "76e85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3007_Sarah_Allen.07.2025.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (62615477)
2025-07-31T10:56:22.3358405+01:00 80124001-0002-9d00-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 76e85c9e-cae7-6322-9971-ff0200979a84 (d79cc654)
2025-07-31T10:56:22.5166928+01:00 80124001-0002-9d00-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-31T10:56:22.7199232+01:00 80124001-0002-9d00-b63f-84710c7967bb [ERR] Error processing CV: Response status code does not indicate success: 404 (Not Found). (db13d13f)
2025-07-31T10:56:22.7200010+01:00 80124001-0002-9d00-b63f-84710c7967bb [INF] (Webjob Application) Posted 76e85c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (f11ae414)
2025-07-31T10:56:22.8252186+01:00 80124001-0002-9d00-b63f-84710c7967bb [INF] (Webjob Application) Posted 76e85c9e-cae7-6322-9971-ff0200979a84  (23211e27)
2025-07-31T10:56:29.5419319+01:00 80174351-0002-bf00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "1cd86f93-5537-45cd-a1f9-eec34de42801",
        "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
        "ParentItemId": "0be85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:55:56.173Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "0be85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3007_Sarah_Allen.07.2025.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "0be85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6035",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T09:55:56.1581386Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "1cd86f93-5537-45cd-a1f9-eec34de42801",
            "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
            "ParentItemId": "0be85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:55:56.173Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "0be85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3007_Sarah_Allen.07.2025.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (55187b82)
2025-07-31T10:56:29.5421853+01:00 80174351-0002-bf00-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "1cd86f93-5537-45cd-a1f9-eec34de42801",
        "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
        "ParentItemId": "0be85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:55:56.173Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "0be85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3007_Sarah_Allen.07.2025.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "0be85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6035",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T09:55:56.1581386Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "1cd86f93-5537-45cd-a1f9-eec34de42801",
            "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
            "ParentItemId": "0be85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:55:56.173Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "0be85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3007_Sarah_Allen.07.2025.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (2be10f5c)
2025-07-31T10:56:29.5422855+01:00 80174351-0002-bf00-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 0be85c9e-cae7-6322-9971-ff0200979a84 (f9527335)
2025-07-31T10:56:29.7233765+01:00 80174351-0002-bf00-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-31T10:56:30.1944808+01:00 80174351-0002-bf00-b63f-84710c7967bb [ERR] Error processing CV: Response status code does not indicate success: 404 (Not Found). (db13d13f)
2025-07-31T10:56:30.1945456+01:00 80174351-0002-bf00-b63f-84710c7967bb [INF] (Webjob Application) Posted 0be85c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (ae78bf9d)
2025-07-31T10:56:30.2839454+01:00 80174351-0002-bf00-b63f-84710c7967bb [INF] (Webjob Application) Posted 0be85c9e-cae7-6322-9971-ff0200979a84  (dc15baae)
2025-07-31T10:56:38.8723323+01:00 800afe46-0003-7800-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "d9bb8029-491a-4056-9992-c5fd9a992d09",
        "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
        "ParentItemId": "76e85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:56:06.25Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "76e85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3007_Sarah_Allen.07.2025.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "76e85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6036",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-31T09:56:06.2495669Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "d9bb8029-491a-4056-9992-c5fd9a992d09",
            "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
            "ParentItemId": "76e85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:56:06.25Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "76e85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3007_Sarah_Allen.07.2025.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (7731f6af)
2025-07-31T10:56:38.8725941+01:00 800afe46-0003-7800-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "d9bb8029-491a-4056-9992-c5fd9a992d09",
        "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
        "ParentItemId": "76e85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T09:56:06.25Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "76e85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3007_Sarah_Allen.07.2025.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "76e85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6036",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-31T09:56:06.2495669Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3007\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah\",\"lastName\":\"Allen\",\"email\":\"<EMAIL>\",\"phone\":\"07511861593\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Ch62 4re\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"unknown\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "d9bb8029-491a-4056-9992-c5fd9a992d09",
            "ChildItemId": "f80ec718-b337-4788-a42f-ea4d6783c8ae",
            "ParentItemId": "76e85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3007_Sarah_Allen.07.2025.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T09:56:06.25Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "76e85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3007_Sarah_Allen.07.2025.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3007_sarah_allen.07.2025.docx?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (62615477)
2025-07-31T10:56:38.8727172+01:00 800afe46-0003-7800-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 76e85c9e-cae7-6322-9971-ff0200979a84 (d79cc654)
2025-07-31T10:56:39.0780636+01:00 800afe46-0003-7800-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-31T10:56:39.5253308+01:00 800afe46-0003-7800-b63f-84710c7967bb [ERR] Error processing CV: Response status code does not indicate success: 404 (Not Found). (db13d13f)
2025-07-31T10:56:39.5253845+01:00 800afe46-0003-7800-b63f-84710c7967bb [INF] (Webjob Application) Posted 76e85c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (f11ae414)
2025-07-31T10:56:39.6045228+01:00 800afe46-0003-7800-b63f-84710c7967bb [INF] (Webjob Application) Posted 76e85c9e-cae7-6322-9971-ff0200979a84  (23211e27)
2025-07-31T18:45:55.9319524+01:00  [WRN] Using an in-memory repository. Keys will not be persisted to storage. (28e83010)
2025-07-31T18:45:55.9352956+01:00  [WRN] Neither user profile nor HKLM registry available. Using an ephemeral key repository. Protected data will be unavailable when application exits. (54f66960)
2025-07-31T18:45:55.9744357+01:00  [WRN] No XML encryptor configured. Key {34a9a575-aa96-4391-86ca-d10c08ccfbda} may be persisted to storage in unencrypted form. (9ca7e61e)
2025-07-31T18:45:56.0547654+01:00 8000964e-0000-7100-b63f-84710c7967bb [WRN] Failed to determine the https port for redirect. (ca76cc21)
2025-07-31T18:45:56.3974537+01:00 8000964e-0000-7100-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4403\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Zaki\",\"lastName\":\"Nisar\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"BL4 7RJ\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "a1ec5c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6037",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T17:45:54.8239769Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4403\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Zaki\",\"lastName\":\"Nisar\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"BL4 7RJ\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (8fd058cb)
2025-07-31T18:45:56.4882032+01:00 8000964e-0000-7100-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4403\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Zaki\",\"lastName\":\"Nisar\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"BL4 7RJ\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
    "FileFieldController": null
  },
  "OriginalEvent": {
    "EntryId": "a1ec5c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6037",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T17:45:54.8239769Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4403\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Zaki\",\"lastName\":\"Nisar\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"BL4 7RJ\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": null,
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (34bc42e1)
2025-07-31T18:45:56.4928402+01:00 8000964e-0000-7100-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId a1ec5c9e-cae7-6322-9971-ff0200979a84 (83ecf82b)
2025-07-31T18:45:56.5710994+01:00 8000964e-0000-7100-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-31T18:45:56.5724926+01:00 8000964e-0000-7100-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-31T18:45:57.0534713+01:00 8000964e-0000-7100-b63f-84710c7967bb [INF] Received HTTP response headers after 475.7454ms - 201 (f0742c1f)
2025-07-31T18:45:57.0541719+01:00 8000964e-0000-7100-b63f-84710c7967bb [INF] End processing HTTP request after 492.712ms - 201 (7656b38e)
2025-07-31T18:45:57.0563437+01:00 8000964e-0000-7100-b63f-84710c7967bb [INF] (Webjob Application) Posted a1ec5c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is True (e16910f2)
2025-07-31T18:45:59.6498007+01:00 8000964e-0000-7100-b63f-84710c7967bb [INF] (Webjob Application) Posted a1ec5c9e-cae7-6322-9971-ff0200979a84  (0e1d9e4d)
2025-07-31T18:50:32.9833321+01:00 80015bbf-0002-8500-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4203\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Zaki\",\"lastName\":\"Nisar\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"BL4 7RJ\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
    "FileFieldController": [
      {
        "Id": "e4e0e8dc-e70a-406d-910f-6fa733efea3d",
        "ChildItemId": "edac48d5-6aa6-4ba5-bd8b-16b1cafc7436",
        "ParentItemId": "47ec5c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4203_Zaki_Nisar.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4203_zaki_nisar.docx?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T17:50:32.827Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "47ec5c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4203_Zaki_Nisar.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4203_zaki_nisar.docx?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "47ec5c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6038",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T17:50:32.1085506Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4203\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Zaki\",\"lastName\":\"Nisar\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"BL4 7RJ\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "e4e0e8dc-e70a-406d-910f-6fa733efea3d",
            "ChildItemId": "edac48d5-6aa6-4ba5-bd8b-16b1cafc7436",
            "ParentItemId": "47ec5c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4203_Zaki_Nisar.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4203_zaki_nisar.docx?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T17:50:32.827Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "47ec5c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4203_Zaki_Nisar.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4203_zaki_nisar.docx?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (da46d2b6)
2025-07-31T18:50:32.9901424+01:00 80015bbf-0002-8500-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4203\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Zaki\",\"lastName\":\"Nisar\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"BL4 7RJ\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
    "FileFieldController": [
      {
        "Id": "e4e0e8dc-e70a-406d-910f-6fa733efea3d",
        "ChildItemId": "edac48d5-6aa6-4ba5-bd8b-16b1cafc7436",
        "ParentItemId": "47ec5c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4203_Zaki_Nisar.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4203_zaki_nisar.docx?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-31T17:50:32.827Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "47ec5c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4203_Zaki_Nisar.docx",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4203_zaki_nisar.docx?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "47ec5c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6038",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-31T17:50:32.1085506Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4203\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Zaki\",\"lastName\":\"Nisar\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"BL4 7RJ\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "e4e0e8dc-e70a-406d-910f-6fa733efea3d",
            "ChildItemId": "edac48d5-6aa6-4ba5-bd8b-16b1cafc7436",
            "ParentItemId": "47ec5c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4203_Zaki_Nisar.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4203_zaki_nisar.docx?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-31T17:50:32.827Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "47ec5c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4203_Zaki_Nisar.docx",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4203_zaki_nisar.docx?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (6bae8aff)
2025-07-31T18:50:33.0192047+01:00 80015bbf-0002-8500-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 47ec5c9e-cae7-6322-9971-ff0200979a84 (b0d950bb)
2025-07-31T18:50:33.2849796+01:00 80015bbf-0002-8500-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-31T18:50:33.5600659+01:00 80015bbf-0002-8500-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-31T18:50:33.5601288+01:00 80015bbf-0002-8500-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-31T18:50:35.3544502+01:00 80015bbf-0002-8500-b63f-84710c7967bb [INF] Received HTTP response headers after 1794.268ms - 201 (f0742c1f)
2025-07-31T18:50:35.3545285+01:00 80015bbf-0002-8500-b63f-84710c7967bb [INF] End processing HTTP request after 1794.5423ms - 201 (7656b38e)
2025-07-31T18:50:35.3546337+01:00 80015bbf-0002-8500-b63f-84710c7967bb [INF] (Webjob Application) Posted 47ec5c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is True (aaa3fdcc)
2025-07-31T18:50:35.4304822+01:00 80015bbf-0002-8500-b63f-84710c7967bb [INF] (Webjob Application) Posted 47ec5c9e-cae7-6322-9971-ff0200979a84  (81353b8e)
