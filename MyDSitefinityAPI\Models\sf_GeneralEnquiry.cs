﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace MyDSitefinityAPI.Models
{
    [Table("sf_generalEnquiryForm", Schema = "dbo")]
    public class sf_GeneralEnquiry
    {
        [Key]
        [Column("Id")]
        public Guid Id { get; set; }
        [Column("First Name")]
        public string FirstName { get; set; }
        [Column("Last Name")]
        public string LastName { get; set; }
        [Column("Phone Number")]
        public string PhoneNumber { get; set; }
        public string Email { get; set; }
        public string? Postcode { get; set; }
        public DateTime DOB { get; set; }
        public string Treatments { get; set; }
        public string GAClientId { get; set; }
        public DateTime? ProcessedDate { get; set; }
        public DateTime? ErrorDate { get; set; }
        public string RawJson { get; set; }
        public DateTime? CreatedDate { get; set; }
        public string Source { get; set; }
        public string PracticePostCode { get; set; }
        public Int64? PracticeId { get; set; }
    }
}
