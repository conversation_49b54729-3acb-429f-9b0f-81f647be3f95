{"Name": "Form is submitted", "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49", "Item": {"TextFieldController": "test0911", "TextFieldController_0": "test0911", "TextFieldController_1": "07993168963", "TextFieldController_2": "<EMAIL>", "TextFieldController_3": "12/11/1970", "TextFieldController_4": "M26 1GG", "CheckboxesFieldController": "Teeth whitening,Teeth straightening", "TextFieldController_5": ""}, "OriginalEvent": {"EntryId": "10b5ad9c-cae7-6322-9971-ff0000979a84", "ReferralCode": "1", "UserId": "00000000-0000-0000-0000-000000000000", "Username": "", "IpAddress": "**************", "SubmissionTime": "2023-11-09T10:03:43.9024491Z", "FormId": "03a5ad9c-cae7-6322-9971-ff0000979a84", "FormName": "sf_quick_contact_generic_form", "FormTitle": "Quick contact Generic form", "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000", "SendConfirmationEmail": false, "Controls": [{"FieldControlName": null, "Id": "44a6ad9c-cae7-6322-9971-ff0000979a84", "SiblingId": "00000000-0000-0000-0000-000000000000", "Text": null, "Type": 0, "Title": "First name", "FieldName": "TextFieldController", "Value": "test0911", "OldValue": null}, {"FieldControlName": null, "Id": "51a6ad9c-cae7-6322-9971-ff0000979a84", "SiblingId": "44a6ad9c-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Last name", "FieldName": "TextFieldController_0", "Value": "test0911", "OldValue": null}, {"FieldControlName": null, "Id": "5ea6ad9c-cae7-6322-9971-ff0000979a84", "SiblingId": "51a6ad9c-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Phone number", "FieldName": "TextFieldController_1", "Value": "07993168963", "OldValue": null}, {"FieldControlName": null, "Id": "6da6ad9c-cae7-6322-9971-ff0000979a84", "SiblingId": "5ea6ad9c-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Email", "FieldName": "TextFieldController_2", "Value": "<EMAIL>", "OldValue": null}, {"FieldControlName": null, "Id": "7ca6ad9c-cae7-6322-9971-ff0000979a84", "SiblingId": "6da6ad9c-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Date of birth (dd/mm/yyyy)", "FieldName": "TextFieldController_3", "Value": "30/05/1992", "OldValue": null}, {"FieldControlName": null, "Id": "93a6ad9c-cae7-6322-9971-ff0000979a84", "SiblingId": "8aa6ad9c-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Postcode", "FieldName": "TextFieldController_4", "Value": "M26 1GG", "OldValue": null}, {"FieldControlName": null, "Id": "a1a6ad9c-cae7-6322-9971-ff0000979a84", "SiblingId": "93a6ad9c-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Interested in", "FieldName": "CheckboxesFieldController", "Value": "Teeth whitening,Teeth straightening", "OldValue": null}, {"FieldControlName": null, "Id": "c1a6ad9c-cae7-6322-9971-ff0000979a84", "SiblingId": "afa6ad9c-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "GAClientId", "FieldName": "TextFieldController_5", "Value": "", "OldValue": null}], "Origin": null}, "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent, Telerik.Sitefinity, Version=14.4.8132.0, Culture=neutral, PublicKeyToken=b28c218413bdf563"}