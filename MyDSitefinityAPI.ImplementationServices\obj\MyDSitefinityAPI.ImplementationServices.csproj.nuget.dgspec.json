{"format": 1, "restore": {"C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.ImplementationServices\\MyDSitefinityAPI.ImplementationServices.csproj": {}}, "projects": {"C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityApi.ClinicianPortalApi\\MyDSitefinityApi.ClinicianPortalApi.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityApi.ClinicianPortalApi\\MyDSitefinityApi.ClinicianPortalApi.csproj", "projectName": "Mydentist.MyDSitefinityApi.ClinicianPortalApi", "projectPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityApi.ClinicianPortalApi\\MyDSitefinityApi.ClinicianPortalApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityApi.ClinicianPortalApi\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Extensions.Options": {"target": "Package", "version": "[7.0.1, )"}, "Newtonsoft.Json.Bson": {"target": "Package", "version": "[1.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Domain\\MyDSitefinityAPI.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Domain\\MyDSitefinityAPI.Domain.csproj", "projectName": "Mydentist.MyDSitefinityAPI.Domain", "projectPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Domain\\MyDSitefinityAPI.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.ImplementationServices\\MyDSitefinityAPI.ImplementationServices.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.ImplementationServices\\MyDSitefinityAPI.ImplementationServices.csproj", "projectName": "Mydentist.MyDSitefinityAPI.ImplementationServices", "projectPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.ImplementationServices\\MyDSitefinityAPI.ImplementationServices.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.ImplementationServices\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityApi.ClinicianPortalApi\\MyDSitefinityApi.ClinicianPortalApi.csproj": {"projectPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityApi.ClinicianPortalApi\\MyDSitefinityApi.ClinicianPortalApi.csproj"}, "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Domain\\MyDSitefinityAPI.Domain.csproj": {"projectPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Domain\\MyDSitefinityAPI.Domain.csproj"}, "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Persistence\\MyDSitefinityAPI.Persistence.csproj": {"projectPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Persistence\\MyDSitefinityAPI.Persistence.csproj"}, "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.WebConfigApi\\MyDSitefinityAPI.WebConfigApi.csproj": {"projectPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.WebConfigApi\\MyDSitefinityAPI.WebConfigApi.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.0, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Persistence\\MyDSitefinityAPI.Persistence.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Persistence\\MyDSitefinityAPI.Persistence.csproj", "projectName": "Mydentist.MyDSitefinityAPI.Persistence", "projectPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Persistence\\MyDSitefinityAPI.Persistence.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Persistence\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Domain\\MyDSitefinityAPI.Domain.csproj": {"projectPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Domain\\MyDSitefinityAPI.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"EntityFramework": {"target": "Package", "version": "[6.4.4, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.1.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[7.0.4, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[7.0.3, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[7.0.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.WebConfigApi\\MyDSitefinityAPI.WebConfigApi.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.WebConfigApi\\MyDSitefinityAPI.WebConfigApi.csproj", "projectName": "Mydentist.MyDSitefinityAPI.WebConfigApi", "projectPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.WebConfigApi\\MyDSitefinityAPI.WebConfigApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.WebConfigApi\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Domain\\MyDSitefinityAPI.Domain.csproj": {"projectPath": "C:\\Dev\\MyDSitefinityAPI\\MyDSitefinityAPI.Domain\\MyDSitefinityAPI.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.1.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[7.0.4, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[7.0.4, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Configuration.ConfigurationManager": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}