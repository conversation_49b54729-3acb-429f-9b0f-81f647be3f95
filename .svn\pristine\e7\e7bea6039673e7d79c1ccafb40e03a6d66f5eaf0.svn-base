CREATE TABLE [Recruitment].[ApplicationsLog](
	[Id] [bigint] IDENTITY(1,1) NOT NULL,
	[Title] [nvarchar](10) NULL,
	[FirstName] [nvarchar](max) NULL,
	[LastName] [nvarchar](max) NULL,
	[VacancyId] [bigint] NOT NULL,
	[Address] [nvarchar](max) NULL,
	[PostCode] [nvarchar](50) NULL,
	[EmailAddress] [nvarchar](80) NULL,
	[GDCNumber] [nvarchar](50) NULL,
	[ContactNumber] [nvarchar](50) NULL,
	[CVLink] [nvarchar](max) NULL,
	[DateCreated] [datetime2](7) NULL,
	[RawJson] [nvarchar](max) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO