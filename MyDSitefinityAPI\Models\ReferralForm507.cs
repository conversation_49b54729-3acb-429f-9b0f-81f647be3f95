﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MyDSitefinityAPI.Models
{
    [Table("sf_507referralform", Schema = "dbo")]
    public class ReferralForm507
    {
        [Key]
        [Column("Id")]
        public int Id { get; set; }
        public string? EntryId { get; set; }
        public string? RawJson { get; set; }
        public DateTime CreatedDate { get; set; }

        // Dentist Information
        public string? ChosenTreatments { get; set; }
        public string? ReferringDentistName { get; set; }
        public string? DentistAddress { get; set; }
        public string? DentistTelephoneNumber { get; set; }
        public string? DentistMobileNumber { get; set; }
        public string? DentistFax { get; set; }
        public string? DentistEmail { get; set; }

        // Patient Information
        public string? PatientName { get; set; }
        public string? PatientAddress { get; set; }
        public string? PatientTelephoneNumber { get; set; }
        public string? PatientMobileNumber { get; set; }
        public string? PatientEmail { get; set; }
        public DateTime? PatientDOB { get; set; }
        public bool PreviouslySeen { get; set; }
        public bool InformedOfCosts { get; set; }
        public PreferredContactMethod? PreferredContactMethod { get; set; }

        // Treatment Information
        public string? TreatmentInfoToothNumber { get; set; }
        public string? ReasonForReferral { get; set; }
        public bool PostAndCoreRequired { get; set; }
        public bool Pain { get; set; }
        public string? HowSevereIsThePain { get; set; }
        public bool Swelling { get; set; }
        public bool ToothRootFilled { get; set; }
        public bool ConsultationOnly { get; set; }
        public bool Treatment { get; set; }
        public List<PatientSymptoms>? PatientSymptoms { get; set; }
        public string? OtherSymptoms { get; set; }
        public bool TreatmentUnderSedation { get; set; }
        public string? RelevantMedicalHistory { get; set; }
        public List<string>? RelevantAttachments { get; set; }

        // Practice Information
        public string PracticePostcode { get; set; } = "BS6 7PT";
        public Int64? PracticeId { get; set; } = 507;
    }

    public enum PreferredContactMethod
    {
        Email,
        Post,
        Telephone
    }

    public enum PatientSymptoms
    {
        DifficultyChewing,
        RecurringAbscesses,
        ToothMobility
    }
}