import json
import os
import re

def parse_log_content(content, filename):
    """Extracts JSON payloads from log content."""
    events = []
    # Regex to find all occurrences of the event and its JSON payload
    matches = re.finditer(r'The raw json at endpoint is:\s*({.*?})\s*([a-f0-9]{8})', content, re.DOTALL)
    for match in matches:
        payload_text = match.group(1).strip()
        events.append({
            "payload_text": payload_text,
            "source_file": filename
        })
    return events

def main():
    log_dir = r'C:\Dev\MyDSitefinityAPI\logs form submission'
    unique_structure_events = []
    seen_structures = set()

    log_files = sorted([f for f in os.listdir(log_dir) if f.startswith('log-') and f.endswith('.txt')])

    for log_file in log_files:
        file_path = os.path.join(log_dir, log_file)
        print(f"Processing {log_file}...")
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
        except Exception as e:
            print(f"  Error reading file: {e}")
            continue

        events = parse_log_content(content, log_file)
        for event in events:
            try:
                # Parse the JSON payload to analyze its structure
                payload_dict = json.loads(event['payload_text'])
                
                # The structure is defined by the sorted tuple of its keys
                # This handles nested objects as well by representing them as a string
                structure_key = tuple(sorted(payload_dict.keys()))

                if structure_key not in seen_structures:
                    # If we haven't seen this structure before, add it for output
                    # and record the structure.
                    unique_structure_events.append({
                        "structure": list(structure_key),
                        "example_payload": payload_dict,
                        "source_file": event["source_file"]
                    })
                    seen_structures.add(structure_key)
            except (json.JSONDecodeError, AttributeError, TypeError):
                # Ignore payloads that are not valid JSON dictionaries
                pass


    output_file = os.path.join(log_dir, 'unique_log_structures.json')
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(unique_structure_events, f, indent=2)

    print(f"\nProcessed {len(log_files)} log files.")
    print(f"Found {len(unique_structure_events)} unique event structures.")
    print(f"Output with examples written to {output_file}")

if __name__ == "__main__":
    main()
