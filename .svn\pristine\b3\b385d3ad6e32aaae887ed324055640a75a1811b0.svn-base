{"Name": "Form is submitted", "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49", "Item": {"CheckboxesFieldController_0": "I have read and accept the terms and conditions", "CheckboxesFieldController_1": "Teeth whitening,Dental implants,White fillings,Dental plan/insurance", "EmailTextFieldController": "<EMAIL>", "CheckboxesFieldController": null}, "OriginalEvent": {"EntryId": "6a476a9c-cae7-6322-9971-ff0200979a84", "ReferralCode": "9172", "UserId": "00000000-0000-0000-0000-000000000000", "Username": "", "IpAddress": "***************", "SubmissionTime": "2023-07-05T13:10:06.2751481Z", "FormId": "56592a9b-cae7-6322-9971-ff0000979a84", "FormName": "sf_wifiloginform", "FormTitle": "WiFi Login Form", "FormSubscriptionListId": "6d19af9b-cae7-6322-9971-ff0000979a84", "SendConfirmationEmail": false, "Controls": [{"FieldControlName": null, "Id": "9c1ad89b-cae7-6322-9971-ff0000979a84", "SiblingId": "395b2a9b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Terms and conditions", "FieldName": "CheckboxesFieldController_0", "Value": "I have read and accept the terms and conditions", "OldValue": null}, {"FieldControlName": null, "Id": "b31ad89b-cae7-6322-9971-ff0000979a84", "SiblingId": "aa1ad89b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Would you like to receive more information about our products and services?", "FieldName": "CheckboxesFieldController_1", "Value": "Teeth whitening,Dental implants,White fillings,Dental plan/insurance", "OldValue": null}, {"FieldControlName": null, "Id": "2642469b-cae7-6322-9971-ff0000979a84", "SiblingId": "b31ad89b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Email", "FieldName": "EmailTextFieldController", "Value": "<EMAIL>", "OldValue": null}, {"FieldControlName": null, "Id": "c48c939b-cae7-6322-9971-ff0000979a84", "SiblingId": "00000000-0000-0000-0000-000000000000", "Text": null, "Type": 0, "Title": "Select a choice", "FieldName": "CheckboxesFieldController", "Value": null, "OldValue": null}], "Origin": null}, "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"}