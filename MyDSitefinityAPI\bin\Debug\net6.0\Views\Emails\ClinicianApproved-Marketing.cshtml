﻿@using MyDSitefinityAPI.Models.PracticeDirectory;
@using System.Configuration;

@model PerformerProfile

<html>
<head>
</head>
<body>
    <div style="font-family: Arial; margin-left: 15px;">
        <h2 style="border-left: 10px solid #7e3b70; border-bottom: 1px solid #7e3b70; padding: 15px;">Image for @Model.Performer.Name has been uploaded for approval</h2>
        <div>
            <p style="padding: 10px;">
                Hello,
            </p>
            <p style="padding: 10px;">
                Image was uploaded for a @Model.Performer.Name.<br />
                <br />
                <a style="color: #b455a0" href="@ConfigurationManager.AppSettings["PracticeViewImageApprovalUrl"]?ImageId=@Model.Images.First().ImageId">Click here for the approval process</a>
            </p>
            <h3 style="border-left: 5px solid #7e3b70; padding: 10px">Image Upload Initiated by:</h3>
            <p style="padding: 10px;">
                Practice: @Model.Images.First().Practice.PracticeName<br />
                Practice Manager: @Model.Images.First().Practice.PMName<br />
                Practice Manager's Email: @Model.Images.First().Practice.PMEmail
            </p>
            <p style="padding: 10px;">
                Practice Directory
            </p>
        </div>
        <div style="border-top: 1px dashed #aaaaaa; padding: 10px;">
            <span style="color: #aaaaaa; font-style: italic;">This email has been generated automatically by the Practice Directory</span>
        </div>
    </div>

</body>
</html>