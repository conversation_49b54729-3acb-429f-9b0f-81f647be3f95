2025-07-28T10:19:00.2768159+01:00  [WRN] Using an in-memory repository. Keys will not be persisted to storage. (28e83010)
2025-07-28T10:19:00.2800407+01:00  [WRN] Neither user profile nor HKLM registry available. Using an ephemeral key repository. Protected data will be unavailable when application exits. (54f66960)
2025-07-28T10:19:00.3213938+01:00  [WRN] No XML encryptor configured. Key {feb5c4a7-7a57-4c5e-bd25-e0fa4c28a395} may be persisted to storage in unencrypted form. (9ca7e61e)
2025-07-28T10:19:00.4049233+01:00 800fec25-0002-8c00-b63f-84710c7967bb [WRN] Failed to determine the https port for redirect. (ca76cc21)
2025-07-28T10:19:00.7702947+01:00 800fec25-0002-8c00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4473\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Marwa\",\"lastName\":\"Jaafar\",\"email\":\"<EMAIL>\",\"phone\":\"07888857818\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"NE3 4JS\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "cc31af37-e608-4dfc-a7d9-c0093b29d70c",
        "ChildItemId": "a6ef4af4-a649-4a37-a43c-5eb20947a53b",
        "ParentItemId": "1ac55c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4473_Marwa_Jaafar.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4473_marwa_jaafar.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T09:18:59.143Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "1ac55c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4473_Marwa_Jaafar.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4473_marwa_jaafar.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "1ac55c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "5993",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "************",
    "SubmissionTime": "2025-07-28T09:18:59.0956303Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4473\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Marwa\",\"lastName\":\"Jaafar\",\"email\":\"<EMAIL>\",\"phone\":\"07888857818\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"NE3 4JS\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "cc31af37-e608-4dfc-a7d9-c0093b29d70c",
            "ChildItemId": "a6ef4af4-a649-4a37-a43c-5eb20947a53b",
            "ParentItemId": "1ac55c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4473_Marwa_Jaafar.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4473_marwa_jaafar.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T09:18:59.143Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "1ac55c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4473_Marwa_Jaafar.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4473_marwa_jaafar.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (d1e24336)
2025-07-28T10:19:00.8599385+01:00 800fec25-0002-8c00-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4473\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Marwa\",\"lastName\":\"Jaafar\",\"email\":\"<EMAIL>\",\"phone\":\"07888857818\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"NE3 4JS\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "cc31af37-e608-4dfc-a7d9-c0093b29d70c",
        "ChildItemId": "a6ef4af4-a649-4a37-a43c-5eb20947a53b",
        "ParentItemId": "1ac55c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4473_Marwa_Jaafar.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4473_marwa_jaafar.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T09:18:59.143Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "1ac55c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4473_Marwa_Jaafar.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4473_marwa_jaafar.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "1ac55c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "5993",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "************",
    "SubmissionTime": "2025-07-28T09:18:59.0956303Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4473\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Marwa\",\"lastName\":\"Jaafar\",\"email\":\"<EMAIL>\",\"phone\":\"07888857818\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"NE3 4JS\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "cc31af37-e608-4dfc-a7d9-c0093b29d70c",
            "ChildItemId": "a6ef4af4-a649-4a37-a43c-5eb20947a53b",
            "ParentItemId": "1ac55c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4473_Marwa_Jaafar.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4473_marwa_jaafar.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T09:18:59.143Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "1ac55c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4473_Marwa_Jaafar.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4473_marwa_jaafar.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (b82bc58c)
2025-07-28T10:19:00.9469539+01:00 800fec25-0002-8c00-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 1ac55c9e-cae7-6322-9971-ff0200979a84 (24e1b5cb)
2025-07-28T10:19:01.3052100+01:00 800fec25-0002-8c00-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-28T10:19:01.5914417+01:00 800fec25-0002-8c00-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-28T10:19:01.5927760+01:00 800fec25-0002-8c00-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-28T10:19:02.1013801+01:00 800fec25-0002-8c00-b63f-84710c7967bb [INF] Received HTTP response headers after 503.161ms - 201 (f0742c1f)
2025-07-28T10:19:02.1021215+01:00 800fec25-0002-8c00-b63f-84710c7967bb [INF] End processing HTTP request after 520.4377ms - 201 (7656b38e)
2025-07-28T10:19:02.1034556+01:00 800fec25-0002-8c00-b63f-84710c7967bb [INF] (Webjob Application) Posted 1ac55c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is True (03d94bb9)
2025-07-28T10:19:06.3768807+01:00 800fec25-0002-8c00-b63f-84710c7967bb [INF] (Webjob Application) Posted 1ac55c9e-cae7-6322-9971-ff0200979a84  (f3ca693b)
2025-07-28T13:24:15.8366038+01:00  [WRN] Using an in-memory repository. Keys will not be persisted to storage. (28e83010)
2025-07-28T13:24:15.8399061+01:00  [WRN] Neither user profile nor HKLM registry available. Using an ephemeral key repository. Protected data will be unavailable when application exits. (54f66960)
2025-07-28T13:24:15.8810247+01:00  [WRN] No XML encryptor configured. Key {b658723a-9755-4fe2-b495-ef377fc376e8} may be persisted to storage in unencrypted form. (9ca7e61e)
2025-07-28T13:24:15.9610978+01:00 80046732-0003-8600-b63f-84710c7967bb [WRN] Failed to determine the https port for redirect. (ca76cc21)
2025-07-28T13:24:16.3087119+01:00 80046732-0003-8600-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3145\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Rayan\",\"lastName\":\"Khan\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M16 9GR\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
    "FileFieldController": [
      {
        "Id": "4723cee9-32dd-4cfd-85ce-bf826710b83c",
        "ChildItemId": "46750f4f-7955-4e72-9957-59c90e088c94",
        "ParentItemId": "e4c65c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3145_Rayan_Khan.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3145_rayan_khan.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T12:24:14.71Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "e4c65c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3145_Rayan_Khan.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3145_rayan_khan.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "e4c65c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "5994",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-28T12:24:14.6939077Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3145\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Rayan\",\"lastName\":\"Khan\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M16 9GR\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "4723cee9-32dd-4cfd-85ce-bf826710b83c",
            "ChildItemId": "46750f4f-7955-4e72-9957-59c90e088c94",
            "ParentItemId": "e4c65c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3145_Rayan_Khan.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3145_rayan_khan.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T12:24:14.71Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "e4c65c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3145_Rayan_Khan.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3145_rayan_khan.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (fc3a1036)
2025-07-28T13:24:16.3990105+01:00 80046732-0003-8600-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3145\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Rayan\",\"lastName\":\"Khan\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M16 9GR\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
    "FileFieldController": [
      {
        "Id": "4723cee9-32dd-4cfd-85ce-bf826710b83c",
        "ChildItemId": "46750f4f-7955-4e72-9957-59c90e088c94",
        "ParentItemId": "e4c65c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3145_Rayan_Khan.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3145_rayan_khan.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T12:24:14.71Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "e4c65c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3145_Rayan_Khan.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3145_rayan_khan.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "e4c65c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "5994",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-28T12:24:14.6939077Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3145\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Rayan\",\"lastName\":\"Khan\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M16 9GR\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "4723cee9-32dd-4cfd-85ce-bf826710b83c",
            "ChildItemId": "46750f4f-7955-4e72-9957-59c90e088c94",
            "ParentItemId": "e4c65c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3145_Rayan_Khan.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3145_rayan_khan.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T12:24:14.71Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "e4c65c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3145_Rayan_Khan.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3145_rayan_khan.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (acc9f5f4)
2025-07-28T13:24:16.4867970+01:00 80046732-0003-8600-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId e4c65c9e-cae7-6322-9971-ff0200979a84 (1063e1c2)
2025-07-28T13:24:16.8476306+01:00 80046732-0003-8600-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-28T13:24:17.2189276+01:00 80046732-0003-8600-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-28T13:24:17.2202526+01:00 80046732-0003-8600-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-28T13:24:18.1476896+01:00 80046732-0003-8600-b63f-84710c7967bb [INF] Received HTTP response headers after 921.7367ms - 201 (f0742c1f)
2025-07-28T13:24:18.1484252+01:00 80046732-0003-8600-b63f-84710c7967bb [INF] End processing HTTP request after 939.3602ms - 201 (7656b38e)
2025-07-28T13:24:18.1496723+01:00 80046732-0003-8600-b63f-84710c7967bb [INF] (Webjob Application) Posted e4c65c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is True (8380340c)
2025-07-28T13:24:20.2491777+01:00 80046732-0003-8600-b63f-84710c7967bb [INF] (Webjob Application) Posted e4c65c9e-cae7-6322-9971-ff0200979a84  (8d982fb4)
2025-07-28T13:24:51.5906560+01:00 80586120-0001-f100-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3144\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Rayan\",\"lastName\":\"Khan\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M16 9GR\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
    "FileFieldController": [
      {
        "Id": "603db192-3f70-4508-acbd-370dc91ecabf",
        "ChildItemId": "faf5e94d-e1ce-46c0-a169-fbef40afb294",
        "ParentItemId": "e5c65c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3144_Rayan_Khan.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3144_rayan_khan.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T12:24:51.447Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "e5c65c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3144_Rayan_Khan.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3144_rayan_khan.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "e5c65c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "5995",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-28T12:24:51.4297057Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3144\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Rayan\",\"lastName\":\"Khan\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M16 9GR\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "603db192-3f70-4508-acbd-370dc91ecabf",
            "ChildItemId": "faf5e94d-e1ce-46c0-a169-fbef40afb294",
            "ParentItemId": "e5c65c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3144_Rayan_Khan.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3144_rayan_khan.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T12:24:51.447Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "e5c65c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3144_Rayan_Khan.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3144_rayan_khan.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (e30848c9)
2025-07-28T13:24:51.5974588+01:00 80586120-0001-f100-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"3144\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Rayan\",\"lastName\":\"Khan\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M16 9GR\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
    "FileFieldController": [
      {
        "Id": "603db192-3f70-4508-acbd-370dc91ecabf",
        "ChildItemId": "faf5e94d-e1ce-46c0-a169-fbef40afb294",
        "ParentItemId": "e5c65c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "3144_Rayan_Khan.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3144_rayan_khan.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T12:24:51.447Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "e5c65c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "3144_Rayan_Khan.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3144_rayan_khan.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "e5c65c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "5995",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-28T12:24:51.4297057Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"3144\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Rayan\",\"lastName\":\"Khan\",\"email\":\"<EMAIL>\",\"phone\":\"**********\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"M16 9GR\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "603db192-3f70-4508-acbd-370dc91ecabf",
            "ChildItemId": "faf5e94d-e1ce-46c0-a169-fbef40afb294",
            "ParentItemId": "e5c65c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "3144_Rayan_Khan.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3144_rayan_khan.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T12:24:51.447Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "e5c65c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "3144_Rayan_Khan.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/3144_rayan_khan.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (b5ae979d)
2025-07-28T13:24:51.5976904+01:00 80586120-0001-f100-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId e5c65c9e-cae7-6322-9971-ff0200979a84 (60a9e303)
2025-07-28T13:24:51.7705406+01:00 80586120-0001-f100-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-28T13:24:52.1343396+01:00 80586120-0001-f100-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-28T13:24:52.1344184+01:00 80586120-0001-f100-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-28T13:24:53.0584320+01:00 80586120-0001-f100-b63f-84710c7967bb [INF] Received HTTP response headers after 923.9383ms - 201 (f0742c1f)
2025-07-28T13:24:53.0584944+01:00 80586120-0001-f100-b63f-84710c7967bb [INF] End processing HTTP request after 924.2432ms - 201 (7656b38e)
2025-07-28T13:24:53.0586180+01:00 80586120-0001-f100-b63f-84710c7967bb [INF] (Webjob Application) Posted e5c65c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is True (2ec86b75)
2025-07-28T13:24:53.1270146+01:00 80586120-0001-f100-b63f-84710c7967bb [INF] (Webjob Application) Posted e5c65c9e-cae7-6322-9971-ff0200979a84  (5fed0981)
2025-07-28T14:23:12.8582696+01:00  [WRN] Using an in-memory repository. Keys will not be persisted to storage. (28e83010)
2025-07-28T14:23:12.8616748+01:00  [WRN] Neither user profile nor HKLM registry available. Using an ephemeral key repository. Protected data will be unavailable when application exits. (54f66960)
2025-07-28T14:23:12.9031287+01:00  [WRN] No XML encryptor configured. Key {3cded3f2-13a1-454d-b59c-e577ebda30d9} may be persisted to storage in unencrypted form. (9ca7e61e)
2025-07-28T14:23:12.9870339+01:00 80100dc6-0003-c000-b63f-84710c7967bb [WRN] Failed to determine the https port for redirect. (ca76cc21)
2025-07-28T14:23:13.3507070+01:00 80100dc6-0003-c000-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4487\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Lawrence \",\"email\":\"<EMAIL>\",\"phone\":\"07903569574\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Sw17 0ea\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"8\",\"Question\":\"What Postcode, Town or City would you like to work near? \",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"Tooting/Wandsworth \"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"10\",\"Question\":\"Are you registered with the GDC?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"11\",\"AnswerValue\":\"11\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"9\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"160037\"}],\"ParentQuestionId\":10}]},{\"QuestionId\":\"11\",\"Question\":\"The questions below are for those not yet GDC registered, if you are a qualified nurse please select 'none applicable' option. \\n\\nWould you be comfortable assisting with dental procedures?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"13\",\"AnswerValue\":\"13\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"12\",\"Question\":\"Are you able to commit to a 12-18 month training program?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"17\",\"AnswerValue\":\"17\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"14\",\"Question\":\"Please select the option that best describes you\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"19\",\"AnswerValue\":\"19\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"15\",\"Question\":\"If you are currently/already registered on a Dental Nursing course, please provide the following details:\\n\\nWhat is the date you started on your current programme?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"16\",\"Question\":\"Who is your current training provider?\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"17\",\"Question\":\"Please provide contact details for your current training provider\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"19\",\"Question\":\"What is your predicted completion date?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "75dab35d-be42-4d4d-8395-6f4c9174286d",
        "ChildItemId": "273f70f6-519a-40f9-a722-b63f8a6d4cc4",
        "ParentItemId": "1dc85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4487_Sarah _Lawrence .pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T13:23:11.637Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "1dc85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4487_Sarah _Lawrence .pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "1dc85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "5996",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-28T13:23:11.6222822Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4487\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Lawrence \",\"email\":\"<EMAIL>\",\"phone\":\"07903569574\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Sw17 0ea\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"8\",\"Question\":\"What Postcode, Town or City would you like to work near? \",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"Tooting/Wandsworth \"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"10\",\"Question\":\"Are you registered with the GDC?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"11\",\"AnswerValue\":\"11\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"9\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"160037\"}],\"ParentQuestionId\":10}]},{\"QuestionId\":\"11\",\"Question\":\"The questions below are for those not yet GDC registered, if you are a qualified nurse please select 'none applicable' option. \\n\\nWould you be comfortable assisting with dental procedures?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"13\",\"AnswerValue\":\"13\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"12\",\"Question\":\"Are you able to commit to a 12-18 month training program?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"17\",\"AnswerValue\":\"17\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"14\",\"Question\":\"Please select the option that best describes you\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"19\",\"AnswerValue\":\"19\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"15\",\"Question\":\"If you are currently/already registered on a Dental Nursing course, please provide the following details:\\n\\nWhat is the date you started on your current programme?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"16\",\"Question\":\"Who is your current training provider?\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"17\",\"Question\":\"Please provide contact details for your current training provider\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"19\",\"Question\":\"What is your predicted completion date?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "75dab35d-be42-4d4d-8395-6f4c9174286d",
            "ChildItemId": "273f70f6-519a-40f9-a722-b63f8a6d4cc4",
            "ParentItemId": "1dc85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4487_Sarah _Lawrence .pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T13:23:11.637Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "1dc85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4487_Sarah _Lawrence .pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (3bfc5264)
2025-07-28T14:23:13.4442343+01:00 80100dc6-0003-c000-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4487\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Lawrence \",\"email\":\"<EMAIL>\",\"phone\":\"07903569574\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Sw17 0ea\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"8\",\"Question\":\"What Postcode, Town or City would you like to work near? \",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"Tooting/Wandsworth \"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"10\",\"Question\":\"Are you registered with the GDC?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"11\",\"AnswerValue\":\"11\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"9\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"160037\"}],\"ParentQuestionId\":10}]},{\"QuestionId\":\"11\",\"Question\":\"The questions below are for those not yet GDC registered, if you are a qualified nurse please select 'none applicable' option. \\n\\nWould you be comfortable assisting with dental procedures?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"13\",\"AnswerValue\":\"13\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"12\",\"Question\":\"Are you able to commit to a 12-18 month training program?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"17\",\"AnswerValue\":\"17\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"14\",\"Question\":\"Please select the option that best describes you\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"19\",\"AnswerValue\":\"19\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"15\",\"Question\":\"If you are currently/already registered on a Dental Nursing course, please provide the following details:\\n\\nWhat is the date you started on your current programme?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"16\",\"Question\":\"Who is your current training provider?\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"17\",\"Question\":\"Please provide contact details for your current training provider\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"19\",\"Question\":\"What is your predicted completion date?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "75dab35d-be42-4d4d-8395-6f4c9174286d",
        "ChildItemId": "273f70f6-519a-40f9-a722-b63f8a6d4cc4",
        "ParentItemId": "1dc85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4487_Sarah _Lawrence .pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T13:23:11.637Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "1dc85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4487_Sarah _Lawrence .pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "1dc85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "5996",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-28T13:23:11.6222822Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4487\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Lawrence \",\"email\":\"<EMAIL>\",\"phone\":\"07903569574\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Sw17 0ea\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"8\",\"Question\":\"What Postcode, Town or City would you like to work near? \",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"Tooting/Wandsworth \"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"10\",\"Question\":\"Are you registered with the GDC?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"11\",\"AnswerValue\":\"11\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"9\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"160037\"}],\"ParentQuestionId\":10}]},{\"QuestionId\":\"11\",\"Question\":\"The questions below are for those not yet GDC registered, if you are a qualified nurse please select 'none applicable' option. \\n\\nWould you be comfortable assisting with dental procedures?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"13\",\"AnswerValue\":\"13\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"12\",\"Question\":\"Are you able to commit to a 12-18 month training program?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"17\",\"AnswerValue\":\"17\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"14\",\"Question\":\"Please select the option that best describes you\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"19\",\"AnswerValue\":\"19\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"15\",\"Question\":\"If you are currently/already registered on a Dental Nursing course, please provide the following details:\\n\\nWhat is the date you started on your current programme?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"16\",\"Question\":\"Who is your current training provider?\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"17\",\"Question\":\"Please provide contact details for your current training provider\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"19\",\"Question\":\"What is your predicted completion date?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "75dab35d-be42-4d4d-8395-6f4c9174286d",
            "ChildItemId": "273f70f6-519a-40f9-a722-b63f8a6d4cc4",
            "ParentItemId": "1dc85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4487_Sarah _Lawrence .pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T13:23:11.637Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "1dc85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4487_Sarah _Lawrence .pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (46428876)
2025-07-28T14:23:13.5339296+01:00 80100dc6-0003-c000-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 1dc85c9e-cae7-6322-9971-ff0200979a84 (b93784e3)
2025-07-28T14:23:13.8949757+01:00 80100dc6-0003-c000-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-28T14:23:14.0955913+01:00 80100dc6-0003-c000-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-28T14:23:14.0969921+01:00 80100dc6-0003-c000-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-28T14:23:15.2029842+01:00 80100dc6-0003-c000-b63f-84710c7967bb [INF] Received HTTP response headers after 1100.7137ms - 201 (f0742c1f)
2025-07-28T14:23:15.2036686+01:00 80100dc6-0003-c000-b63f-84710c7967bb [INF] End processing HTTP request after 1120.998ms - 201 (7656b38e)
2025-07-28T14:23:15.2049952+01:00 80100dc6-0003-c000-b63f-84710c7967bb [INF] (Webjob Application) Posted 1dc85c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is True (38eda2ef)
2025-07-28T14:23:25.3966236+01:00 80100dc6-0003-c000-b63f-84710c7967bb [INF] (Webjob Application) Posted 1dc85c9e-cae7-6322-9971-ff0200979a84  (e869895b)
2025-07-28T14:25:38.3127560+01:00 801010d6-0003-c000-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4487\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Lawrence \",\"email\":\"<EMAIL>\",\"phone\":\"07903569574\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Sw17 0ea\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"8\",\"Question\":\"What Postcode, Town or City would you like to work near? \",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"Tooting/Wandsworth \"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"10\",\"Question\":\"Are you registered with the GDC?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"11\",\"AnswerValue\":\"11\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"9\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"160037\"}],\"ParentQuestionId\":10}]},{\"QuestionId\":\"11\",\"Question\":\"The questions below are for those not yet GDC registered, if you are a qualified nurse please select 'none applicable' option. \\n\\nWould you be comfortable assisting with dental procedures?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"13\",\"AnswerValue\":\"13\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"12\",\"Question\":\"Are you able to commit to a 12-18 month training program?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"17\",\"AnswerValue\":\"17\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"14\",\"Question\":\"Please select the option that best describes you\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"19\",\"AnswerValue\":\"19\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"15\",\"Question\":\"If you are currently/already registered on a Dental Nursing course, please provide the following details:\\n\\nWhat is the date you started on your current programme?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"16\",\"Question\":\"Who is your current training provider?\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"17\",\"Question\":\"Please provide contact details for your current training provider\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"19\",\"Question\":\"What is your predicted completion date?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "18afff55-2268-4383-b591-8032da27dec8",
        "ChildItemId": "273f70f6-519a-40f9-a722-b63f8a6d4cc4",
        "ParentItemId": "22c85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4487_Sarah _Lawrence .pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T13:25:38.147Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "22c85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4487_Sarah _Lawrence .pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "22c85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "5997",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "************",
    "SubmissionTime": "2025-07-28T13:25:38.1298217Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4487\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Lawrence \",\"email\":\"<EMAIL>\",\"phone\":\"07903569574\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Sw17 0ea\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"8\",\"Question\":\"What Postcode, Town or City would you like to work near? \",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"Tooting/Wandsworth \"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"10\",\"Question\":\"Are you registered with the GDC?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"11\",\"AnswerValue\":\"11\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"9\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"160037\"}],\"ParentQuestionId\":10}]},{\"QuestionId\":\"11\",\"Question\":\"The questions below are for those not yet GDC registered, if you are a qualified nurse please select 'none applicable' option. \\n\\nWould you be comfortable assisting with dental procedures?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"13\",\"AnswerValue\":\"13\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"12\",\"Question\":\"Are you able to commit to a 12-18 month training program?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"17\",\"AnswerValue\":\"17\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"14\",\"Question\":\"Please select the option that best describes you\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"19\",\"AnswerValue\":\"19\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"15\",\"Question\":\"If you are currently/already registered on a Dental Nursing course, please provide the following details:\\n\\nWhat is the date you started on your current programme?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"16\",\"Question\":\"Who is your current training provider?\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"17\",\"Question\":\"Please provide contact details for your current training provider\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"19\",\"Question\":\"What is your predicted completion date?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "18afff55-2268-4383-b591-8032da27dec8",
            "ChildItemId": "273f70f6-519a-40f9-a722-b63f8a6d4cc4",
            "ParentItemId": "22c85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4487_Sarah _Lawrence .pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T13:25:38.147Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "22c85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4487_Sarah _Lawrence .pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (a449d086)
2025-07-28T14:25:38.3232818+01:00 801010d6-0003-c000-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4487\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Lawrence \",\"email\":\"<EMAIL>\",\"phone\":\"07903569574\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Sw17 0ea\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"8\",\"Question\":\"What Postcode, Town or City would you like to work near? \",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"Tooting/Wandsworth \"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"10\",\"Question\":\"Are you registered with the GDC?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"11\",\"AnswerValue\":\"11\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"9\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"160037\"}],\"ParentQuestionId\":10}]},{\"QuestionId\":\"11\",\"Question\":\"The questions below are for those not yet GDC registered, if you are a qualified nurse please select 'none applicable' option. \\n\\nWould you be comfortable assisting with dental procedures?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"13\",\"AnswerValue\":\"13\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"12\",\"Question\":\"Are you able to commit to a 12-18 month training program?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"17\",\"AnswerValue\":\"17\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"14\",\"Question\":\"Please select the option that best describes you\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"19\",\"AnswerValue\":\"19\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"15\",\"Question\":\"If you are currently/already registered on a Dental Nursing course, please provide the following details:\\n\\nWhat is the date you started on your current programme?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"16\",\"Question\":\"Who is your current training provider?\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"17\",\"Question\":\"Please provide contact details for your current training provider\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"19\",\"Question\":\"What is your predicted completion date?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "18afff55-2268-4383-b591-8032da27dec8",
        "ChildItemId": "273f70f6-519a-40f9-a722-b63f8a6d4cc4",
        "ParentItemId": "22c85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4487_Sarah _Lawrence .pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T13:25:38.147Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "22c85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4487_Sarah _Lawrence .pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "22c85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "5997",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "************",
    "SubmissionTime": "2025-07-28T13:25:38.1298217Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4487\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Lawrence \",\"email\":\"<EMAIL>\",\"phone\":\"07903569574\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Sw17 0ea\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"8\",\"Question\":\"What Postcode, Town or City would you like to work near? \",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"Tooting/Wandsworth \"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"10\",\"Question\":\"Are you registered with the GDC?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"11\",\"AnswerValue\":\"11\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"9\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"160037\"}],\"ParentQuestionId\":10}]},{\"QuestionId\":\"11\",\"Question\":\"The questions below are for those not yet GDC registered, if you are a qualified nurse please select 'none applicable' option. \\n\\nWould you be comfortable assisting with dental procedures?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"13\",\"AnswerValue\":\"13\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"12\",\"Question\":\"Are you able to commit to a 12-18 month training program?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"17\",\"AnswerValue\":\"17\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"14\",\"Question\":\"Please select the option that best describes you\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"19\",\"AnswerValue\":\"19\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"15\",\"Question\":\"If you are currently/already registered on a Dental Nursing course, please provide the following details:\\n\\nWhat is the date you started on your current programme?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"16\",\"Question\":\"Who is your current training provider?\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"17\",\"Question\":\"Please provide contact details for your current training provider\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"19\",\"Question\":\"What is your predicted completion date?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "18afff55-2268-4383-b591-8032da27dec8",
            "ChildItemId": "273f70f6-519a-40f9-a722-b63f8a6d4cc4",
            "ParentItemId": "22c85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4487_Sarah _Lawrence .pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T13:25:38.147Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "22c85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4487_Sarah _Lawrence .pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (d4eddf42)
2025-07-28T14:25:38.3236409+01:00 801010d6-0003-c000-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 22c85c9e-cae7-6322-9971-ff0200979a84 (dea337dd)
2025-07-28T14:25:38.4923396+01:00 801010d6-0003-c000-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-28T14:25:38.5987009+01:00 801010d6-0003-c000-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-28T14:25:38.5987598+01:00 801010d6-0003-c000-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-28T14:25:39.2570151+01:00 801010d6-0003-c000-b63f-84710c7967bb [INF] Received HTTP response headers after 658.188ms - 500 (f0742c1f)
2025-07-28T14:25:39.2570878+01:00 801010d6-0003-c000-b63f-84710c7967bb [INF] End processing HTTP request after 658.4823ms - 500 (7656b38e)
2025-07-28T14:25:39.2609889+01:00 801010d6-0003-c000-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-28T14:25:39.2610610+01:00 801010d6-0003-c000-b63f-84710c7967bb [INF] (Webjob Application) Posted 22c85c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (689cb12b)
2025-07-28T14:25:39.5512298+01:00 801010d6-0003-c000-b63f-84710c7967bb [INF] (Webjob Application) Posted 22c85c9e-cae7-6322-9971-ff0200979a84  (81411545)
2025-07-28T14:25:40.6358898+01:00 80159381-0001-b100-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4487\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Lawrence \",\"email\":\"<EMAIL>\",\"phone\":\"07903569574\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Sw17 0ea\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"8\",\"Question\":\"What Postcode, Town or City would you like to work near? \",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"Tooting/Wandsworth \"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"10\",\"Question\":\"Are you registered with the GDC?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"11\",\"AnswerValue\":\"11\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"9\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"160037\"}],\"ParentQuestionId\":10}]},{\"QuestionId\":\"11\",\"Question\":\"The questions below are for those not yet GDC registered, if you are a qualified nurse please select 'none applicable' option. \\n\\nWould you be comfortable assisting with dental procedures?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"13\",\"AnswerValue\":\"13\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"12\",\"Question\":\"Are you able to commit to a 12-18 month training program?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"17\",\"AnswerValue\":\"17\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"14\",\"Question\":\"Please select the option that best describes you\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"19\",\"AnswerValue\":\"19\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"15\",\"Question\":\"If you are currently/already registered on a Dental Nursing course, please provide the following details:\\n\\nWhat is the date you started on your current programme?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"16\",\"Question\":\"Who is your current training provider?\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"17\",\"Question\":\"Please provide contact details for your current training provider\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"19\",\"Question\":\"What is your predicted completion date?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "18afff55-2268-4383-b591-8032da27dec8",
        "ChildItemId": "273f70f6-519a-40f9-a722-b63f8a6d4cc4",
        "ParentItemId": "22c85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4487_Sarah _Lawrence .pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T13:25:38.147Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "22c85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4487_Sarah _Lawrence .pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "22c85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "5997",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "************",
    "SubmissionTime": "2025-07-28T13:25:38.1298217Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4487\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Lawrence \",\"email\":\"<EMAIL>\",\"phone\":\"07903569574\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Sw17 0ea\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"8\",\"Question\":\"What Postcode, Town or City would you like to work near? \",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"Tooting/Wandsworth \"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"10\",\"Question\":\"Are you registered with the GDC?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"11\",\"AnswerValue\":\"11\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"9\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"160037\"}],\"ParentQuestionId\":10}]},{\"QuestionId\":\"11\",\"Question\":\"The questions below are for those not yet GDC registered, if you are a qualified nurse please select 'none applicable' option. \\n\\nWould you be comfortable assisting with dental procedures?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"13\",\"AnswerValue\":\"13\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"12\",\"Question\":\"Are you able to commit to a 12-18 month training program?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"17\",\"AnswerValue\":\"17\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"14\",\"Question\":\"Please select the option that best describes you\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"19\",\"AnswerValue\":\"19\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"15\",\"Question\":\"If you are currently/already registered on a Dental Nursing course, please provide the following details:\\n\\nWhat is the date you started on your current programme?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"16\",\"Question\":\"Who is your current training provider?\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"17\",\"Question\":\"Please provide contact details for your current training provider\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"19\",\"Question\":\"What is your predicted completion date?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "18afff55-2268-4383-b591-8032da27dec8",
            "ChildItemId": "273f70f6-519a-40f9-a722-b63f8a6d4cc4",
            "ParentItemId": "22c85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4487_Sarah _Lawrence .pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T13:25:38.147Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "22c85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4487_Sarah _Lawrence .pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (a449d086)
2025-07-28T14:25:40.6362764+01:00 80159381-0001-b100-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4487\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Lawrence \",\"email\":\"<EMAIL>\",\"phone\":\"07903569574\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Sw17 0ea\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"8\",\"Question\":\"What Postcode, Town or City would you like to work near? \",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"Tooting/Wandsworth \"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"10\",\"Question\":\"Are you registered with the GDC?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"11\",\"AnswerValue\":\"11\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"9\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"160037\"}],\"ParentQuestionId\":10}]},{\"QuestionId\":\"11\",\"Question\":\"The questions below are for those not yet GDC registered, if you are a qualified nurse please select 'none applicable' option. \\n\\nWould you be comfortable assisting with dental procedures?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"13\",\"AnswerValue\":\"13\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"12\",\"Question\":\"Are you able to commit to a 12-18 month training program?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"17\",\"AnswerValue\":\"17\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"14\",\"Question\":\"Please select the option that best describes you\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"19\",\"AnswerValue\":\"19\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"15\",\"Question\":\"If you are currently/already registered on a Dental Nursing course, please provide the following details:\\n\\nWhat is the date you started on your current programme?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"16\",\"Question\":\"Who is your current training provider?\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"17\",\"Question\":\"Please provide contact details for your current training provider\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"19\",\"Question\":\"What is your predicted completion date?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "18afff55-2268-4383-b591-8032da27dec8",
        "ChildItemId": "273f70f6-519a-40f9-a722-b63f8a6d4cc4",
        "ParentItemId": "22c85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4487_Sarah _Lawrence .pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T13:25:38.147Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "22c85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4487_Sarah _Lawrence .pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "22c85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "5997",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "************",
    "SubmissionTime": "2025-07-28T13:25:38.1298217Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4487\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Lawrence \",\"email\":\"<EMAIL>\",\"phone\":\"07903569574\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Sw17 0ea\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"8\",\"Question\":\"What Postcode, Town or City would you like to work near? \",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"Tooting/Wandsworth \"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"10\",\"Question\":\"Are you registered with the GDC?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"11\",\"AnswerValue\":\"11\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"9\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"160037\"}],\"ParentQuestionId\":10}]},{\"QuestionId\":\"11\",\"Question\":\"The questions below are for those not yet GDC registered, if you are a qualified nurse please select 'none applicable' option. \\n\\nWould you be comfortable assisting with dental procedures?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"13\",\"AnswerValue\":\"13\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"12\",\"Question\":\"Are you able to commit to a 12-18 month training program?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"17\",\"AnswerValue\":\"17\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"14\",\"Question\":\"Please select the option that best describes you\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"19\",\"AnswerValue\":\"19\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"15\",\"Question\":\"If you are currently/already registered on a Dental Nursing course, please provide the following details:\\n\\nWhat is the date you started on your current programme?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"16\",\"Question\":\"Who is your current training provider?\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"17\",\"Question\":\"Please provide contact details for your current training provider\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"19\",\"Question\":\"What is your predicted completion date?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "18afff55-2268-4383-b591-8032da27dec8",
            "ChildItemId": "273f70f6-519a-40f9-a722-b63f8a6d4cc4",
            "ParentItemId": "22c85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4487_Sarah _Lawrence .pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T13:25:38.147Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "22c85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4487_Sarah _Lawrence .pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (d4eddf42)
2025-07-28T14:25:40.6364513+01:00 80159381-0001-b100-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 22c85c9e-cae7-6322-9971-ff0200979a84 (dea337dd)
2025-07-28T14:25:40.8259504+01:00 80159381-0001-b100-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-28T14:25:40.9703585+01:00 80159381-0001-b100-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-28T14:25:40.9704072+01:00 80159381-0001-b100-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-28T14:25:41.6641418+01:00 80159381-0001-b100-b63f-84710c7967bb [INF] Received HTTP response headers after 693.6509ms - 500 (f0742c1f)
2025-07-28T14:25:41.6642199+01:00 80159381-0001-b100-b63f-84710c7967bb [INF] End processing HTTP request after 693.9388ms - 500 (7656b38e)
2025-07-28T14:25:41.6645478+01:00 80159381-0001-b100-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-28T14:25:41.6645861+01:00 80159381-0001-b100-b63f-84710c7967bb [INF] (Webjob Application) Posted 22c85c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (689cb12b)
2025-07-28T14:25:41.8698688+01:00 80159381-0001-b100-b63f-84710c7967bb [INF] (Webjob Application) Posted 22c85c9e-cae7-6322-9971-ff0200979a84  (81411545)
2025-07-28T14:25:45.9298137+01:00 801c971b-0003-ba00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4487\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Lawrence \",\"email\":\"<EMAIL>\",\"phone\":\"07903569574\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Sw17 0ea\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"8\",\"Question\":\"What Postcode, Town or City would you like to work near? \",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"Tooting/Wandsworth \"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"10\",\"Question\":\"Are you registered with the GDC?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"11\",\"AnswerValue\":\"11\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"9\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"160037\"}],\"ParentQuestionId\":10}]},{\"QuestionId\":\"11\",\"Question\":\"The questions below are for those not yet GDC registered, if you are a qualified nurse please select 'none applicable' option. \\n\\nWould you be comfortable assisting with dental procedures?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"13\",\"AnswerValue\":\"13\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"12\",\"Question\":\"Are you able to commit to a 12-18 month training program?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"17\",\"AnswerValue\":\"17\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"14\",\"Question\":\"Please select the option that best describes you\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"19\",\"AnswerValue\":\"19\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"15\",\"Question\":\"If you are currently/already registered on a Dental Nursing course, please provide the following details:\\n\\nWhat is the date you started on your current programme?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"16\",\"Question\":\"Who is your current training provider?\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"17\",\"Question\":\"Please provide contact details for your current training provider\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"19\",\"Question\":\"What is your predicted completion date?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "18afff55-2268-4383-b591-8032da27dec8",
        "ChildItemId": "273f70f6-519a-40f9-a722-b63f8a6d4cc4",
        "ParentItemId": "22c85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4487_Sarah _Lawrence .pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T13:25:38.147Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "22c85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4487_Sarah _Lawrence .pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "22c85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "5997",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "************",
    "SubmissionTime": "2025-07-28T13:25:38.1298217Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4487\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Lawrence \",\"email\":\"<EMAIL>\",\"phone\":\"07903569574\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Sw17 0ea\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"8\",\"Question\":\"What Postcode, Town or City would you like to work near? \",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"Tooting/Wandsworth \"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"10\",\"Question\":\"Are you registered with the GDC?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"11\",\"AnswerValue\":\"11\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"9\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"160037\"}],\"ParentQuestionId\":10}]},{\"QuestionId\":\"11\",\"Question\":\"The questions below are for those not yet GDC registered, if you are a qualified nurse please select 'none applicable' option. \\n\\nWould you be comfortable assisting with dental procedures?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"13\",\"AnswerValue\":\"13\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"12\",\"Question\":\"Are you able to commit to a 12-18 month training program?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"17\",\"AnswerValue\":\"17\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"14\",\"Question\":\"Please select the option that best describes you\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"19\",\"AnswerValue\":\"19\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"15\",\"Question\":\"If you are currently/already registered on a Dental Nursing course, please provide the following details:\\n\\nWhat is the date you started on your current programme?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"16\",\"Question\":\"Who is your current training provider?\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"17\",\"Question\":\"Please provide contact details for your current training provider\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"19\",\"Question\":\"What is your predicted completion date?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "18afff55-2268-4383-b591-8032da27dec8",
            "ChildItemId": "273f70f6-519a-40f9-a722-b63f8a6d4cc4",
            "ParentItemId": "22c85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4487_Sarah _Lawrence .pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T13:25:38.147Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "22c85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4487_Sarah _Lawrence .pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (a449d086)
2025-07-28T14:25:45.9302129+01:00 801c971b-0003-ba00-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4487\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Lawrence \",\"email\":\"<EMAIL>\",\"phone\":\"07903569574\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Sw17 0ea\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"8\",\"Question\":\"What Postcode, Town or City would you like to work near? \",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"Tooting/Wandsworth \"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"10\",\"Question\":\"Are you registered with the GDC?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"11\",\"AnswerValue\":\"11\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"9\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"160037\"}],\"ParentQuestionId\":10}]},{\"QuestionId\":\"11\",\"Question\":\"The questions below are for those not yet GDC registered, if you are a qualified nurse please select 'none applicable' option. \\n\\nWould you be comfortable assisting with dental procedures?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"13\",\"AnswerValue\":\"13\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"12\",\"Question\":\"Are you able to commit to a 12-18 month training program?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"17\",\"AnswerValue\":\"17\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"14\",\"Question\":\"Please select the option that best describes you\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"19\",\"AnswerValue\":\"19\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"15\",\"Question\":\"If you are currently/already registered on a Dental Nursing course, please provide the following details:\\n\\nWhat is the date you started on your current programme?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"16\",\"Question\":\"Who is your current training provider?\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"17\",\"Question\":\"Please provide contact details for your current training provider\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"19\",\"Question\":\"What is your predicted completion date?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "18afff55-2268-4383-b591-8032da27dec8",
        "ChildItemId": "273f70f6-519a-40f9-a722-b63f8a6d4cc4",
        "ParentItemId": "22c85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4487_Sarah _Lawrence .pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T13:25:38.147Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "22c85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4487_Sarah _Lawrence .pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "22c85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "5997",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "************",
    "SubmissionTime": "2025-07-28T13:25:38.1298217Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4487\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Lawrence \",\"email\":\"<EMAIL>\",\"phone\":\"07903569574\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Sw17 0ea\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"8\",\"Question\":\"What Postcode, Town or City would you like to work near? \",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"Tooting/Wandsworth \"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"10\",\"Question\":\"Are you registered with the GDC?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"11\",\"AnswerValue\":\"11\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"9\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"160037\"}],\"ParentQuestionId\":10}]},{\"QuestionId\":\"11\",\"Question\":\"The questions below are for those not yet GDC registered, if you are a qualified nurse please select 'none applicable' option. \\n\\nWould you be comfortable assisting with dental procedures?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"13\",\"AnswerValue\":\"13\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"12\",\"Question\":\"Are you able to commit to a 12-18 month training program?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"17\",\"AnswerValue\":\"17\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"14\",\"Question\":\"Please select the option that best describes you\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"19\",\"AnswerValue\":\"19\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"15\",\"Question\":\"If you are currently/already registered on a Dental Nursing course, please provide the following details:\\n\\nWhat is the date you started on your current programme?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"16\",\"Question\":\"Who is your current training provider?\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"17\",\"Question\":\"Please provide contact details for your current training provider\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"19\",\"Question\":\"What is your predicted completion date?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "18afff55-2268-4383-b591-8032da27dec8",
            "ChildItemId": "273f70f6-519a-40f9-a722-b63f8a6d4cc4",
            "ParentItemId": "22c85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4487_Sarah _Lawrence .pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T13:25:38.147Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "22c85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4487_Sarah _Lawrence .pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (d4eddf42)
2025-07-28T14:25:45.9303896+01:00 801c971b-0003-ba00-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 22c85c9e-cae7-6322-9971-ff0200979a84 (dea337dd)
2025-07-28T14:25:46.1335286+01:00 801c971b-0003-ba00-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-28T14:25:46.2070287+01:00 801c971b-0003-ba00-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-28T14:25:46.2070688+01:00 801c971b-0003-ba00-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-28T14:25:47.0536745+01:00 801c971b-0003-ba00-b63f-84710c7967bb [INF] Received HTTP response headers after 846.544ms - 500 (f0742c1f)
2025-07-28T14:25:47.0537207+01:00 801c971b-0003-ba00-b63f-84710c7967bb [INF] End processing HTTP request after 846.7618ms - 500 (7656b38e)
2025-07-28T14:25:47.0540130+01:00 801c971b-0003-ba00-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-28T14:25:47.0540478+01:00 801c971b-0003-ba00-b63f-84710c7967bb [INF] (Webjob Application) Posted 22c85c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (689cb12b)
2025-07-28T14:25:47.2479648+01:00 801c971b-0003-ba00-b63f-84710c7967bb [INF] (Webjob Application) Posted 22c85c9e-cae7-6322-9971-ff0200979a84  (81411545)
2025-07-28T14:25:56.3236348+01:00 801593cd-0001-b100-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4487\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Lawrence \",\"email\":\"<EMAIL>\",\"phone\":\"07903569574\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Sw17 0ea\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"8\",\"Question\":\"What Postcode, Town or City would you like to work near? \",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"Tooting/Wandsworth \"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"10\",\"Question\":\"Are you registered with the GDC?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"11\",\"AnswerValue\":\"11\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"9\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"160037\"}],\"ParentQuestionId\":10}]},{\"QuestionId\":\"11\",\"Question\":\"The questions below are for those not yet GDC registered, if you are a qualified nurse please select 'none applicable' option. \\n\\nWould you be comfortable assisting with dental procedures?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"13\",\"AnswerValue\":\"13\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"12\",\"Question\":\"Are you able to commit to a 12-18 month training program?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"17\",\"AnswerValue\":\"17\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"14\",\"Question\":\"Please select the option that best describes you\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"19\",\"AnswerValue\":\"19\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"15\",\"Question\":\"If you are currently/already registered on a Dental Nursing course, please provide the following details:\\n\\nWhat is the date you started on your current programme?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"16\",\"Question\":\"Who is your current training provider?\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"17\",\"Question\":\"Please provide contact details for your current training provider\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"19\",\"Question\":\"What is your predicted completion date?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "18afff55-2268-4383-b591-8032da27dec8",
        "ChildItemId": "273f70f6-519a-40f9-a722-b63f8a6d4cc4",
        "ParentItemId": "22c85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4487_Sarah _Lawrence .pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T13:25:38.147Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "22c85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4487_Sarah _Lawrence .pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "22c85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "5997",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "************",
    "SubmissionTime": "2025-07-28T13:25:38.1298217Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4487\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Lawrence \",\"email\":\"<EMAIL>\",\"phone\":\"07903569574\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Sw17 0ea\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"8\",\"Question\":\"What Postcode, Town or City would you like to work near? \",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"Tooting/Wandsworth \"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"10\",\"Question\":\"Are you registered with the GDC?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"11\",\"AnswerValue\":\"11\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"9\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"160037\"}],\"ParentQuestionId\":10}]},{\"QuestionId\":\"11\",\"Question\":\"The questions below are for those not yet GDC registered, if you are a qualified nurse please select 'none applicable' option. \\n\\nWould you be comfortable assisting with dental procedures?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"13\",\"AnswerValue\":\"13\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"12\",\"Question\":\"Are you able to commit to a 12-18 month training program?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"17\",\"AnswerValue\":\"17\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"14\",\"Question\":\"Please select the option that best describes you\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"19\",\"AnswerValue\":\"19\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"15\",\"Question\":\"If you are currently/already registered on a Dental Nursing course, please provide the following details:\\n\\nWhat is the date you started on your current programme?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"16\",\"Question\":\"Who is your current training provider?\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"17\",\"Question\":\"Please provide contact details for your current training provider\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"19\",\"Question\":\"What is your predicted completion date?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "18afff55-2268-4383-b591-8032da27dec8",
            "ChildItemId": "273f70f6-519a-40f9-a722-b63f8a6d4cc4",
            "ParentItemId": "22c85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4487_Sarah _Lawrence .pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T13:25:38.147Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "22c85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4487_Sarah _Lawrence .pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (a449d086)
2025-07-28T14:25:56.3240027+01:00 801593cd-0001-b100-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4487\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Lawrence \",\"email\":\"<EMAIL>\",\"phone\":\"07903569574\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Sw17 0ea\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"8\",\"Question\":\"What Postcode, Town or City would you like to work near? \",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"Tooting/Wandsworth \"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"10\",\"Question\":\"Are you registered with the GDC?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"11\",\"AnswerValue\":\"11\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"9\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"160037\"}],\"ParentQuestionId\":10}]},{\"QuestionId\":\"11\",\"Question\":\"The questions below are for those not yet GDC registered, if you are a qualified nurse please select 'none applicable' option. \\n\\nWould you be comfortable assisting with dental procedures?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"13\",\"AnswerValue\":\"13\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"12\",\"Question\":\"Are you able to commit to a 12-18 month training program?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"17\",\"AnswerValue\":\"17\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"14\",\"Question\":\"Please select the option that best describes you\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"19\",\"AnswerValue\":\"19\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"15\",\"Question\":\"If you are currently/already registered on a Dental Nursing course, please provide the following details:\\n\\nWhat is the date you started on your current programme?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"16\",\"Question\":\"Who is your current training provider?\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"17\",\"Question\":\"Please provide contact details for your current training provider\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"19\",\"Question\":\"What is your predicted completion date?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "18afff55-2268-4383-b591-8032da27dec8",
        "ChildItemId": "273f70f6-519a-40f9-a722-b63f8a6d4cc4",
        "ParentItemId": "22c85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4487_Sarah _Lawrence .pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T13:25:38.147Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "22c85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4487_Sarah _Lawrence .pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "22c85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "5997",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "************",
    "SubmissionTime": "2025-07-28T13:25:38.1298217Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4487\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Lawrence \",\"email\":\"<EMAIL>\",\"phone\":\"07903569574\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Sw17 0ea\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"8\",\"Question\":\"What Postcode, Town or City would you like to work near? \",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"Tooting/Wandsworth \"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"10\",\"Question\":\"Are you registered with the GDC?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"11\",\"AnswerValue\":\"11\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"9\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"160037\"}],\"ParentQuestionId\":10}]},{\"QuestionId\":\"11\",\"Question\":\"The questions below are for those not yet GDC registered, if you are a qualified nurse please select 'none applicable' option. \\n\\nWould you be comfortable assisting with dental procedures?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"13\",\"AnswerValue\":\"13\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"12\",\"Question\":\"Are you able to commit to a 12-18 month training program?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"17\",\"AnswerValue\":\"17\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"14\",\"Question\":\"Please select the option that best describes you\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"19\",\"AnswerValue\":\"19\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"15\",\"Question\":\"If you are currently/already registered on a Dental Nursing course, please provide the following details:\\n\\nWhat is the date you started on your current programme?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"16\",\"Question\":\"Who is your current training provider?\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"17\",\"Question\":\"Please provide contact details for your current training provider\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"19\",\"Question\":\"What is your predicted completion date?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "18afff55-2268-4383-b591-8032da27dec8",
            "ChildItemId": "273f70f6-519a-40f9-a722-b63f8a6d4cc4",
            "ParentItemId": "22c85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4487_Sarah _Lawrence .pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T13:25:38.147Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "22c85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4487_Sarah _Lawrence .pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (d4eddf42)
2025-07-28T14:25:56.3241276+01:00 801593cd-0001-b100-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 22c85c9e-cae7-6322-9971-ff0200979a84 (dea337dd)
2025-07-28T14:25:56.4827506+01:00 801593cd-0001-b100-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-28T14:25:56.5657107+01:00 801593cd-0001-b100-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-28T14:25:56.5657437+01:00 801593cd-0001-b100-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-28T14:25:57.1866737+01:00 801593cd-0001-b100-b63f-84710c7967bb [INF] Received HTTP response headers after 620.8726ms - 500 (f0742c1f)
2025-07-28T14:25:57.1867177+01:00 801593cd-0001-b100-b63f-84710c7967bb [INF] End processing HTTP request after 621.0592ms - 500 (7656b38e)
2025-07-28T14:25:57.1870295+01:00 801593cd-0001-b100-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-28T14:25:57.1870744+01:00 801593cd-0001-b100-b63f-84710c7967bb [INF] (Webjob Application) Posted 22c85c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (689cb12b)
2025-07-28T14:25:57.3525102+01:00 801593cd-0001-b100-b63f-84710c7967bb [INF] (Webjob Application) Posted 22c85c9e-cae7-6322-9971-ff0200979a84  (81411545)
2025-07-28T14:26:13.4102304+01:00 800efa98-0002-e100-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4487\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Lawrence \",\"email\":\"<EMAIL>\",\"phone\":\"07903569574\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Sw17 0ea\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"8\",\"Question\":\"What Postcode, Town or City would you like to work near? \",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"Tooting/Wandsworth \"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"10\",\"Question\":\"Are you registered with the GDC?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"11\",\"AnswerValue\":\"11\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"9\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"160037\"}],\"ParentQuestionId\":10}]},{\"QuestionId\":\"11\",\"Question\":\"The questions below are for those not yet GDC registered, if you are a qualified nurse please select 'none applicable' option. \\n\\nWould you be comfortable assisting with dental procedures?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"13\",\"AnswerValue\":\"13\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"12\",\"Question\":\"Are you able to commit to a 12-18 month training program?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"17\",\"AnswerValue\":\"17\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"14\",\"Question\":\"Please select the option that best describes you\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"19\",\"AnswerValue\":\"19\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"15\",\"Question\":\"If you are currently/already registered on a Dental Nursing course, please provide the following details:\\n\\nWhat is the date you started on your current programme?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"16\",\"Question\":\"Who is your current training provider?\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"17\",\"Question\":\"Please provide contact details for your current training provider\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"19\",\"Question\":\"What is your predicted completion date?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "18afff55-2268-4383-b591-8032da27dec8",
        "ChildItemId": "273f70f6-519a-40f9-a722-b63f8a6d4cc4",
        "ParentItemId": "22c85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4487_Sarah _Lawrence .pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T13:25:38.147Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "22c85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4487_Sarah _Lawrence .pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "22c85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "5997",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "************",
    "SubmissionTime": "2025-07-28T13:25:38.1298217Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4487\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Lawrence \",\"email\":\"<EMAIL>\",\"phone\":\"07903569574\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Sw17 0ea\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"8\",\"Question\":\"What Postcode, Town or City would you like to work near? \",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"Tooting/Wandsworth \"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"10\",\"Question\":\"Are you registered with the GDC?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"11\",\"AnswerValue\":\"11\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"9\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"160037\"}],\"ParentQuestionId\":10}]},{\"QuestionId\":\"11\",\"Question\":\"The questions below are for those not yet GDC registered, if you are a qualified nurse please select 'none applicable' option. \\n\\nWould you be comfortable assisting with dental procedures?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"13\",\"AnswerValue\":\"13\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"12\",\"Question\":\"Are you able to commit to a 12-18 month training program?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"17\",\"AnswerValue\":\"17\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"14\",\"Question\":\"Please select the option that best describes you\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"19\",\"AnswerValue\":\"19\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"15\",\"Question\":\"If you are currently/already registered on a Dental Nursing course, please provide the following details:\\n\\nWhat is the date you started on your current programme?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"16\",\"Question\":\"Who is your current training provider?\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"17\",\"Question\":\"Please provide contact details for your current training provider\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"19\",\"Question\":\"What is your predicted completion date?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "18afff55-2268-4383-b591-8032da27dec8",
            "ChildItemId": "273f70f6-519a-40f9-a722-b63f8a6d4cc4",
            "ParentItemId": "22c85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4487_Sarah _Lawrence .pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T13:25:38.147Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "22c85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4487_Sarah _Lawrence .pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (a449d086)
2025-07-28T14:26:13.4139796+01:00 800efa98-0002-e100-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4487\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Lawrence \",\"email\":\"<EMAIL>\",\"phone\":\"07903569574\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Sw17 0ea\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"8\",\"Question\":\"What Postcode, Town or City would you like to work near? \",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"Tooting/Wandsworth \"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"10\",\"Question\":\"Are you registered with the GDC?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"11\",\"AnswerValue\":\"11\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"9\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"160037\"}],\"ParentQuestionId\":10}]},{\"QuestionId\":\"11\",\"Question\":\"The questions below are for those not yet GDC registered, if you are a qualified nurse please select 'none applicable' option. \\n\\nWould you be comfortable assisting with dental procedures?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"13\",\"AnswerValue\":\"13\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"12\",\"Question\":\"Are you able to commit to a 12-18 month training program?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"17\",\"AnswerValue\":\"17\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"14\",\"Question\":\"Please select the option that best describes you\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"19\",\"AnswerValue\":\"19\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"15\",\"Question\":\"If you are currently/already registered on a Dental Nursing course, please provide the following details:\\n\\nWhat is the date you started on your current programme?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"16\",\"Question\":\"Who is your current training provider?\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"17\",\"Question\":\"Please provide contact details for your current training provider\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"19\",\"Question\":\"What is your predicted completion date?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "18afff55-2268-4383-b591-8032da27dec8",
        "ChildItemId": "273f70f6-519a-40f9-a722-b63f8a6d4cc4",
        "ParentItemId": "22c85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4487_Sarah _Lawrence .pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T13:25:38.147Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "22c85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4487_Sarah _Lawrence .pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "22c85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "5997",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "************",
    "SubmissionTime": "2025-07-28T13:25:38.1298217Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4487\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sarah \",\"lastName\":\"Lawrence \",\"email\":\"<EMAIL>\",\"phone\":\"07903569574\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Sw17 0ea\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"8\",\"Question\":\"What Postcode, Town or City would you like to work near? \",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"Tooting/Wandsworth \"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"10\",\"Question\":\"Are you registered with the GDC?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"11\",\"AnswerValue\":\"11\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"9\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"160037\"}],\"ParentQuestionId\":10}]},{\"QuestionId\":\"11\",\"Question\":\"The questions below are for those not yet GDC registered, if you are a qualified nurse please select 'none applicable' option. \\n\\nWould you be comfortable assisting with dental procedures?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"13\",\"AnswerValue\":\"13\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"12\",\"Question\":\"Are you able to commit to a 12-18 month training program?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"17\",\"AnswerValue\":\"17\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"14\",\"Question\":\"Please select the option that best describes you\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"19\",\"AnswerValue\":\"19\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"15\",\"Question\":\"If you are currently/already registered on a Dental Nursing course, please provide the following details:\\n\\nWhat is the date you started on your current programme?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"16\",\"Question\":\"Who is your current training provider?\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"17\",\"Question\":\"Please provide contact details for your current training provider\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"N/A\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"19\",\"Question\":\"What is your predicted completion date?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "18afff55-2268-4383-b591-8032da27dec8",
            "ChildItemId": "273f70f6-519a-40f9-a722-b63f8a6d4cc4",
            "ParentItemId": "22c85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4487_Sarah _Lawrence .pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T13:25:38.147Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "22c85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4487_Sarah _Lawrence .pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (d4eddf42)
2025-07-28T14:26:13.4141347+01:00 800efa98-0002-e100-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 22c85c9e-cae7-6322-9971-ff0200979a84 (dea337dd)
2025-07-28T14:26:13.6177565+01:00 800efa98-0002-e100-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-28T14:26:13.8013170+01:00 800efa98-0002-e100-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-28T14:26:13.8013846+01:00 800efa98-0002-e100-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-28T14:26:14.4590030+01:00 800efa98-0002-e100-b63f-84710c7967bb [INF] Received HTTP response headers after 657.5134ms - 500 (f0742c1f)
2025-07-28T14:26:14.4590536+01:00 800efa98-0002-e100-b63f-84710c7967bb [INF] End processing HTTP request after 657.8766ms - 500 (7656b38e)
2025-07-28T14:26:14.4597246+01:00 800efa98-0002-e100-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-28T14:26:14.4598101+01:00 800efa98-0002-e100-b63f-84710c7967bb [INF] (Webjob Application) Posted 22c85c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (689cb12b)
2025-07-28T14:26:14.6747418+01:00 800efa98-0002-e100-b63f-84710c7967bb [INF] (Webjob Application) Posted 22c85c9e-cae7-6322-9971-ff0200979a84  (81411545)
2025-07-28T16:26:35.3803894+01:00  [WRN] Using an in-memory repository. Keys will not be persisted to storage. (28e83010)
2025-07-28T16:26:35.3835748+01:00  [WRN] Neither user profile nor HKLM registry available. Using an ephemeral key repository. Protected data will be unavailable when application exits. (54f66960)
2025-07-28T16:26:35.4224625+01:00  [WRN] No XML encryptor configured. Key {b87d27e3-59e9-40ff-b4e6-28c0511bbb2d} may be persisted to storage in unencrypted form. (9ca7e61e)
2025-07-28T16:26:35.5023276+01:00 80266191-0001-9600-b63f-84710c7967bb [WRN] Failed to determine the https port for redirect. (ca76cc21)
2025-07-28T16:26:35.8662394+01:00 80266191-0001-9600-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4401\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Katie\",\"lastName\":\"Cockburn\",\"email\":\"<EMAIL>\",\"phone\":\"07766646001\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"BN112QG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
    "FileFieldController": [
      {
        "Id": "b136415e-2420-411b-bddd-e32436d7d53c",
        "ChildItemId": "75343c35-1af0-46e7-84a4-db6861f354c0",
        "ParentItemId": "f9c85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4401_Katie_Cockburn.2025.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T15:26:34.257Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "f9c85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4401_Katie_Cockburn.2025.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "f9c85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "5998",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-28T15:26:34.2403794Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4401\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Katie\",\"lastName\":\"Cockburn\",\"email\":\"<EMAIL>\",\"phone\":\"07766646001\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"BN112QG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "b136415e-2420-411b-bddd-e32436d7d53c",
            "ChildItemId": "75343c35-1af0-46e7-84a4-db6861f354c0",
            "ParentItemId": "f9c85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4401_Katie_Cockburn.2025.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T15:26:34.257Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "f9c85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4401_Katie_Cockburn.2025.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (9592677d)
2025-07-28T16:26:35.9584725+01:00 80266191-0001-9600-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4401\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Katie\",\"lastName\":\"Cockburn\",\"email\":\"<EMAIL>\",\"phone\":\"07766646001\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"BN112QG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
    "FileFieldController": [
      {
        "Id": "b136415e-2420-411b-bddd-e32436d7d53c",
        "ChildItemId": "75343c35-1af0-46e7-84a4-db6861f354c0",
        "ParentItemId": "f9c85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4401_Katie_Cockburn.2025.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T15:26:34.257Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "f9c85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4401_Katie_Cockburn.2025.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "f9c85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "5998",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-28T15:26:34.2403794Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4401\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Katie\",\"lastName\":\"Cockburn\",\"email\":\"<EMAIL>\",\"phone\":\"07766646001\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"BN112QG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "b136415e-2420-411b-bddd-e32436d7d53c",
            "ChildItemId": "75343c35-1af0-46e7-84a4-db6861f354c0",
            "ParentItemId": "f9c85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4401_Katie_Cockburn.2025.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T15:26:34.257Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "f9c85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4401_Katie_Cockburn.2025.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (cabb0023)
2025-07-28T16:26:36.0452118+01:00 80266191-0001-9600-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId f9c85c9e-cae7-6322-9971-ff0200979a84 (c6fddb90)
2025-07-28T16:26:36.4270980+01:00 80266191-0001-9600-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-28T16:26:37.1423956+01:00 80266191-0001-9600-b63f-84710c7967bb [ERR] Error processing CV: Response status code does not indicate success: 404 (Not Found). (db13d13f)
2025-07-28T16:26:37.1428186+01:00 80266191-0001-9600-b63f-84710c7967bb [INF] (Webjob Application) Posted f9c85c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (447c7274)
2025-07-28T16:26:39.7509234+01:00 80266191-0001-9600-b63f-84710c7967bb [INF] (Webjob Application) Posted f9c85c9e-cae7-6322-9971-ff0200979a84  (f8e654dc)
2025-07-28T16:26:40.8341564+01:00 80105492-0003-c000-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4401\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Katie\",\"lastName\":\"Cockburn\",\"email\":\"<EMAIL>\",\"phone\":\"07766646001\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"BN112QG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
    "FileFieldController": [
      {
        "Id": "b136415e-2420-411b-bddd-e32436d7d53c",
        "ChildItemId": "75343c35-1af0-46e7-84a4-db6861f354c0",
        "ParentItemId": "f9c85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4401_Katie_Cockburn.2025.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T15:26:34.257Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "f9c85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4401_Katie_Cockburn.2025.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "f9c85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "5998",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-28T15:26:34.2403794Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4401\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Katie\",\"lastName\":\"Cockburn\",\"email\":\"<EMAIL>\",\"phone\":\"07766646001\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"BN112QG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "b136415e-2420-411b-bddd-e32436d7d53c",
            "ChildItemId": "75343c35-1af0-46e7-84a4-db6861f354c0",
            "ParentItemId": "f9c85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4401_Katie_Cockburn.2025.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T15:26:34.257Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "f9c85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4401_Katie_Cockburn.2025.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (9592677d)
2025-07-28T16:26:40.8410150+01:00 80105492-0003-c000-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4401\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Katie\",\"lastName\":\"Cockburn\",\"email\":\"<EMAIL>\",\"phone\":\"07766646001\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"BN112QG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
    "FileFieldController": [
      {
        "Id": "b136415e-2420-411b-bddd-e32436d7d53c",
        "ChildItemId": "75343c35-1af0-46e7-84a4-db6861f354c0",
        "ParentItemId": "f9c85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4401_Katie_Cockburn.2025.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T15:26:34.257Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "f9c85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4401_Katie_Cockburn.2025.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "f9c85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "5998",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-28T15:26:34.2403794Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4401\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Katie\",\"lastName\":\"Cockburn\",\"email\":\"<EMAIL>\",\"phone\":\"07766646001\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"BN112QG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "b136415e-2420-411b-bddd-e32436d7d53c",
            "ChildItemId": "75343c35-1af0-46e7-84a4-db6861f354c0",
            "ParentItemId": "f9c85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4401_Katie_Cockburn.2025.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T15:26:34.257Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "f9c85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4401_Katie_Cockburn.2025.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (cabb0023)
2025-07-28T16:26:40.8412445+01:00 80105492-0003-c000-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId f9c85c9e-cae7-6322-9971-ff0200979a84 (c6fddb90)
2025-07-28T16:26:41.0250678+01:00 80105492-0003-c000-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-28T16:26:41.4077541+01:00 80105492-0003-c000-b63f-84710c7967bb [ERR] Error processing CV: Response status code does not indicate success: 404 (Not Found). (db13d13f)
2025-07-28T16:26:41.4078481+01:00 80105492-0003-c000-b63f-84710c7967bb [INF] (Webjob Application) Posted f9c85c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (447c7274)
2025-07-28T16:26:41.4741664+01:00 80105492-0003-c000-b63f-84710c7967bb [INF] (Webjob Application) Posted f9c85c9e-cae7-6322-9971-ff0200979a84  (f8e654dc)
2025-07-28T16:26:45.5690968+01:00 801054a6-0003-c000-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4401\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Katie\",\"lastName\":\"Cockburn\",\"email\":\"<EMAIL>\",\"phone\":\"07766646001\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"BN112QG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
    "FileFieldController": [
      {
        "Id": "b136415e-2420-411b-bddd-e32436d7d53c",
        "ChildItemId": "75343c35-1af0-46e7-84a4-db6861f354c0",
        "ParentItemId": "f9c85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4401_Katie_Cockburn.2025.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T15:26:34.257Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "f9c85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4401_Katie_Cockburn.2025.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "f9c85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "5998",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-28T15:26:34.2403794Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4401\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Katie\",\"lastName\":\"Cockburn\",\"email\":\"<EMAIL>\",\"phone\":\"07766646001\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"BN112QG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "b136415e-2420-411b-bddd-e32436d7d53c",
            "ChildItemId": "75343c35-1af0-46e7-84a4-db6861f354c0",
            "ParentItemId": "f9c85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4401_Katie_Cockburn.2025.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T15:26:34.257Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "f9c85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4401_Katie_Cockburn.2025.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (9592677d)
2025-07-28T16:26:45.5694075+01:00 801054a6-0003-c000-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4401\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Katie\",\"lastName\":\"Cockburn\",\"email\":\"<EMAIL>\",\"phone\":\"07766646001\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"BN112QG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
    "FileFieldController": [
      {
        "Id": "b136415e-2420-411b-bddd-e32436d7d53c",
        "ChildItemId": "75343c35-1af0-46e7-84a4-db6861f354c0",
        "ParentItemId": "f9c85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4401_Katie_Cockburn.2025.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T15:26:34.257Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "f9c85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4401_Katie_Cockburn.2025.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "f9c85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "5998",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-28T15:26:34.2403794Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4401\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Katie\",\"lastName\":\"Cockburn\",\"email\":\"<EMAIL>\",\"phone\":\"07766646001\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"BN112QG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "b136415e-2420-411b-bddd-e32436d7d53c",
            "ChildItemId": "75343c35-1af0-46e7-84a4-db6861f354c0",
            "ParentItemId": "f9c85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4401_Katie_Cockburn.2025.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T15:26:34.257Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "f9c85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4401_Katie_Cockburn.2025.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (cabb0023)
2025-07-28T16:26:45.5695706+01:00 801054a6-0003-c000-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId f9c85c9e-cae7-6322-9971-ff0200979a84 (c6fddb90)
2025-07-28T16:26:45.7393113+01:00 801054a6-0003-c000-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-28T16:26:46.0613276+01:00 801054a6-0003-c000-b63f-84710c7967bb [ERR] Error processing CV: Response status code does not indicate success: 404 (Not Found). (db13d13f)
2025-07-28T16:26:46.0613989+01:00 801054a6-0003-c000-b63f-84710c7967bb [INF] (Webjob Application) Posted f9c85c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (447c7274)
2025-07-28T16:26:46.1239981+01:00 801054a6-0003-c000-b63f-84710c7967bb [INF] (Webjob Application) Posted f9c85c9e-cae7-6322-9971-ff0200979a84  (f8e654dc)
2025-07-28T16:26:55.1600304+01:00 8028b51d-0000-cc00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4401\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Katie\",\"lastName\":\"Cockburn\",\"email\":\"<EMAIL>\",\"phone\":\"07766646001\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"BN112QG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
    "FileFieldController": [
      {
        "Id": "b136415e-2420-411b-bddd-e32436d7d53c",
        "ChildItemId": "75343c35-1af0-46e7-84a4-db6861f354c0",
        "ParentItemId": "f9c85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4401_Katie_Cockburn.2025.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T15:26:34.257Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "f9c85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4401_Katie_Cockburn.2025.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "f9c85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "5998",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-28T15:26:34.2403794Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4401\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Katie\",\"lastName\":\"Cockburn\",\"email\":\"<EMAIL>\",\"phone\":\"07766646001\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"BN112QG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "b136415e-2420-411b-bddd-e32436d7d53c",
            "ChildItemId": "75343c35-1af0-46e7-84a4-db6861f354c0",
            "ParentItemId": "f9c85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4401_Katie_Cockburn.2025.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T15:26:34.257Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "f9c85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4401_Katie_Cockburn.2025.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (9592677d)
2025-07-28T16:26:55.1603834+01:00 8028b51d-0000-cc00-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4401\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Katie\",\"lastName\":\"Cockburn\",\"email\":\"<EMAIL>\",\"phone\":\"07766646001\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"BN112QG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
    "FileFieldController": [
      {
        "Id": "b136415e-2420-411b-bddd-e32436d7d53c",
        "ChildItemId": "75343c35-1af0-46e7-84a4-db6861f354c0",
        "ParentItemId": "f9c85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4401_Katie_Cockburn.2025.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T15:26:34.257Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "f9c85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4401_Katie_Cockburn.2025.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "f9c85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "5998",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-28T15:26:34.2403794Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4401\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Katie\",\"lastName\":\"Cockburn\",\"email\":\"<EMAIL>\",\"phone\":\"07766646001\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"BN112QG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "b136415e-2420-411b-bddd-e32436d7d53c",
            "ChildItemId": "75343c35-1af0-46e7-84a4-db6861f354c0",
            "ParentItemId": "f9c85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4401_Katie_Cockburn.2025.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T15:26:34.257Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "f9c85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4401_Katie_Cockburn.2025.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (cabb0023)
2025-07-28T16:26:55.1605215+01:00 8028b51d-0000-cc00-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId f9c85c9e-cae7-6322-9971-ff0200979a84 (c6fddb90)
2025-07-28T16:26:55.3656324+01:00 8028b51d-0000-cc00-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-28T16:26:55.7756513+01:00 8028b51d-0000-cc00-b63f-84710c7967bb [ERR] Error processing CV: Response status code does not indicate success: 404 (Not Found). (db13d13f)
2025-07-28T16:26:55.7757284+01:00 8028b51d-0000-cc00-b63f-84710c7967bb [INF] (Webjob Application) Posted f9c85c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (447c7274)
2025-07-28T16:26:55.8645831+01:00 8028b51d-0000-cc00-b63f-84710c7967bb [INF] (Webjob Application) Posted f9c85c9e-cae7-6322-9971-ff0200979a84  (f8e654dc)
2025-07-28T16:27:11.9011017+01:00 80053f70-0002-7d00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4401\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Katie\",\"lastName\":\"Cockburn\",\"email\":\"<EMAIL>\",\"phone\":\"07766646001\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"BN112QG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
    "FileFieldController": [
      {
        "Id": "b136415e-2420-411b-bddd-e32436d7d53c",
        "ChildItemId": "75343c35-1af0-46e7-84a4-db6861f354c0",
        "ParentItemId": "f9c85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4401_Katie_Cockburn.2025.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T15:26:34.257Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "f9c85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4401_Katie_Cockburn.2025.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "f9c85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "5998",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-28T15:26:34.2403794Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4401\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Katie\",\"lastName\":\"Cockburn\",\"email\":\"<EMAIL>\",\"phone\":\"07766646001\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"BN112QG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "b136415e-2420-411b-bddd-e32436d7d53c",
            "ChildItemId": "75343c35-1af0-46e7-84a4-db6861f354c0",
            "ParentItemId": "f9c85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4401_Katie_Cockburn.2025.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T15:26:34.257Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "f9c85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4401_Katie_Cockburn.2025.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (9592677d)
2025-07-28T16:27:11.9013840+01:00 80053f70-0002-7d00-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4401\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Katie\",\"lastName\":\"Cockburn\",\"email\":\"<EMAIL>\",\"phone\":\"07766646001\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"BN112QG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
    "FileFieldController": [
      {
        "Id": "b136415e-2420-411b-bddd-e32436d7d53c",
        "ChildItemId": "75343c35-1af0-46e7-84a4-db6861f354c0",
        "ParentItemId": "f9c85c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4401_Katie_Cockburn.2025.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T15:26:34.257Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "f9c85c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4401_Katie_Cockburn.2025.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "f9c85c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "5998",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-28T15:26:34.2403794Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4401\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Katie\",\"lastName\":\"Cockburn\",\"email\":\"<EMAIL>\",\"phone\":\"07766646001\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"BN112QG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "b136415e-2420-411b-bddd-e32436d7d53c",
            "ChildItemId": "75343c35-1af0-46e7-84a4-db6861f354c0",
            "ParentItemId": "f9c85c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4401_Katie_Cockburn.2025.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T15:26:34.257Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "f9c85c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4401_Katie_Cockburn.2025.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4401_katie_cockburn.2025.pdf?Status=Master&download=true"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (cabb0023)
2025-07-28T16:27:11.9015134+01:00 80053f70-0002-7d00-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId f9c85c9e-cae7-6322-9971-ff0200979a84 (c6fddb90)
2025-07-28T16:27:12.0724297+01:00 80053f70-0002-7d00-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-28T16:27:12.4411932+01:00 80053f70-0002-7d00-b63f-84710c7967bb [ERR] Error processing CV: Response status code does not indicate success: 404 (Not Found). (db13d13f)
2025-07-28T16:27:12.4412603+01:00 80053f70-0002-7d00-b63f-84710c7967bb [INF] (Webjob Application) Posted f9c85c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (447c7274)
2025-07-28T16:27:12.5286382+01:00 80053f70-0002-7d00-b63f-84710c7967bb [INF] (Webjob Application) Posted f9c85c9e-cae7-6322-9971-ff0200979a84  (f8e654dc)
2025-07-28T19:58:00.4069660+01:00  [WRN] Using an in-memory repository. Keys will not be persisted to storage. (28e83010)
2025-07-28T19:58:00.4101003+01:00  [WRN] Neither user profile nor HKLM registry available. Using an ephemeral key repository. Protected data will be unavailable when application exits. (54f66960)
2025-07-28T19:58:00.4500119+01:00  [WRN] No XML encryptor configured. Key {e8f69a11-90cf-4677-bcbf-dd098335ffa9} may be persisted to storage in unencrypted form. (9ca7e61e)
2025-07-28T19:58:00.5323265+01:00 801ef47a-0003-b400-b63f-84710c7967bb [WRN] Failed to determine the https port for redirect. (ca76cc21)
2025-07-28T19:58:00.8759666+01:00 801ef47a-0003-b400-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4432\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Jossiah\",\"lastName\":\"Makombe\",\"email\":\"<EMAIL>\",\"phone\":\"7487309112\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"PO21 1DG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "efc4746e-a728-44dc-a262-124f84efa8c3",
        "ChildItemId": "5e5087f7-b806-4a51-a795-c68af2766434",
        "ParentItemId": "30ca5c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4432_Jossiah_Makombe.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T18:57:59.173Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "30ca5c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4432_Jossiah_Makombe.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "30ca5c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "5999",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-28T18:57:59.1563542Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4432\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Jossiah\",\"lastName\":\"Makombe\",\"email\":\"<EMAIL>\",\"phone\":\"7487309112\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"PO21 1DG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "efc4746e-a728-44dc-a262-124f84efa8c3",
            "ChildItemId": "5e5087f7-b806-4a51-a795-c68af2766434",
            "ParentItemId": "30ca5c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4432_Jossiah_Makombe.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T18:57:59.173Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "30ca5c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4432_Jossiah_Makombe.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (a477f436)
2025-07-28T19:58:00.9638321+01:00 801ef47a-0003-b400-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4432\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Jossiah\",\"lastName\":\"Makombe\",\"email\":\"<EMAIL>\",\"phone\":\"7487309112\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"PO21 1DG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "efc4746e-a728-44dc-a262-124f84efa8c3",
        "ChildItemId": "5e5087f7-b806-4a51-a795-c68af2766434",
        "ParentItemId": "30ca5c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4432_Jossiah_Makombe.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T18:57:59.173Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "30ca5c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4432_Jossiah_Makombe.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "30ca5c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "5999",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "**************",
    "SubmissionTime": "2025-07-28T18:57:59.1563542Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4432\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Jossiah\",\"lastName\":\"Makombe\",\"email\":\"<EMAIL>\",\"phone\":\"7487309112\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"PO21 1DG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "efc4746e-a728-44dc-a262-124f84efa8c3",
            "ChildItemId": "5e5087f7-b806-4a51-a795-c68af2766434",
            "ParentItemId": "30ca5c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4432_Jossiah_Makombe.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T18:57:59.173Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "30ca5c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4432_Jossiah_Makombe.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (ea8b521e)
2025-07-28T19:58:01.0489761+01:00 801ef47a-0003-b400-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 30ca5c9e-cae7-6322-9971-ff0200979a84 (ad33ccf8)
2025-07-28T19:58:01.4101008+01:00 801ef47a-0003-b400-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-28T19:58:01.7749691+01:00 801ef47a-0003-b400-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-28T19:58:01.7762568+01:00 801ef47a-0003-b400-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-28T19:58:02.7167732+01:00 801ef47a-0003-b400-b63f-84710c7967bb [INF] Received HTTP response headers after 935.2609ms - 201 (f0742c1f)
2025-07-28T19:58:02.7174686+01:00 801ef47a-0003-b400-b63f-84710c7967bb [INF] End processing HTTP request after 952.3376ms - 201 (7656b38e)
2025-07-28T19:58:02.7186613+01:00 801ef47a-0003-b400-b63f-84710c7967bb [INF] (Webjob Application) Posted 30ca5c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is True (1ef066db)
2025-07-28T19:58:04.6471782+01:00 801ef47a-0003-b400-b63f-84710c7967bb [INF] (Webjob Application) Posted 30ca5c9e-cae7-6322-9971-ff0200979a84  (fa3e370c)
2025-07-28T19:58:47.2103209+01:00 80103488-0002-9400-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4432\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Jossiah\",\"lastName\":\"Makombe\",\"email\":\"<EMAIL>\",\"phone\":\"7487309112\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"PO21 1DG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "5bf8a2dc-a738-48ab-9850-188eb61993d3",
        "ChildItemId": "5e5087f7-b806-4a51-a795-c68af2766434",
        "ParentItemId": "31ca5c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4432_Jossiah_Makombe.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T18:58:47.09Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "31ca5c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4432_Jossiah_Makombe.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "31ca5c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6000",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-28T18:58:47.0735341Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4432\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Jossiah\",\"lastName\":\"Makombe\",\"email\":\"<EMAIL>\",\"phone\":\"7487309112\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"PO21 1DG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "5bf8a2dc-a738-48ab-9850-188eb61993d3",
            "ChildItemId": "5e5087f7-b806-4a51-a795-c68af2766434",
            "ParentItemId": "31ca5c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4432_Jossiah_Makombe.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T18:58:47.09Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "31ca5c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4432_Jossiah_Makombe.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (b5f871f5)
2025-07-28T19:58:47.2176798+01:00 80103488-0002-9400-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4432\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Jossiah\",\"lastName\":\"Makombe\",\"email\":\"<EMAIL>\",\"phone\":\"7487309112\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"PO21 1DG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "5bf8a2dc-a738-48ab-9850-188eb61993d3",
        "ChildItemId": "5e5087f7-b806-4a51-a795-c68af2766434",
        "ParentItemId": "31ca5c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4432_Jossiah_Makombe.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T18:58:47.09Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "31ca5c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4432_Jossiah_Makombe.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "31ca5c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6000",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-28T18:58:47.0735341Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4432\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Jossiah\",\"lastName\":\"Makombe\",\"email\":\"<EMAIL>\",\"phone\":\"7487309112\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"PO21 1DG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "5bf8a2dc-a738-48ab-9850-188eb61993d3",
            "ChildItemId": "5e5087f7-b806-4a51-a795-c68af2766434",
            "ParentItemId": "31ca5c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4432_Jossiah_Makombe.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T18:58:47.09Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "31ca5c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4432_Jossiah_Makombe.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (963b0a43)
2025-07-28T19:58:47.2179557+01:00 80103488-0002-9400-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 31ca5c9e-cae7-6322-9971-ff0200979a84 (8560d1e0)
2025-07-28T19:58:47.4149879+01:00 80103488-0002-9400-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-28T19:58:47.5760762+01:00 80103488-0002-9400-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-28T19:58:47.5761365+01:00 80103488-0002-9400-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-28T19:58:48.2885565+01:00 80103488-0002-9400-b63f-84710c7967bb [INF] Received HTTP response headers after 712.3366ms - 500 (f0742c1f)
2025-07-28T19:58:48.2886407+01:00 80103488-0002-9400-b63f-84710c7967bb [INF] End processing HTTP request after 712.6535ms - 500 (7656b38e)
2025-07-28T19:58:48.2906312+01:00 80103488-0002-9400-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-28T19:58:48.2906956+01:00 80103488-0002-9400-b63f-84710c7967bb [INF] (Webjob Application) Posted 31ca5c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (8999dfbc)
2025-07-28T19:58:48.3558675+01:00 80103488-0002-9400-b63f-84710c7967bb [INF] (Webjob Application) Posted 31ca5c9e-cae7-6322-9971-ff0200979a84  (82426f90)
2025-07-28T19:58:49.4144566+01:00 801ef51a-0003-b400-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4432\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Jossiah\",\"lastName\":\"Makombe\",\"email\":\"<EMAIL>\",\"phone\":\"7487309112\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"PO21 1DG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "5bf8a2dc-a738-48ab-9850-188eb61993d3",
        "ChildItemId": "5e5087f7-b806-4a51-a795-c68af2766434",
        "ParentItemId": "31ca5c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4432_Jossiah_Makombe.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T18:58:47.09Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "31ca5c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4432_Jossiah_Makombe.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "31ca5c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6000",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-28T18:58:47.0735341Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4432\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Jossiah\",\"lastName\":\"Makombe\",\"email\":\"<EMAIL>\",\"phone\":\"7487309112\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"PO21 1DG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "5bf8a2dc-a738-48ab-9850-188eb61993d3",
            "ChildItemId": "5e5087f7-b806-4a51-a795-c68af2766434",
            "ParentItemId": "31ca5c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4432_Jossiah_Makombe.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T18:58:47.09Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "31ca5c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4432_Jossiah_Makombe.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (b5f871f5)
2025-07-28T19:58:49.4147199+01:00 801ef51a-0003-b400-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4432\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Jossiah\",\"lastName\":\"Makombe\",\"email\":\"<EMAIL>\",\"phone\":\"7487309112\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"PO21 1DG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "5bf8a2dc-a738-48ab-9850-188eb61993d3",
        "ChildItemId": "5e5087f7-b806-4a51-a795-c68af2766434",
        "ParentItemId": "31ca5c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4432_Jossiah_Makombe.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T18:58:47.09Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "31ca5c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4432_Jossiah_Makombe.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "31ca5c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6000",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-28T18:58:47.0735341Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4432\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Jossiah\",\"lastName\":\"Makombe\",\"email\":\"<EMAIL>\",\"phone\":\"7487309112\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"PO21 1DG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "5bf8a2dc-a738-48ab-9850-188eb61993d3",
            "ChildItemId": "5e5087f7-b806-4a51-a795-c68af2766434",
            "ParentItemId": "31ca5c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4432_Jossiah_Makombe.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T18:58:47.09Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "31ca5c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4432_Jossiah_Makombe.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (963b0a43)
2025-07-28T19:58:49.4148712+01:00 801ef51a-0003-b400-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 31ca5c9e-cae7-6322-9971-ff0200979a84 (8560d1e0)
2025-07-28T19:58:49.6774246+01:00 801ef51a-0003-b400-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-28T19:58:49.7931409+01:00 801ef51a-0003-b400-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-28T19:58:49.7931869+01:00 801ef51a-0003-b400-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-28T19:58:50.5429001+01:00 801ef51a-0003-b400-b63f-84710c7967bb [INF] Received HTTP response headers after 749.6372ms - 500 (f0742c1f)
2025-07-28T19:58:50.5429790+01:00 801ef51a-0003-b400-b63f-84710c7967bb [INF] End processing HTTP request after 749.9007ms - 500 (7656b38e)
2025-07-28T19:58:50.5432607+01:00 801ef51a-0003-b400-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-28T19:58:50.5432951+01:00 801ef51a-0003-b400-b63f-84710c7967bb [INF] (Webjob Application) Posted 31ca5c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (8999dfbc)
2025-07-28T19:58:50.6075869+01:00 801ef51a-0003-b400-b63f-84710c7967bb [INF] (Webjob Application) Posted 31ca5c9e-cae7-6322-9971-ff0200979a84  (82426f90)
2025-07-28T19:58:54.7068345+01:00 8016237d-0001-b100-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4432\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Jossiah\",\"lastName\":\"Makombe\",\"email\":\"<EMAIL>\",\"phone\":\"7487309112\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"PO21 1DG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "5bf8a2dc-a738-48ab-9850-188eb61993d3",
        "ChildItemId": "5e5087f7-b806-4a51-a795-c68af2766434",
        "ParentItemId": "31ca5c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4432_Jossiah_Makombe.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T18:58:47.09Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "31ca5c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4432_Jossiah_Makombe.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "31ca5c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6000",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-28T18:58:47.0735341Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4432\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Jossiah\",\"lastName\":\"Makombe\",\"email\":\"<EMAIL>\",\"phone\":\"7487309112\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"PO21 1DG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "5bf8a2dc-a738-48ab-9850-188eb61993d3",
            "ChildItemId": "5e5087f7-b806-4a51-a795-c68af2766434",
            "ParentItemId": "31ca5c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4432_Jossiah_Makombe.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T18:58:47.09Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "31ca5c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4432_Jossiah_Makombe.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (b5f871f5)
2025-07-28T19:58:54.7070950+01:00 8016237d-0001-b100-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4432\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Jossiah\",\"lastName\":\"Makombe\",\"email\":\"<EMAIL>\",\"phone\":\"7487309112\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"PO21 1DG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "5bf8a2dc-a738-48ab-9850-188eb61993d3",
        "ChildItemId": "5e5087f7-b806-4a51-a795-c68af2766434",
        "ParentItemId": "31ca5c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4432_Jossiah_Makombe.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T18:58:47.09Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "31ca5c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4432_Jossiah_Makombe.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "31ca5c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6000",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-28T18:58:47.0735341Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4432\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Jossiah\",\"lastName\":\"Makombe\",\"email\":\"<EMAIL>\",\"phone\":\"7487309112\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"PO21 1DG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "5bf8a2dc-a738-48ab-9850-188eb61993d3",
            "ChildItemId": "5e5087f7-b806-4a51-a795-c68af2766434",
            "ParentItemId": "31ca5c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4432_Jossiah_Makombe.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T18:58:47.09Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "31ca5c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4432_Jossiah_Makombe.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (963b0a43)
2025-07-28T19:58:54.7072073+01:00 8016237d-0001-b100-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 31ca5c9e-cae7-6322-9971-ff0200979a84 (8560d1e0)
2025-07-28T19:58:54.8864829+01:00 8016237d-0001-b100-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-28T19:58:54.9688761+01:00 8016237d-0001-b100-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-28T19:58:54.9689191+01:00 8016237d-0001-b100-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-28T19:58:55.9283718+01:00 8016237d-0001-b100-b63f-84710c7967bb [INF] Received HTTP response headers after 959.3934ms - 500 (f0742c1f)
2025-07-28T19:58:55.9284168+01:00 8016237d-0001-b100-b63f-84710c7967bb [INF] End processing HTTP request after 959.5971ms - 500 (7656b38e)
2025-07-28T19:58:55.9360696+01:00 8016237d-0001-b100-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-28T19:58:55.9361157+01:00 8016237d-0001-b100-b63f-84710c7967bb [INF] (Webjob Application) Posted 31ca5c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (8999dfbc)
2025-07-28T19:58:56.0740298+01:00 8016237d-0001-b100-b63f-84710c7967bb [INF] (Webjob Application) Posted 31ca5c9e-cae7-6322-9971-ff0200979a84  (82426f90)
2025-07-28T19:59:05.1189313+01:00 800577b7-0002-7d00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4432\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Jossiah\",\"lastName\":\"Makombe\",\"email\":\"<EMAIL>\",\"phone\":\"7487309112\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"PO21 1DG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "5bf8a2dc-a738-48ab-9850-188eb61993d3",
        "ChildItemId": "5e5087f7-b806-4a51-a795-c68af2766434",
        "ParentItemId": "31ca5c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4432_Jossiah_Makombe.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T18:58:47.09Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "31ca5c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4432_Jossiah_Makombe.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "31ca5c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6000",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-28T18:58:47.0735341Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4432\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Jossiah\",\"lastName\":\"Makombe\",\"email\":\"<EMAIL>\",\"phone\":\"7487309112\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"PO21 1DG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "5bf8a2dc-a738-48ab-9850-188eb61993d3",
            "ChildItemId": "5e5087f7-b806-4a51-a795-c68af2766434",
            "ParentItemId": "31ca5c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4432_Jossiah_Makombe.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T18:58:47.09Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "31ca5c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4432_Jossiah_Makombe.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (b5f871f5)
2025-07-28T19:59:05.1223942+01:00 800577b7-0002-7d00-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4432\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Jossiah\",\"lastName\":\"Makombe\",\"email\":\"<EMAIL>\",\"phone\":\"7487309112\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"PO21 1DG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "5bf8a2dc-a738-48ab-9850-188eb61993d3",
        "ChildItemId": "5e5087f7-b806-4a51-a795-c68af2766434",
        "ParentItemId": "31ca5c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4432_Jossiah_Makombe.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T18:58:47.09Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "31ca5c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4432_Jossiah_Makombe.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "31ca5c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6000",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-28T18:58:47.0735341Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4432\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Jossiah\",\"lastName\":\"Makombe\",\"email\":\"<EMAIL>\",\"phone\":\"7487309112\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"PO21 1DG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "5bf8a2dc-a738-48ab-9850-188eb61993d3",
            "ChildItemId": "5e5087f7-b806-4a51-a795-c68af2766434",
            "ParentItemId": "31ca5c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4432_Jossiah_Makombe.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T18:58:47.09Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "31ca5c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4432_Jossiah_Makombe.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (963b0a43)
2025-07-28T19:59:05.1225940+01:00 800577b7-0002-7d00-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 31ca5c9e-cae7-6322-9971-ff0200979a84 (8560d1e0)
2025-07-28T19:59:05.2723467+01:00 800577b7-0002-7d00-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-28T19:59:05.3805311+01:00 800577b7-0002-7d00-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-28T19:59:05.3805688+01:00 800577b7-0002-7d00-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-28T19:59:06.0619892+01:00 800577b7-0002-7d00-b63f-84710c7967bb [INF] Received HTTP response headers after 681.3684ms - 500 (f0742c1f)
2025-07-28T19:59:06.0620314+01:00 800577b7-0002-7d00-b63f-84710c7967bb [INF] End processing HTTP request after 681.5562ms - 500 (7656b38e)
2025-07-28T19:59:06.0623171+01:00 800577b7-0002-7d00-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-28T19:59:06.0623575+01:00 800577b7-0002-7d00-b63f-84710c7967bb [INF] (Webjob Application) Posted 31ca5c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (8999dfbc)
2025-07-28T19:59:06.1669793+01:00 800577b7-0002-7d00-b63f-84710c7967bb [INF] (Webjob Application) Posted 31ca5c9e-cae7-6322-9971-ff0200979a84  (82426f90)
2025-07-28T19:59:22.2109377+01:00 801ef626-0003-b400-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4432\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Jossiah\",\"lastName\":\"Makombe\",\"email\":\"<EMAIL>\",\"phone\":\"7487309112\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"PO21 1DG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "5bf8a2dc-a738-48ab-9850-188eb61993d3",
        "ChildItemId": "5e5087f7-b806-4a51-a795-c68af2766434",
        "ParentItemId": "31ca5c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4432_Jossiah_Makombe.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T18:58:47.09Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "31ca5c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4432_Jossiah_Makombe.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "31ca5c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6000",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-28T18:58:47.0735341Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4432\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Jossiah\",\"lastName\":\"Makombe\",\"email\":\"<EMAIL>\",\"phone\":\"7487309112\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"PO21 1DG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "5bf8a2dc-a738-48ab-9850-188eb61993d3",
            "ChildItemId": "5e5087f7-b806-4a51-a795-c68af2766434",
            "ParentItemId": "31ca5c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4432_Jossiah_Makombe.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T18:58:47.09Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "31ca5c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4432_Jossiah_Makombe.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (b5f871f5)
2025-07-28T19:59:22.2112191+01:00 801ef626-0003-b400-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4432\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Jossiah\",\"lastName\":\"Makombe\",\"email\":\"<EMAIL>\",\"phone\":\"7487309112\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"PO21 1DG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
    "FileFieldController": [
      {
        "Id": "5bf8a2dc-a738-48ab-9850-188eb61993d3",
        "ChildItemId": "5e5087f7-b806-4a51-a795-c68af2766434",
        "ParentItemId": "31ca5c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "4432_Jossiah_Makombe.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T18:58:47.09Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "31ca5c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "4432_Jossiah_Makombe.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "31ca5c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6000",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-28T18:58:47.0735341Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"4432\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Jossiah\",\"lastName\":\"Makombe\",\"email\":\"<EMAIL>\",\"phone\":\"7487309112\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"PO21 1DG\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.**********\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "5bf8a2dc-a738-48ab-9850-188eb61993d3",
            "ChildItemId": "5e5087f7-b806-4a51-a795-c68af2766434",
            "ParentItemId": "31ca5c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "4432_Jossiah_Makombe.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T18:58:47.09Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "31ca5c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "4432_Jossiah_Makombe.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4432_jossiah_makombe.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (963b0a43)
2025-07-28T19:59:22.2113223+01:00 801ef626-0003-b400-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 31ca5c9e-cae7-6322-9971-ff0200979a84 (8560d1e0)
2025-07-28T19:59:22.4409984+01:00 801ef626-0003-b400-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-28T19:59:22.5402351+01:00 801ef626-0003-b400-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-28T19:59:22.5402805+01:00 801ef626-0003-b400-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-28T19:59:23.2276146+01:00 801ef626-0003-b400-b63f-84710c7967bb [INF] Received HTTP response headers after 687.2783ms - 500 (f0742c1f)
2025-07-28T19:59:23.2276572+01:00 801ef626-0003-b400-b63f-84710c7967bb [INF] End processing HTTP request after 687.5011ms - 500 (7656b38e)
2025-07-28T19:59:23.2279468+01:00 801ef626-0003-b400-b63f-84710c7967bb [WRN] Failed to send payload. Status code: InternalServerError (c35c2239)
2025-07-28T19:59:23.2279725+01:00 801ef626-0003-b400-b63f-84710c7967bb [INF] (Webjob Application) Posted 31ca5c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is False (8999dfbc)
2025-07-28T19:59:23.3187985+01:00 801ef626-0003-b400-b63f-84710c7967bb [INF] (Webjob Application) Posted 31ca5c9e-cae7-6322-9971-ff0200979a84  (82426f90)
2025-07-28T20:05:26.0948466+01:00 8029d0e1-0002-de00-b63f-84710c7967bb [INF] The raw json at endpoint is:  {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"2549\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sharon Treesa \",\"lastName\":\"James\",\"email\":\"<EMAIL>\",\"phone\":\"7767 952712 \",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"CA13 0JA \",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.1545655197\"},\"jobQuestions\":[{\"QuestionId\":\"6\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"5\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"320786\"}],\"ParentQuestionId\":6}]}]}",
    "FileFieldController": [
      {
        "Id": "5a59d79a-d4c2-47cb-86b9-1b0746373c3b",
        "ChildItemId": "0bde2ef2-4b3d-4d74-ba63-b4e10ee8ccc7",
        "ParentItemId": "35ca5c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "2549_Sharon Treesa _James.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/2549_sharon-treesa-_james.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T19:05:25.947Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "35ca5c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "2549_Sharon Treesa _James.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/2549_sharon-treesa-_james.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "35ca5c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6001",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-28T19:05:25.931617Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"2549\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sharon Treesa \",\"lastName\":\"James\",\"email\":\"<EMAIL>\",\"phone\":\"7767 952712 \",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"CA13 0JA \",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.1545655197\"},\"jobQuestions\":[{\"QuestionId\":\"6\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"5\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"320786\"}],\"ParentQuestionId\":6}]}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "5a59d79a-d4c2-47cb-86b9-1b0746373c3b",
            "ChildItemId": "0bde2ef2-4b3d-4d74-ba63-b4e10ee8ccc7",
            "ParentItemId": "35ca5c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "2549_Sharon Treesa _James.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/2549_sharon-treesa-_james.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T19:05:25.947Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "35ca5c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "2549_Sharon Treesa _James.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/2549_sharon-treesa-_james.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (3fb55357)
2025-07-28T20:05:26.0951230+01:00 8029d0e1-0002-de00-b63f-84710c7967bb [INF]  Logging only, not processed - The Application parent JSON is : {
  "Name": "Form is submitted",
  "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49",
  "Item": {
    "ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"2549\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sharon Treesa \",\"lastName\":\"James\",\"email\":\"<EMAIL>\",\"phone\":\"7767 952712 \",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"CA13 0JA \",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.1545655197\"},\"jobQuestions\":[{\"QuestionId\":\"6\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"5\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"320786\"}],\"ParentQuestionId\":6}]}]}",
    "FileFieldController": [
      {
        "Id": "5a59d79a-d4c2-47cb-86b9-1b0746373c3b",
        "ChildItemId": "0bde2ef2-4b3d-4d74-ba63-b4e10ee8ccc7",
        "ParentItemId": "35ca5c9e-cae7-6322-9971-ff0200979a84",
        "ChildItemProviderName": "OpenAccessDataProvider",
        "ParentItemProviderName": null,
        "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
        "ChildItemAdditionalInfo": "2549_Sharon Treesa _James.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/2549_sharon-treesa-_james.pdf?Status=Master",
        "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
        "ParentItemAdditionalInfo": null,
        "LastModified": "2025-07-28T19:05:25.947Z",
        "Ordinal": 0.0,
        "Attributes": {},
        "ApplicationName": "GroupWebsite/",
        "ComponentPropertyName": "35ca5c9e-cae7-6322-9971-ff0200979a84",
        "AvailableForLive": false,
        "AvailableForMaster": false,
        "AvailableForTemp": false,
        "IsParentDeleted": false,
        "IsChildDeleted": false,
        "FileName": "2549_Sharon Treesa _James.pdf",
        "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/2549_sharon-treesa-_james.pdf?Status=Master"
      }
    ]
  },
  "OriginalEvent": {
    "EntryId": "35ca5c9e-cae7-6322-9971-ff0200979a84",
    "ReferralCode": "6001",
    "UserId": "00000000-0000-0000-0000-000000000000",
    "Username": "",
    "IpAddress": "*************",
    "SubmissionTime": "2025-07-28T19:05:25.931617Z",
    "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84",
    "FormName": "sf_recruitmentjobapplication",
    "FormTitle": "Recruitment Job Application",
    "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000",
    "SendConfirmationEmail": false,
    "Controls": [
      {
        "FieldControlName": null,
        "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "00000000-0000-0000-0000-000000000000",
        "Text": null,
        "Type": 0,
        "Title": "PayloadJson",
        "FieldName": "ParagraphTextFieldController",
        "Value": "{\"Jobinfo\":{\"jobId\":\"2549\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"Sharon Treesa \",\"lastName\":\"James\",\"email\":\"<EMAIL>\",\"phone\":\"7767 952712 \",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"CA13 0JA \",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.1545655197\"},\"jobQuestions\":[{\"QuestionId\":\"6\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"5\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"320786\"}],\"ParentQuestionId\":6}]}]}",
        "OldValue": null
      },
      {
        "FieldControlName": null,
        "Id": "ded52a9d-cae7-6322-9971-ff0000979a84",
        "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84",
        "Text": null,
        "Type": 1,
        "Title": "Untitled",
        "FieldName": "FileFieldController",
        "Value": [
          {
            "Id": "5a59d79a-d4c2-47cb-86b9-1b0746373c3b",
            "ChildItemId": "0bde2ef2-4b3d-4d74-ba63-b4e10ee8ccc7",
            "ParentItemId": "35ca5c9e-cae7-6322-9971-ff0200979a84",
            "ChildItemProviderName": "OpenAccessDataProvider",
            "ParentItemProviderName": null,
            "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document",
            "ChildItemAdditionalInfo": "2549_Sharon Treesa _James.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/2549_sharon-treesa-_james.pdf?Status=Master",
            "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication",
            "ParentItemAdditionalInfo": null,
            "LastModified": "2025-07-28T19:05:25.947Z",
            "Ordinal": 0.0,
            "Attributes": {},
            "ApplicationName": "GroupWebsite/",
            "ComponentPropertyName": "35ca5c9e-cae7-6322-9971-ff0200979a84",
            "AvailableForLive": false,
            "AvailableForMaster": false,
            "AvailableForTemp": false,
            "IsParentDeleted": false,
            "IsChildDeleted": false,
            "FileName": "2549_Sharon Treesa _James.pdf",
            "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/2549_sharon-treesa-_james.pdf?Status=Master"
          }
        ],
        "OldValue": null
      }
    ],
    "Origin": null
  },
  "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"
} (a337240c)
2025-07-28T20:05:26.0952731+01:00 8029d0e1-0002-de00-b63f-84710c7967bb [INF] (Webjob Application) Saved web job application entryId 35ca5c9e-cae7-6322-9971-ff0200979a84 (0fad60a6)
2025-07-28T20:05:26.2641787+01:00 8029d0e1-0002-de00-b63f-84710c7967bb [INF] Successfully retrieved bearer token. HTTP Response: OK (21d1c55b)
2025-07-28T20:05:26.4761686+01:00 8029d0e1-0002-de00-b63f-84710c7967bb [INF] Start processing HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (338f1c77)
2025-07-28T20:05:26.4762045+01:00 8029d0e1-0002-de00-b63f-84710c7967bb [INF] Sending HTTP request "POST" "https://apinew.mydentist.co.uk/RecruitmentHub.API.V1/api/Application/PostWebApplication" (2e7ac211)
2025-07-28T20:05:26.9943597+01:00 8029d0e1-0002-de00-b63f-84710c7967bb [INF] Received HTTP response headers after 518.1107ms - 201 (f0742c1f)
2025-07-28T20:05:26.9944043+01:00 8029d0e1-0002-de00-b63f-84710c7967bb [INF] End processing HTTP request after 518.2822ms - 201 (7656b38e)
2025-07-28T20:05:26.9944836+01:00 8029d0e1-0002-de00-b63f-84710c7967bb [INF] (Webjob Application) Posted 35ca5c9e-cae7-6322-9971-ff0200979a84 to RecruitmentHub, result is True (cd8db637)
2025-07-28T20:05:27.1461274+01:00 8029d0e1-0002-de00-b63f-84710c7967bb [INF] (Webjob Application) Posted 35ca5c9e-cae7-6322-9971-ff0200979a84  (a11df94c)
