﻿
CREATE TABLE [dbo].[sf_generalEnquiryForm](
	[Id] [uniqueidentifier] NOT NULL,
	[First Name] [nvarchar](max) NULL,
	[Last Name] [nvarchar](max) NULL,
	[Phone Number] [nvarchar](60) NULL,
	[Email] [nvarchar](200) NULL,
	[DOB] [datetime2](7) NULL,
	[Postcode] [nvarchar](50) NULL,
	[Treatments] [nvarchar](max) NULL,
	[GAClientId] [nvarchar](50) NULL,
	[ProcessedDate] [datetime2](7) NULL,
	[ErrorDate] [datetime2](7) NULL,
	[RawJson] [nvarchar](max) NULL,
	[CreatedDate] [datetime2](7) NULL,
	[Source] [nvarchar](50) NULL,
	[PracticePostCode] [nvarchar](50) NULL,
	[PracticeId] [bigint] NULL
CONSTRAINT [sf_generalEnquiryForm] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[sf_generalEnquiryForm] ADD  CONSTRAINT [DF_sf_generalEnquiryForm_createdDate]  DEFAULT (getdate()) FOR [CreatedDate]
GO

