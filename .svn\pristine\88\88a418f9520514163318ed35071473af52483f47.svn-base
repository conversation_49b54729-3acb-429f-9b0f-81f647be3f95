{"Name": "Form is submitted", "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49", "Item": {"GAClientId": "455788", "VacancyID": "5719", "JobCategory": "Associate Dentist, Private Dentist", "TextFieldController_2": "Batsirai", "TextFieldController_3": "Tambo", "TextFieldController_4": "***********", "TextFieldController_5": "<EMAIL>", "TextFieldController_6": "m26 1gg", "FileFieldController": null, "TextFieldController_7": "this is a linked account", "TextFieldController_8": "i heard about it from the internet", "TextFieldController_9": "123309", "MultipleChoiceFieldController": "Yes", "MultipleChoiceFieldController_0": "Yes", "MultipleChoiceFieldController_2": null, "TextFieldController_11": null, "TextFieldController_12": null, "TextFieldController_13": null, "TextFieldController_14": null, "TextFieldController_15": null, "DropdownListFieldController": null, "MultipleChoiceFieldController_3": null, "MultipleChoiceFieldController_4": null, "TextFieldController_22": null, "MultipleChoiceFieldController_5": null, "MultipleChoiceFieldController_6": null, "MultipleChoiceFieldController_7": null, "MultipleChoiceFieldController_8": null, "MultipleChoiceFieldController_9": null}, "OriginalEvent": {"EntryId": "27fdd49b-cae7-6322-9971-ff0000979a84", "ReferralCode": "3", "UserId": "********-0000-0000-0000-************", "Username": "", "IpAddress": "***************", "SubmissionTime": "2022-12-20T11:48:06.5580471Z", "FormId": "aa06d49b-cae7-6322-9971-ff0000979a84", "FormName": "sf_careersjobapplicationform", "FormTitle": "Careers job application form", "FormSubscriptionListId": "********-0000-0000-0000-************", "SendConfirmationEmail": false, "Controls": [{"FieldControlName": "GAClientId", "Id": "2340d49b-cae7-6322-9971-ff0000979a84", "SiblingId": "********-0000-0000-0000-************", "Text": null, "Type": 0, "Title": "Google Client ID", "FieldName": "GAClientId", "Value": "455788", "OldValue": null}, {"FieldControlName": "VacancyID", "Id": "0730d49b-cae7-6322-9971-ff0000979a84", "SiblingId": "2340d49b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "VacancyID", "FieldName": "VacancyID", "Value": "5719", "OldValue": null}, {"FieldControlName": "JobCategory", "Id": "1430d49b-cae7-6322-9971-ff0000979a84", "SiblingId": "0730d49b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "JobCategory", "FieldName": "JobCategory", "Value": "Associate Dentist, Private Dentist", "OldValue": null}, {"FieldControlName": null, "Id": "7608d49b-cae7-6322-9971-ff0000979a84", "SiblingId": "1430d49b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "First Name", "FieldName": "TextFieldController_2", "Value": "Batsirai", "OldValue": null}, {"FieldControlName": null, "Id": "8308d49b-cae7-6322-9971-ff0000979a84", "SiblingId": "7608d49b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Last Name", "FieldName": "TextFieldController_3", "Value": "Tambo", "OldValue": null}, {"FieldControlName": null, "Id": "9008d49b-cae7-6322-9971-ff0000979a84", "SiblingId": "8308d49b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Phone Number", "FieldName": "TextFieldController_4", "Value": "***********", "OldValue": null}, {"FieldControlName": null, "Id": "9f08d49b-cae7-6322-9971-ff0000979a84", "SiblingId": "9008d49b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Email Address", "FieldName": "TextFieldController_5", "Value": "<EMAIL>", "OldValue": null}, {"FieldControlName": null, "Id": "ae08d49b-cae7-6322-9971-ff0000979a84", "SiblingId": "9f08d49b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Home Postcode", "FieldName": "TextFieldController_6", "Value": "m26 1gg", "OldValue": null}, {"FieldControlName": null, "Id": "bb08d49b-cae7-6322-9971-ff0000979a84", "SiblingId": "ae08d49b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 1, "Title": "CV Upload", "FieldName": "FileFieldController", "Value": null, "OldValue": null}, {"FieldControlName": null, "Id": "c608d49b-cae7-6322-9971-ff0000979a84", "SiblingId": "bb08d49b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "<PERSON>ed<PERSON> Account", "FieldName": "TextFieldController_7", "Value": "this is a linked account", "OldValue": null}, {"FieldControlName": null, "Id": "d008d49b-cae7-6322-9971-ff0000979a84", "SiblingId": "c608d49b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Where did you hear about this vacancy?", "FieldName": "TextFieldController_8", "Value": "i heard about it from the internet", "OldValue": null}, {"FieldControlName": null, "Id": "da08d49b-cae7-6322-9971-ff0000979a84", "SiblingId": "d008d49b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "GDC Number", "FieldName": "TextFieldController_9", "Value": "123309", "OldValue": null}, {"FieldControlName": null, "Id": "e708d49b-cae7-6322-9971-ff0000979a84", "SiblingId": "da08d49b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Do you currently hold eligibility to work in the UK?", "FieldName": "MultipleChoiceFieldController", "Value": "Yes", "OldValue": null}, {"FieldControlName": null, "Id": "f208d49b-cae7-6322-9971-ff0000979a84", "SiblingId": "e708d49b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Do you currently hold a UK Driving licence?", "FieldName": "MultipleChoiceFieldController_0", "Value": "Yes", "OldValue": null}, {"FieldControlName": null, "Id": "970fd49b-cae7-6322-9971-ff0000979a84", "SiblingId": "f208d49b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Don’t have a GDC number?", "FieldName": "MultipleChoiceFieldController_2", "Value": "No", "OldValue": null}, {"FieldControlName": null, "Id": "a20fd49b-cae7-6322-9971-ff0000979a84", "SiblingId": "970fd49b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Current City / Town", "FieldName": "TextFieldController_11", "Value": "56", "OldValue": null}, {"FieldControlName": null, "Id": "ac0fd49b-cae7-6322-9971-ff0000979a84", "SiblingId": "a20fd49b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Current Country", "FieldName": "TextFieldController_12", "Value": "23", "OldValue": null}, {"FieldControlName": null, "Id": "b60fd49b-cae7-6322-9971-ff0000979a84", "SiblingId": "ac0fd49b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "University of qualification", "FieldName": "TextFieldController_13", "Value": "13", "OldValue": null}, {"FieldControlName": null, "Id": "c00fd49b-cae7-6322-9971-ff0000979a84", "SiblingId": "b60fd49b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Country of Qualification", "FieldName": "TextFieldController_14", "Value": "16", "OldValue": null}, {"FieldControlName": null, "Id": "ca0fd49b-cae7-6322-9971-ff0000979a84", "SiblingId": "c00fd49b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Year of qualification", "FieldName": "TextFieldController_15", "Value": "1998", "OldValue": null}, {"FieldControlName": null, "Id": "3722d49b-cae7-6322-9971-ff0000979a84", "SiblingId": "ca0fd49b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Visa status", "FieldName": "DropdownListFieldController", "Value": "1", "OldValue": null}, {"FieldControlName": null, "Id": "1310d49b-cae7-6322-9971-ff0000979a84", "SiblingId": "3722d49b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "As you do not currently hold a GDC registration, you may be interested in our trainee dental nurse programme. This is a fully funded learn while you earn course, accredited by the NEBDN. We would like to ask you a few more questions to start your career in dental nursing- is that ok?", "FieldName": "MultipleChoiceFieldController_3", "Value": null, "OldValue": null}, {"FieldControlName": null, "Id": "1e10d49b-cae7-6322-9971-ff0000979a84", "SiblingId": "1310d49b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Do you have a Grade C/4 or above in English GCSE (or equivalent)?", "FieldName": "MultipleChoiceFieldController_4", "Value": "Yes", "OldValue": null}, {"FieldControlName": null, "Id": "2910d49b-cae7-6322-9971-ff0000979a84", "SiblingId": "1e10d49b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "If no, we can provide an opportunity to complete an English assessment, which you would be required to pass (leave as shown note under the above.)", "FieldName": "TextFieldController_22", "Value": null, "OldValue": null}, {"FieldControlName": null, "Id": "3310d49b-cae7-6322-9971-ff0000979a84", "SiblingId": "2910d49b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Would you be comfortable working in a clinical healthcare setting (working with medical equipment, needles etc)?", "FieldName": "MultipleChoiceFieldController_5", "Value": "Yes", "OldValue": null}, {"FieldControlName": null, "Id": "3e10d49b-cae7-6322-9971-ff0000979a84", "SiblingId": "3310d49b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Would you be comfortable assisting with dental procedures (ie tooth extractions?)", "FieldName": "MultipleChoiceFieldController_6", "Value": "Yes", "OldValue": null}, {"FieldControlName": null, "Id": "4910d49b-cae7-6322-9971-ff0000979a84", "SiblingId": "3e10d49b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Putting ‘Patients First’ is our key strength – would you be comfortable dealing with patients who are nervous, in pain or upset?", "FieldName": "MultipleChoiceFieldController_7", "Value": "Yes", "OldValue": null}, {"FieldControlName": null, "Id": "5410d49b-cae7-6322-9971-ff0000979a84", "SiblingId": "4910d49b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Are you able to commit to a 12-18 month training programme? This will be a mixture of on the job and online learning online learning modules (topics include Microbiology/ Pathology/ Oral Surgery/ Pain control/ Perio Diseases)written and practical exams and submission of a record of experience.", "FieldName": "MultipleChoiceFieldController_8", "Value": "Yes", "OldValue": null}, {"FieldControlName": null, "Id": "5f10d49b-cae7-6322-9971-ff0000979a84", "SiblingId": "5410d49b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Poss a temporary question about “are you comfortable wearing PPE?” (with a picture?)", "FieldName": "MultipleChoiceFieldController_9", "Value": "Yes", "OldValue": null}], "Origin": null}, "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"}