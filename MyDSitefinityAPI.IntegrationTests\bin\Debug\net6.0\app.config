﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
  </configSections>
  <connectionStrings>
    <add name="Warehouse" connectionString="Data Source=myd-dev-clarity;Initial Catalog=Warehouse;Integrated Security=True" providerName="System.Data.SqlClient"/>
    <add name="GroupWebsitePracticeData" connectionString="Data Source=myd-dev-clarity;Initial Catalog=GroupWebsitePracticeData;Integrated Security=True" providerName="System.Data.SqlClient"/>
    <add name="Staging" connectionString="Data Source=myd-sql-staging;Initial Catalog=Staging;Integrated Security=True;Trusted_Connection=True;MultipleActiveResultSets=True" providerName="System.Data.SqlClient"/>
  </connectionStrings>
  <appSettings>
    <add key="sitefinityUsername" value="DataUpdate"/>
    <add key="sitefinityPassword" value="wZpFf847FaODf5hpXul2"/>
    <add key="EmailToList" value="<EMAIL>"/>
    <add key="EmailTemplate" value="CustomerFeedbackSubmitted.cshtml"/>
    <add key="ReferralEmailTemplate" value="PatientReferralSubmitted.cshtml"/>
    <add key="PatientSupportFeedBackUser" value="WXHO\\mmckessy"/>
    <add key="APIKey" value="ec32870c8968aebacab659d03d0098317857142729baeaa3c3da3e116dba685a"/>
    <add key="RawJsonLogging" value="no"/>
    <add key="applicationEmail" value="<EMAIL>"/>
    <add key="EmailToOverride" value="<EMAIL>"/>
    <add key="MarketingTeamEmail" value="<EMAIL>"/>
    <add key="PracticeViewImageApprovalUrl" value="http://dev-myd-web/PracticeView/Images/Status"/>
    <add key="CoreUpdaterAPIUrl" value="http://dev-myd-web/coreupdater/api/UpdateSync" />
	  <add key="websiteConfigBaseUrl" value="https://api.mydentist.co.uk/webconfig.Api.V4.Dev/"/>
	  <add key="websiteConfigApiKey" value="CT-Hvk6OuOtBGlq75VhLEch5zfA5dV1IJmrA"/>
    <add key="PracticeApi" value="https://apinew.mydentist.co.uk/WebPractice.API.V1.Test" />
    <add key="PracticeApiKey" value="11233" />
  </appSettings>
  <entityFramework>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer"/>
    </providers>
  </entityFramework>
</configuration>