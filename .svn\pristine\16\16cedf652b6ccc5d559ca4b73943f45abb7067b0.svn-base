{"ExtendedData": {"inputs": ["http://172.30.1.174/EmailInterfaceServiceV2/Service1.svc"], "collectionTypes": ["System.Collections.Generic.Dictionary`2", "System.Collections.Generic.List`1"], "namespaceMappings": ["*, EmailService"], "references": ["<PERSON><PERSON><PERSON>Framework, {<PERSON>tityFramework, 6.4.4}", "EntityFramework.SqlServer, {EntityFramework, 6.4.4}", "Microsoft.Bcl.AsyncInterfaces, {Microsoft.Bcl.AsyncInterfaces, 5.0.0}", "Microsoft.Extensions.Configuration.Abstractions, {Microsoft.Extensions.Configuration.Abstractions, 6.0.0}", "Microsoft.Extensions.Configuration.Binder, {Microsoft.Extensions.Configuration.Binder, 6.0.0}", "Microsoft.Extensions.DependencyInjection, {Microsoft.Extensions.DependencyInjection, 2.1.0}", "Microsoft.Extensions.DependencyInjection.Abstractions, {Microsoft.Extensions.DependencyInjection.Abstractions, 3.1.9}", "Microsoft.Extensions.DependencyModel, {Microsoft.Extensions.DependencyModel, 3.1.6}", "Microsoft.Extensions.Logging, {Microsoft.Extensions.Logging, 2.0.0}", "Microsoft.Extensions.Logging.Abstractions, {Microsoft.Extensions.Logging.Abstractions, 2.0.0}", "Microsoft.Extensions.ObjectPool, {Microsoft.Extensions.ObjectPool, 5.0.10}", "Microsoft.Extensions.Options, {Microsoft.Extensions.Options, 2.0.0}", "Microsoft.Extensions.Primitives, {Microsoft.Extensions.Primitives, 6.0.0}", "Microsoft.IdentityModel.Logging, {Microsoft.IdentityModel.Logging, 6.8.0}", "Microsoft.IdentityModel.Protocols.WsTrust, {Microsoft.IdentityModel.Protocols.WsTrust, 6.8.0}", "Microsoft.IdentityModel.Tokens, {Microsoft.IdentityModel.Tokens, 6.8.0}", "Microsoft.IdentityModel.Tokens.Saml, {Microsoft.IdentityModel.Tokens.Saml, 6.8.0}", "Microsoft.IdentityModel.Xml, {Microsoft.IdentityModel.Xml, 6.8.0}", "Microsoft.OpenApi, {Microsoft.OpenApi, 1.3.1}", "<PERSON><PERSON><PERSON>, {<PERSON><PERSON><PERSON>, 3.3.0}", "<PERSON><PERSON><PERSON>, {Serilog, 2.10.0}", "Serilog.Extensions.Logging, {Serilog.Extensions.Logging, 3.1.0}", "Serilog.Extensions.Logging.File, {Serilog.Extensions.Logging.File, 3.0.0}", "Serilog.Formatting.Compact, {Serilog.Formatting.Compact, 1.1.0}", "Serilog.Sinks.Async, {Serilog.Sinks.Async, 1.5.0}", "Serilog.Sinks.File, {Serilog.Sinks.File, 3.2.0}", "Serilog.Sinks.RollingFile, {Serilog.Sinks.RollingFile, 3.3.0}", "Swashbuckle.AspNetCore.Filters, {Swashbuckle.AspNetCore.Filters, 7.0.5}", "Swashbuckle.AspNetCore.Filters.Abstractions, {Swashbuckle.AspNetCore.Filters.Abstractions, 7.0.5}", "Swashbuckle.AspNetCore.Swagger, {Swashbuckle.AspNetCore.Swagger, 6.4.0}", "Swashbuckle.AspNetCore.SwaggerGen, {Swashbuckle.AspNetCore.SwaggerGen, 6.2.3}", "Swashbuckle.AspNetCore.SwaggerUI, {Swashbuckle.AspNetCore.SwaggerUI, 6.2.3}", "System.CodeDom, {System.CodeDom, 4.7.0}", "System.ComponentModel.Annotations, {System.ComponentModel.Annotations, 4.7.0}", "System.Configuration.ConfigurationManager, {System.Configuration.ConfigurationManager, 4.7.0}", "System.Data.SqlClient, {System.Data.SqlClient, 4.8.1}", "System.Drawing.Common, {System.Drawing.Common, 5.0.0}", "System.IO, {System.IO, 4.3.0}", "System.IO.FileSystem, {System.IO.FileSystem, 4.3.0}", "System.IO.FileSystem.Primitives, {System.IO.FileSystem.Primitives, 4.3.0}", "System.Reflection, {System.Reflection, 4.3.0}", "System.Reflection.DispatchProxy, {System.Reflection.DispatchProxy, 4.7.1}", "System.Reflection.Primitives, {System.Reflection.Primitives, 4.3.0}", "System.Runtime, {System.Runtime, 4.3.0}", "System.Runtime.CompilerServices.Unsafe, {System.Runtime.CompilerServices.Unsafe, 6.0.0}", "System.Runtime.Handles, {System.Runtime.Handles, 4.3.0}", "System.Runtime.InteropServices, {System.Runtime.InteropServices, 4.3.0}", "System.Security.AccessControl, {System.Security.AccessControl, 5.0.0}", "System.Security.Cryptography.Cng, {System.Security.Cryptography.Cng, 5.0.0}", "System.Security.Cryptography.Xml, {System.Security.Cryptography.Xml, 5.0.0}", "System.Security.Permissions, {System.Security.Permissions, 5.0.0}", "System.Security.Principal.Windows, {System.Security.Principal.Windows, 5.0.0}", "System.ServiceModel, {System.ServiceModel.Primitives, 4.10.0}", "System.ServiceModel.Duplex, {System.ServiceModel.Duplex, 4.10.0}", "System.ServiceModel.Federation, {System.ServiceModel.Federation, 4.10.0}", "System.ServiceModel.Http, {System.ServiceModel.Http, 4.10.0}", "System.ServiceModel.NetTcp, {System.ServiceModel.NetTcp, 4.10.0}", "System.ServiceModel.Primitives, {System.ServiceModel.Primitives, 4.10.0}", "System.ServiceModel.Security, {System.ServiceModel.Security, 4.10.0}", "System.Text.Encoding, {System.Text.Encoding, 4.3.0}", "System.Text.Encoding.Extensions, {System.Text.Encoding.Extensions, 4.3.0}", "System.<PERSON>.<PERSON>, {System.Text.<PERSON>, 4.7.2}", "System.Threading, {System.Threading, 4.3.0}", "System.Threading.Tasks, {System.Threading.Tasks, 4.3.0}", "System.Threading.Timer, {System.Threading.Timer, 4.0.1}", "System.Windows.Extensions, {System.Windows.Extensions, 5.0.0}", "System.Xml.ReaderWriter, {System.Xml.ReaderWriter, 4.3.0}", "System.Xml.XmlDocument, {System.Xml.XmlDocument, 4.3.0}"], "targetFramework": "net6.0", "typeReuseMode": "All"}}