﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MyDSitefinityAPI.Models.PracticeDirectory
{
    [Table("PracticePersonLink", Schema = "Directory")]
    public class PracticePersonLink
    {
        [Key]
        [Column("PracticePersonLinkId")]
        public int PracticePersonLinkId { get; set; }

        [Column("PracticePersonId")]
        public int PracticePersonId { get; set; }

        [<PERSON>umn("Leaver")]
        public bool IsLeaver { get; set; }

        [Column("LeaverDate")]
        public DateTime? LeaverDate { get; set; }

        [ForeignKey("PracticePersonId")]
        public virtual PracticePerson PracticePerson { get; set; }
    }
}
