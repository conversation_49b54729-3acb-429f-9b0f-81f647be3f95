﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mydentist.MyDSitefinityAPI.ImplementationService
{
    public static class StringExtensionMethods
    {

        public static string FirstName(this string fullName)
        {
            if (string.IsNullOrEmpty(fullName))
                return null;

            int positionOfSpace = fullName.IndexOf(" ");

            if(positionOfSpace == -1)
                return fullName;

            string firstName = fullName.Substring(0, positionOfSpace);

            return firstName.FirstCharToUpper();
        }

        public static string LastName(this string fullName)
        {
            string firstName =  fullName.FirstName();

            if (firstName == null)
                return null;

            string restOfName = fullName.Replace(firstName, string.Empty);

            if (string.IsNullOrEmpty(restOfName))
                return null;

            string restOfNameTrimmed = restOfName.Trim();
            return restOfNameTrimmed.FirstCharToUpper();
        }

        public static string FirstCharToUpper(this string input) =>
            input switch
            {
                null => null,
                "" => null,
                _ => string.Concat(input[0].ToString().ToUpper(), input.AsSpan(1))
            };
    }
}
