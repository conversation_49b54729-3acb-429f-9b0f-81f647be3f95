﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Mydentist.MyDSitefinityAPI.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class PostgradUni : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "YearQualified",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                newName: "YearQualifiedUndergraduate");

            migrationBuilder.RenameColumn(
                name: "University",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                newName: "UniversityUndergraduate");

            migrationBuilder.AddColumn<int>(
                name: "UniversityPostgraduate",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "YearQualifiedPostgraduate",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                type: "date",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "UniversityPostgraduate",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm");

            migrationBuilder.DropColumn(
                name: "YearQualifiedPostgraduate",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm");

            migrationBuilder.RenameColumn(
                name: "YearQualifiedUndergraduate",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                newName: "YearQualified");

            migrationBuilder.RenameColumn(
                name: "UniversityUndergraduate",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                newName: "University");
        }
    }
}
