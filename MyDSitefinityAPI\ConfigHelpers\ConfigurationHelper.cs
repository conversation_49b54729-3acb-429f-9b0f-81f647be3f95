﻿using ConfigurationManager = System.Configuration.ConfigurationManager;

namespace MyDSitefinityAPI.ConfigHelpers
{
    public class ConfigurationHelper
    {

        public static string GetValue(string key, string defaultValue = null, bool supressException = true)
        {
            if (ConfigurationManager.AppSettings[key] == null && !supressException)
                throw new MissingConfigurationException(key);

            if (ConfigurationManager.AppSettings[key] == null && !string.IsNullOrEmpty(defaultValue))
                return defaultValue;

            return ConfigurationManager.AppSettings[key].ToString();
        }
    }

    public class MissingConfigurationException : Exception
    {
        public MissingConfigurationException(string key) : base(string.Format("Missing appSetting '{0}.", key))
        {

        }
    }

}

