﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MyDSitefinityAPI.Models.PracticeDirectory
{
    [Table("Image", Schema = "Directory")]
    public class PerformerImage
    {
        [Key]
        [Column("ImageId")]
        public Guid ImageId { get; set; }

        [Column("OriginatingPracticeId")]
        public int PracticeId { get; set; }

        [Column("GdcNo")]
        public string GdcNumber { get; set; }

        /// <summary>
        /// A lazy loaded instance of the performer profile associated with the image
        /// </summary>
        [ForeignKey("GdcNumber")]
        public virtual PerformerProfile PerformerProfile { get; set; }

        /// <summary>
        /// A lazy loaded instance of the originating practice for the image
        /// </summary>
        [ForeignKey("PracticeId")]
        public virtual PracticeDetails Practice { get; set; }
    }
}
