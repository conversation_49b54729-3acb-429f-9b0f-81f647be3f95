﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Mydentist.MyDSitefinityAPI.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class LogLeadCode : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsProcessed",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm");

            migrationBuilder.RenameColumn(
                name: "ProcessedDateTime",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                newName: "LastProcessedDateTime");

            migrationBuilder.AddColumn<string>(
                name: "CRMLeadContactCode",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ErrorMessage",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Status",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CRMLeadContactCode",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm");

            migrationBuilder.DropColumn(
                name: "ErrorMessage",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm");

            migrationBuilder.DropColumn(
                name: "Status",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm");

            migrationBuilder.RenameColumn(
                name: "LastProcessedDateTime",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                newName: "ProcessedDateTime");

            migrationBuilder.AddColumn<bool>(
                name: "IsProcessed",
                schema: "Recruitment",
                table: "RecruitmentWebsiteForm",
                type: "bit",
                nullable: true);
        }
    }
}
