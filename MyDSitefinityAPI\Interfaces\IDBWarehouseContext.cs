﻿using MyDSitefinityAPI.Models;
using System.Collections.Generic;
using System.Data.Entity;

namespace MyDSitefinityAPI.Interfaces
{
    public interface IDBWarehouseContext
    {
        DbSet<sf_quickcontactpmform> sf_quickcontactpmform { get; set; }
        DbSet<sf_feedbackform> sf_feedbackform { get; set; }

        DbSet<sf_Emergencydentalappointment> sf_Emergencydentalappointment { get; set; }
        DbSet<sf_GeneralEnquiry> sf_generalEnquiry { get; set; }

        DbSet<PatientSupportUpdate> patientsupportupdate { get; set; }

        DbSet<PatientSupportFeedback> patientsupportfeedback { get; set; }
        DbSet<PracticeDetails> practiceDetails { get; set; }
        DbSet<ApplicationLog> ApplicationLog { get; set; }

    }
}
