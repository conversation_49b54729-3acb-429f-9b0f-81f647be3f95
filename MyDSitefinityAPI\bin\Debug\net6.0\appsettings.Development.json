{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "NearestPractice": {
    "BaseUrl": "https://api.mydentist.co.uk/PracticeLocation.API.V1",
    "PracticeLocationApiKey": "1191661"
  },
  "ConnectionStrings": {
    //"DbContextWarehouse": "Data Source=10.1.2.115; User ID=formSubmitUser; Password= Password3456!;Initial Catalog=Warehouse; Encrypt=False",
    // "DBContextWebsiteConfig": "Data Source=10.1.2.115; User ID=formSubmitUser; Password= Password3456!;Initial Catalog=WebsiteConfig; Encrypt=False",

    "DbContextWarehouse": "Data Source=myd-dev-clarity;Initial Catalog=Warehouse; Trusted_Connection=True; Encrypt=False",
    "DBContextWebsiteConfig": "Data Source=myd-dev-clarity;Initial Catalog=WebsiteConfig; Trusted_Connection=True; Encrypt=False"
  }
}
