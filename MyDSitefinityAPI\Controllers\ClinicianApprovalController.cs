﻿using System.Net;
using System.Web;
using IDHGroup.SharedLibraries.SitefinityServiceCaller.Core;
using Microsoft.AspNetCore.Mvc;
using MyDSitefinity;
using MyDSitefinityAPI.Models;
using MyDSitefinityAPI.Models.PracticeDirectory;
using MyDSitefinityAPI.Services;

namespace MyDSitefinityAPI.Controllers
{
    [Controller]
    [Route("workflow/[controller]/[action]")]
    public class ClinicianApprovalController : Controller
    {
        private ILogger<ClinicianApprovalController> Logger { get; }
        private PublicUserAuthenticationService AuthenticationService { get; }
        private PerformerProfileService PerformerProfileService { get; }

        public ClinicianApprovalController(ILogger<ClinicianApprovalController> logger, PublicUserAuthenticationService authenticationService, PerformerProfileService performerProfileService)
        {
            Logger = logger;
            AuthenticationService = authenticationService;
            PerformerProfileService = performerProfileService;
        }

        /// <summary>
        /// Displays a login form for the current user
        /// </summary>
        /// <param name="imageId">The ID of the image the user wants to approve</param>
        /// <param name="token">A salted username</param>
        /// <returns></returns>
        [HttpGet]
        public ActionResult Login([FromQuery] Guid imageId, [FromQuery] string? token)
        {
            if (imageId == Guid.Empty)
            {
                return View();
            }

            PublicUser? saltedUser = AuthenticationService.GetSaltedUser(token);

            if (saltedUser != null)
            {
                Logger.LogDebug("Token is not null. I am already logged in. Redirecting to clinician approval page.");

                return new RedirectToActionResult("Approve", "ClinicianApproval", new { imageId });
            }

            PublicUser emptyUser = new PublicUser { KeyParameter = imageId };
            return View(emptyUser);
        }

        /// <summary>
        /// Authenticates a user with a username and password
        /// </summary>
        /// <param name="user">A PublicUser object representing the current user</param>
        /// <returns></returns>
        [HttpPost]
        public JsonResult Login([FromBody] PublicUser user)
        {
            if (string.IsNullOrEmpty(user.UserId) || string.IsNullOrEmpty(user.Password))
            {
                Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                return Json(ResultViewModel.Failure("Must provide both a username and a password."));
            }

            PublicUser? saltedUser = AuthenticationService.GetUserById(user.UserId);

            if (saltedUser == null || AuthenticationService.Authenticate(saltedUser, user.Password) == false)
            {
                Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                return Json(ResultViewModel.Failure("Invalid username or password."));
            }

            Logger.LogDebug($"Logged in successfully as {user.UserId}. Redirecting to clinician approval page.");

            string hash = AuthenticationService.ComputeHash(user.UserId, saltedUser.Salt);

            return Json(ResultViewModel.Success(HttpUtility.UrlEncode(hash)));
        }

        [HttpGet]
        public ActionResult ApproveClinician([FromQuery] Guid imageId, [FromQuery] string? token)
        {
            if (string.IsNullOrEmpty(token))
            {
                ViewBag.TokenIsEmpty = true;
                return View();
            }

            if (AuthenticationService.GetSaltedUser(token) == null)
            {
                return RedirectToAction("Login", "ClinicianApproval", new { imageId });
            }

            ViewBag.TokenIsEmpty = false;
            ViewBag.Token = token;

            PerformerProfile? performer = PerformerProfileService.GetByImageId(imageId);
            if (performer != null)
            {
                performer.ImageId = imageId;
            }

            return View(performer);
        }

        [HttpPost]
        public async Task<ActionResult> ApproveClinician([FromForm] PerformerProfile performerProfile)
        {
            PublicUser? saltedUser = AuthenticationService.GetSaltedUser(performerProfile.Token);

            ViewBag.TokenIsEmpty = string.IsNullOrEmpty(performerProfile.Token);
            ViewBag.Token = performerProfile.Token;

            if (saltedUser == null)
            {
                return RedirectToAction("Login", "ClinicianApproval", new { performerProfile.ImageId });
            }

            // The view and emails are expecting the image and other related objects to be passed to it. performerProfile doesn't contain these
            PerformerProfile? completePerformer = PerformerProfileService.GetByImageId(performerProfile.ImageId.Value);

            if (completePerformer == null)
            {
                return View(null);
            }

            PerformerImage performerImage = completePerformer.Images.First(image => image.PracticeId != 0);

            // Approved
            if (performerProfile.OnlinePresenceStatusId == (int)OnlinePresenceStatus.WithMarketingTeam)
            {
                PerformerProfileService.ApprovePerformer(performerProfile, saltedUser);
                PerformerProfileService.UpdateSitefinityClinician(completePerformer, performerImage.PracticeId);

                await PerformerProfileService.NotifyPracticeOfApprovalAsync(completePerformer);
                await PerformerProfileService.NotifyMarketingOfApprovalAsync(completePerformer);
            }

            // Reject
            else
            {
                PerformerProfileService.RejectPerformer(performerProfile, saltedUser);
                await PerformerProfileService.NotifyPracticeOfRejectionAsync(completePerformer);
            }

            return View(completePerformer);
        }
    }
}
