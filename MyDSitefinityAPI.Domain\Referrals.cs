﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mydentist.MyDSitefinityAPI.Domain
{
    [Table("Sf_ReferralForm", Schema = "dbo")]
    public class Referrals
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        public string PatientTitle { get; set; } = default!;

        public string PatientFirstName { get; set; } = default!;
        public string PatientLastName { get; set; } = default!;
        public string PatientAddressLine1 { get; set; } = default!;
        public string PatientAddressLine2 { get; set; } = default!;
        public string PatientCounty { get; set; } = default!;
        public string PatientTown { get; set; } = default!;
        public string PatientPostcode { get; set; } = default!;
        public string PatientDateOfBirth { get; set; } = default!;
        public string PatientContactNumber { get; set; } = default!;
        public string PatientInterest { get; set; } = default!;
        public string PatientEmail { get; set; } = default!;
        public string PatientNhsNumber { get; set; } = default!;

        public string ClinicianFirstName { get; set; } = default!;
        public string ClinicianLastName { get; set; } = default!;
        public string ClinicianAddressLine1 { get; set; } = default!;
        public string ClinicianAddressLine2 { get; set; } = default!;
        public string ClinicianCounty { get; set; } = default!;
        public string ClinicianTown { get; set; } = default!;
        public string ClinicianPostcode { get; set; } = default!;
        public DateTime ReferralDate { get; set; } = DateTime.Now;
        public string PracticeName { get; set; } = default!;
        public string ClinicianGdcNumber { get; set; } = default!;
        public string IsNhsOrPrivate { get; set; } = default!;
        public string ClinicianReasonForReferral { get; set; } = default!;
        public string IsClinicianReferralUrgent { get; set; } = default!;
        public string OtherRelevantMedicalDentalInfo { get; set; } = default!;
        public string WasDptRadioTakenLastYear { get; set; } = default!;
        public string ShouldDoOrthodonticExtractions { get; set; } = default!;
        public string ShouldSeeOrthodontist { get; set; } = default!;
        public string ClinicianToSee { get; set; } = default!;
        public string ReferralFormType { get; set; } = default!;
        public bool HasError { get; set; }
        public string? ErrorMessage { get; set; }
        public DateTime DateOfErrorMessage { get; set; }

        public string PracticeReferredTo { get; set; } = null!;
        public string ReferralSource { get; set; } = null!;
    }
}
