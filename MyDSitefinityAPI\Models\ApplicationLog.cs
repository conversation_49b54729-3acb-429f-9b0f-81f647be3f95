﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.Core.Objects.DataClasses;
using System.Reflection;

namespace MyDSitefinityAPI.Models
{
    [Table("ApplicationsLog" , Schema = "Recruitment")]
    public class ApplicationLog
    {
        [Key]
        public int Id { get; set; }
        public string Title {get;set;}
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public int VacancyId { get; set; }
        public string Address { get; set; }
        public string PostCode { get; set; }
        public string EmailAddress { get; set; }
        public string GDCNumber { get; set; }
        public string ContactNumber { get; set; }
        public string CVLink { get; set; }
        public DateTime DateCreated { get; set; }
        public string RawJson { get; set; }


    }
}
