﻿using System.Reflection;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using MyDSitefinityAPI.EmailHelpers;
using MyDSitefinityAPI.Interfaces;

namespace MyDSitefinityAPI.IntegrationTests.Mock
{
    /// <summary>
    /// A class which allows a <see cref="IEmailSender"/> to be passed as a dependency without needing to reproduce the entire RazorEngine structure
    /// </summary>
    public class MockEmailSender : IEmailSender
    {
        public async Task SendEmail(EmailDetail detail)
        {
            FileInfo assemblyInfo = new FileInfo(Assembly.GetExecutingAssembly().Location);
            FileInfo templateInfo = new FileInfo($"{assemblyInfo.Directory.FullName}/Views/{detail.TemplateLocation}.cshtml");
            
            Assert.IsTrue(templateInfo.Exists, "Email template does not exist");
            Assert.IsFalse(string.IsNullOrEmpty(detail.EmailAddress), "Email address is null");
            Assert.IsFalse(string.IsNullOrEmpty(detail.Subject), "Email subject must be provided");
        }

        public void SendMail(EmailDetail detail)
        {
            FileInfo assemblyInfo = new FileInfo(Assembly.GetExecutingAssembly().Location);
            FileInfo templateInfo = new FileInfo($"{assemblyInfo.Directory.FullName}/Views/{detail.TemplateLocation}.cshtml");

            Assert.IsTrue(templateInfo.Exists, "Email template does not exist");
            Assert.IsFalse(string.IsNullOrEmpty(detail.EmailAddress), "Email address is null");
            Assert.IsFalse(string.IsNullOrEmpty(detail.Subject), "Email subject must be provided");
        }
    }
}
