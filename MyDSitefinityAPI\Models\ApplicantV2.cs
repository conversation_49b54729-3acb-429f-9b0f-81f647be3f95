﻿namespace MyDSitefinityAPI.Models
{
    public class ApplicantV2
    {
        //public string title { get; set; }
        public int? titleId { get; set; }
        public string firstName { get; set; }
        public string lastName { get; set; }
        public string gdcNumber { get; set; }
        public string emailAddress { get; set; }
        public string contactNumber { get; set; }
        public string address { get; set; }
        public string postCode { get; set; }
        public string linkedInAccount { get; set; }
        public bool? eligibleToWorkInUk { get; set; }
        public bool? hasUkDrivingLicense { get; set; }
        public int? currentCountryId { get; set; }
        public int? countryOfQualificationId { get; set; }
        public int? universityOfQualificationId { get; set; }
        public int? yearOfQualification { get; set; }
        public int? visaStatusId { get; set; }

    }
}
