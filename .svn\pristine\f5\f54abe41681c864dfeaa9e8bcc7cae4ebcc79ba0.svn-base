﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Mydentist.MyDSitefinityAPI.Persistence;

public class DesignTimeDbContextFactory : IDesignTimeDbContextFactory<DbContextWarehouse>
{
    public DbContextWarehouse CreateDbContext(string[] args)
    {
        var optionsBuilder = new DbContextOptionsBuilder<DbContextWarehouse>();

        // Change the connection string to point at the database you will be updating
        optionsBuilder.UseSqlServer("Server=myd-dev-clarity;Database=Warehouse;Trusted_Connection=True;Encrypt=False;"); 

        return new DbContextWarehouse(optionsBuilder.Options);
    }
}