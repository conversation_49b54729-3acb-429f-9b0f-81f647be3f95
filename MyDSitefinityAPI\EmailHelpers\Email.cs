﻿using System.ServiceModel;
using Microsoft.Extensions.Logging;
using ConfigurationManager = System.Configuration.ConfigurationManager;

namespace MyDSitefinityAPI.EmailHelpers
{
    public class Email
    {


        public Email()
        {

        }
        public Email(ILogger<Email> logger)
        {
            _logger = logger;
        }

        public bool Add()
        {
            using (EmailService.Service1Client proxy = new EmailService.Service1Client())
            {
                proxy.Endpoint.Address = new EndpointAddress(ConfigurationManager.AppSettings["EmailEndpoint"]);
                EmailService.SendEmailRequest mRequest = new EmailService.SendEmailRequest();
                EmailService.Application_Details ap = new EmailService.Application_Details();
                EmailService.Email ed = new EmailService.Email()
                {
                    From = new List<string> { ConfigurationManager.AppSettings["applicationEmail"] },
                    To = new List<string>(),
                    CC = new List<string>(),
                    Attachments = new List<string>()
                };

                if (ConfigurationManager.AppSettings["EmailToOverride"].Length > 0)
                {
                    ed.To = new List<string> { ConfigurationManager.AppSettings["EmailToOverride"] };
                }
                else
                {
                    if (Cc != null)
                    {
                        foreach (string ccEmail in Cc)
                            if (ccEmail != "") { ed.CC.Add(ccEmail); _logger.LogInformation("EmailService - Adding CC To " + ccEmail); }
                    }
                    else
                        _logger.LogInformation("Email CC To is Empty");


                    if (To != null)
                    {
                        foreach (string toEmail in To)
                            if (toEmail != "") { ed.To.Add(toEmail); _logger.LogInformation("EmailService - Adding Email to " + toEmail); }
                    }
                    else
                        _logger.LogInformation("Email To is Empty");
                }

                ap.Requesting_System = "FormSubmission API";
                ap.Requesting_Version = "1.0.0.0";

                ed.Subject = Subject;

                ed.body = "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body>" + Body + "</body></html>";

                ed.IsHTML = true;
                ed.Attachments = Attachments;
                mRequest.email = ed;
                mRequest.App_Details = ap;

                EmailService.SendEmailResponse e_resp = new EmailService.SendEmailResponse();

                e_resp = proxy.Send_MailAsync(mRequest).Result;

                if (e_resp.ErrorOccured == true)
                {
                    _logger.LogInformation("EmailService - Failed to add email to queue.");
                    return false;
                }

                _logger.LogInformation("EmailService - Email Successfully Added To Queue");

                return true;
            }
        }

        #region Properties
        public string Subject { get; set; }
        public List<string> To { get; set; }
        public List<string> Cc { get; set; }

        public List<string> Attachments { get; set; }

        public string Body { get; set; }
        public DateTime DelayedDate { get; set; }
        public bool DelayEmail { get; set; }
        public ILogger<Email> _logger { get; set; }

        #endregion

    }
}
