﻿@model MyDSitefinityAPI.Models.PublicUser

<!DOCTYPE html>
<html>
<head>
    <title>{my}dentist | Login to clinician approval workflow</title>
    <style>
        /* Normalise */
        article, aside, details, figcaption, figure, footer, header, hgroup, main, nav, section, summary {
            display: block;
        }

        audio, canvas, video {
            display: inline-block;
        }

            audio:not([controls]) {
                display: none;
                height: 0;
            }

        [hidden], template {
            display: none;
        }

        html {
            font-family: sans-serif;
            -ms-text-size-adjust: 100%;
            -webkit-text-size-adjust: 100%;
        }

        body {
            margin: 0;
        }

        a {
            background: transparent;
        }

            a:focus {
                outline: thin dotted;
            }

            a:active, a:hover {
                outline: 0;
            }

        h1 {
            font-size: 2em;
            margin: 0.67em 0;
        }

        abbr[title] {
            border-bottom: 1px dotted;
        }

        b, strong {
            font-weight: bold;
        }

        dfn {
            font-style: italic;
        }

        hr {
            border: none;
            -moz-box-sizing: content-box;
            box-sizing: content-box;
            height: 2;
            background-color: #778988;
        }

        mark {
            background: #ff0;
            color: $black;
        }

        code, kbd, pre, samp {
            font-family: monospace, serif;
            font-size: 1em;
        }

        pre {
            white-space: pre-wrap;
        }

        q {
            quotes: "\201C" "\201D" "\2018" "\2019";
        }

        small {
            font-size: 80%;
        }

        sub, sup {
            font-size: 75%;
            line-height: 0;
            position: relative;
            vertical-align: baseline;
        }

        sup {
            top: -0.5em;
        }

        sub {
            bottom: -0.25em;
        }

        img {
            border: 0;
            color: $black;
        }

        svg:not(:root) {
            overflow: hidden;
        }

        figure {
            margin: 0;
        }

        fieldset {
            border: none;
            margin: 0;
            padding: 0;
        }

        legend {
            border: 0;
            padding: 0;
        }

        button, input, select, textarea {
            font-family: inherit;
            font-size: 100%;
            margin: 0;
        }

        button, input {
            line-height: normal;
        }

        button, select {
            text-transform: none;
        }

        button, html input[type="button"], input[type="reset"], input[type="submit"] {
            -webkit-appearance: button;
            cursor: pointer;
        }

            button[disabled], html input[disabled] {
                cursor: default;
            }

        input[type="checkbox"], input[type="radio"] {
            box-sizing: border-box;
            padding: 0;
        }

        input[type="search"] {
            -webkit-appearance: textfield;
            -moz-box-sizing: border-box;
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
        }

            input[type="search"]::-webkit-search-cancel-button, input[type="search"]::-webkit-search-decoration {
                -webkit-appearance: none;
            }

        button::-moz-focus-inner, input::-moz-focus-inner {
            border: 0;
            padding: 0;
        }

        textarea {
            overflow: auto;
            vertical-align: top;
        }

        table {
            border-collapse: collapse;
            border-spacing: 0;
        }

        .RadDock .rdTable {
            height: auto;
        }

        /* Core styles */
        body, select, input, textarea {
            font: 14px/1.4em Arial, Helvetica, sans-serif;
            color: #1b2523;
            padding-bottom: 60px;
        }

        p {
            margin: 10px 0;
        }

        h1 {
            margin: 20px 0 10px 0;
            line-height: normal;
            letter-spacing: -0.02em;
            font-weight: bold;
            border: none;
            padding: 0;
        }

        .align-center {
            text-align: center;
        }

        .mydentist-logo {
            background: url('data:image/svg+xml;charset=US-ASCII,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%0D%0A%3C%21--%20Generator%3A%20Adobe%20Illustrator%2018.1.1%2C%20SVG%20Export%20Plug-In%20.%20SVG%20Version%3A%206.00%20Build%200%29%20%20--%3E%0D%0A%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20xmlns%3Axlink%3D%22http%3A//www.w3.org/1999/xlink%22%20x%3D%220px%22%20y%3D%220px%22%0D%0A%09%20width%3D%22265px%22%20height%3D%2270px%22%20viewBox%3D%22165.1%20385.9%20265%2070%22%20enable-background%3D%22new%20165.1%20385.9%20265%2070%22%20xml%3Aspace%3D%22preserve%22%3E%0D%0A%3Cg%3E%0D%0A%09%3Cg%3E%0D%0A%09%09%3Cpath%20fill%3D%22%23778988%22%20d%3D%22M255.7%2C407.2c-0.9%2C0-1.8%2C0.4-2.1%2C1.4l-6.1%2C18.8l-7.6-18.8c-0.3-1-1.2-1.4-2.3-1.4c-2.1%2C0-5.4%2C1.7-5.4%2C3.9%0D%0A%09%09%09c0%2C0.4%2C0.1%2C0.7%2C0.2%2C1.1l10.8%2C23.2c-0.4%2C7.6-6.2%2C9.3-9%2C9.3c-1.5%2C0-2.1%2C1.6-2.1%2C3.4c0%2C3%2C1.6%2C3.4%2C3.7%2C3.4c11.3%2C0%2C13-8.3%2C16-15.8%0D%0A%09%09%09l9.2-23.5c0.1-0.3%2C0.2-0.7%2C0.2-0.9C261.3%2C409%2C257.6%2C407.2%2C255.7%2C407.2z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%23778988%22%20d%3D%22M220.5%2C407.3c-3.6%2C0-7.3%2C2-9.5%2C6.2c-1.3-3.7-4.2-6.2-7.9-6.2c-4.1%2C0-7%2C2.3-8.3%2C4.7v-1.9%0D%0A%09%09%09c0-1.5-1.6-2.5-3.6-2.5c-2.4%2C0-4%2C1-4%2C2.5v24.8c0%2C1.2%2C1.6%2C2.6%2C4%2C2.6c2.2%2C0%2C4.1-1.3%2C4.1-2.6V420c0-3.7%2C2.4-5.6%2C4.9-5.6%0D%0A%09%09%09c2.6%2C0%2C5.1%2C2.1%2C5.1%2C5.6v15c0%2C1.7%2C2.1%2C2.5%2C4%2C2.5c2%2C0%2C4.1-0.7%2C4.1-2.5v-15c0-3.6%2C2.4-5.6%2C4.9-5.6c2.5%2C0%2C5.1%2C1.9%2C5.1%2C5.7V435%0D%0A%09%09%09c0%2C1.2%2C1.9%2C2.6%2C4%2C2.6c2.2%2C0%2C4.1-1.3%2C4.1-2.6v-15C231.6%2C411.8%2C226.1%2C407.3%2C220.5%2C407.3z%22/%3E%0D%0A%09%3C/g%3E%0D%0A%09%3Cg%3E%0D%0A%09%09%3Cpath%20fill%3D%22%233A4646%22%20d%3D%22M424.3%2C393.6H421c-0.2%2C0-0.3%2C0.2-0.3%2C0.4s0.1%2C0.5%2C0.3%2C0.5h1.1v4.4c0%2C0.2%2C0.2%2C0.3%2C0.5%2C0.3%0D%0A%09%09%09c0.2%2C0%2C0.5-0.1%2C0.5-0.3v-4.4h1.1c0.2%2C0%2C0.3-0.2%2C0.3-0.5C424.7%2C393.9%2C424.6%2C393.6%2C424.3%2C393.6z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%233A4646%22%20d%3D%22M429.1%2C393.6c-0.4%2C0-0.5%2C0.1-0.8%2C0.5l-0.9%2C1.7l-0.9-1.7c-0.3-0.5-0.4-0.5-0.8-0.5c-0.3%2C0-0.5%2C0.1-0.5%2C0.5%0D%0A%09%09%09v4.8c0%2C0.2%2C0.2%2C0.3%2C0.5%2C0.3c0.2%2C0%2C0.5-0.1%2C0.5-0.3v-3.4l0.9%2C1.7c0.1%2C0.2%2C0.2%2C0.2%2C0.3%2C0.2c0.1%2C0%2C0.3%2C0%2C0.3-0.2l0.9-1.6v3.3%0D%0A%09%09%09c0%2C0.2%2C0.2%2C0.3%2C0.5%2C0.3c0.2%2C0%2C0.5-0.1%2C0.5-0.3v-4.8C429.6%2C393.8%2C429.4%2C393.6%2C429.1%2C393.6z%22/%3E%0D%0A%09%3C/g%3E%0D%0A%09%3Cg%3E%0D%0A%09%09%3Cpath%20fill%3D%22%233A4646%22%20d%3D%22M305.7%2C389.1c-1%2C0-1.8%2C0.7-1.8%2C1.6v20.7c-1.8-2.6-4.7-4.1-8.1-4.1c-6.3%2C0-11.6%2C5.2-11.6%2C11.8v7.3%0D%0A%09%09%09c0%2C6.3%2C5.3%2C11.9%2C11.5%2C11.9c3.2%2C0%2C6.5-1.6%2C8.3-4.1v2.1c0%2C0.9%2C0.8%2C1.6%2C1.8%2C1.6c1.1%2C0%2C1.8-0.8%2C1.8-1.6v-45.4%0D%0A%09%09%09C307.5%2C389.8%2C306.7%2C389.1%2C305.7%2C389.1z%20M303.9%2C419v9.5c0%2C1.9-3.3%2C6.3-8%2C6.3c-4.2%2C0-8.1-4-8.1-8.5v-7.3c0-4.3%2C3.9-8.4%2C8.2-8.4%0D%0A%09%09%09C299.9%2C410.6%2C303.9%2C413.8%2C303.9%2C419z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%233A4646%22%20d%3D%22M348.6%2C407.2c-3.9%2C0-6.7%2C2-8.2%2C4.1v-2.2c0-0.9-0.8-1.5-1.8-1.5s-1.8%2C0.6-1.8%2C1.5v27c0%2C0.8%2C0.7%2C1.6%2C1.8%2C1.6%0D%0A%09%09%09c1%2C0%2C1.8-0.8%2C1.8-1.6V419c0-5.2%2C4-8.4%2C7.9-8.4c4.2%2C0%2C8.2%2C3.9%2C8.2%2C8.4v17.1c0%2C1.1%2C0.9%2C1.6%2C1.8%2C1.6s1.8-0.6%2C1.8-1.6V419%0D%0A%09%09%09C360.1%2C412.4%2C355%2C407.2%2C348.6%2C407.2z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%233A4646%22%20d%3D%22M328.8%2C423.9c2.5%2C0%2C5.6%2C0%2C5.6-5.1c0-6.5-5.1-11.7-11.7-11.7c-6.5%2C0-11.9%2C5.2-11.9%2C11.6v7.5%0D%0A%09%09%09c0%2C7.1%2C5.4%2C11.9%2C13.4%2C11.9c5.7%2C0%2C9.4-2.6%2C9.4-4.1c0-0.8-0.8-1.8-1.7-1.8c-0.5%2C0-1%2C0.3-1.5%2C0.7c-1%2C0.7-2.6%2C1.8-5.9%2C1.8%0D%0A%09%09%09c-5.9%2C0-9.9-3.4-9.9-8.5v-2.4h14.2V423.9z%20M314.4%2C420.8v-2.3c0-4.4%2C3.7-8.1%2C8.3-8.1c5%2C0%2C8.2%2C4.3%2C8.2%2C8.4c0%2C1.6-0.4%2C2-3.2%2C2H314.4z%0D%0A%09%09%09%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%233A4646%22%20d%3D%22M395.8%2C420.9c-3.5-1-6.5-1.9-6.5-5.2c0-2.5%2C2-5.2%2C6.4-5.2c2.5%2C0%2C4.1%2C0.7%2C5.2%2C1.2c0.6%2C0.3%2C1%2C0.4%2C1.3%2C0.4%0D%0A%09%09%09c0.9%2C0%2C1.6-1.2%2C1.6-1.9c0-1.7-4.5-3.1-8.4-3.1c-6.8%2C0-9.9%2C4.4-9.9%2C8.6c0%2C5.6%2C4.2%2C6.9%2C8.5%2C8.2c3.7%2C1.1%2C7.3%2C2.2%2C7.3%2C6.3%0D%0A%09%09%09c0%2C2.9-2.4%2C4.5-6.7%2C4.5c-3.8%2C0-5.6-1.3-6.8-2.2c-0.6-0.4-1-0.8-1.5-0.8c-0.7%2C0-1.4%2C1-1.4%2C1.7c0%2C1.8%2C3.8%2C4.5%2C9.8%2C4.5%0D%0A%09%09%09c6.3%2C0%2C10.1-3%2C10.1-8.1C405.1%2C423.6%2C400.1%2C422.2%2C395.8%2C420.9z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%233A4646%22%20d%3D%22M375.6%2C410.8c0.8%2C0%2C1.5-0.7%2C1.5-1.6s-0.7-1.6-1.5-1.6h-8.1v-12.9c0-0.9-0.8-1.6-1.8-1.6%0D%0A%09%09%09c-0.8%2C0-1.8%2C0.7-1.8%2C1.6v33.8c0%2C6.2%2C3.2%2C9.3%2C9.9%2C9.3h1.6c1.1%2C0%2C1.7-0.9%2C1.7-1.7c0-1-0.7-1.7-1.7-1.7h-1.6c-4.6%2C0-6.2-1.5-6.2-5.8%0D%0A%09%09%09v-17.6H375.6z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%233A4646%22%20d%3D%22M418.9%2C434.4h-1.6c-4.6%2C0-6.2-1.5-6.2-5.8v-17.7h8.1c0.8%2C0%2C1.5-0.7%2C1.5-1.6s-0.7-1.6-1.5-1.6H411v-12.9%0D%0A%09%09%09c0-0.9-0.8-1.6-1.8-1.6c-0.8%2C0-1.8%2C0.7-1.8%2C1.6v33.8c0%2C6.2%2C3.2%2C9.3%2C9.9%2C9.3h1.6c1.1%2C0%2C1.7-0.9%2C1.7-1.7%0D%0A%09%09%09C420.6%2C435.1%2C419.9%2C434.4%2C418.9%2C434.4z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%233A4646%22%20d%3D%22M380.9%2C407.6c-1%2C0-1.8%2C0.6-1.8%2C1.5v27c0%2C0.8%2C0.7%2C1.6%2C1.8%2C1.6c1%2C0%2C1.8-0.8%2C1.8-1.6v-27%0D%0A%09%09%09C382.7%2C408.2%2C382%2C407.6%2C380.9%2C407.6z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%233A4646%22%20d%3D%22M380.9%2C393.7c-1.5%2C0-2.6%2C1.1-2.6%2C2.5s1.2%2C2.6%2C2.6%2C2.6c1.4%2C0%2C2.6-1.2%2C2.6-2.6%0D%0A%09%09%09C383.5%2C394.8%2C382.3%2C393.7%2C380.9%2C393.7z%22/%3E%0D%0A%09%3C/g%3E%0D%0A%09%3Cg%3E%0D%0A%09%09%3Cpath%20fill%3D%22%235BC4BF%22%20d%3D%22M289.2%2C445.2c-0.7%2C0-1.3%2C0.3-1.6%2C0.7V442c0-0.3-0.4-0.6-0.9-0.6s-0.9%2C0.3-0.9%2C0.6v9.5%0D%0A%09%09%09c0%2C0.3%2C0.3%2C0.6%2C0.9%2C0.6c0.5%2C0%2C0.9-0.3%2C0.9-0.6V448c0-0.8%2C0.6-1.3%2C1.2-1.3c0.7%2C0%2C1.2%2C0.6%2C1.2%2C1.3v3.4c0%2C0.4%2C0.4%2C0.6%2C0.9%2C0.6%0D%0A%09%09%09c0.4%2C0%2C0.9-0.2%2C0.9-0.6V448C291.8%2C446.5%2C290.6%2C445.2%2C289.2%2C445.2z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%235BC4BF%22%20d%3D%22M333.8%2C446.7c0.3%2C0%2C0.5-0.3%2C0.5-0.7s-0.2-0.7-0.5-0.7h-1.5v-2.4c0-0.4-0.5-0.6-0.9-0.6s-0.9%2C0.2-0.9%2C0.6v7%0D%0A%09%09%09c0%2C1.5%2C0.9%2C2.3%2C2.6%2C2.3h0.5c0.5%2C0%2C0.8-0.4%2C0.8-0.8s-0.2-0.8-0.8-0.8h-0.5c-0.6%2C0-0.8-0.2-0.8-0.8v-3.2h1.5V446.7z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%235BC4BF%22%20d%3D%22M326.3%2C445.9c0.2-0.1%2C0.3-0.3%2C0.3-0.5c0-0.3-0.3-0.6-0.7-0.6c-0.2%2C0-0.6%2C0.2-1%2C0.9%0D%0A%09%09%09c-0.4-0.3-0.9-0.4-1.5-0.4c-1.6%2C0-2.7%2C1-2.7%2C2.4v0.4c0%2C0.7%2C0.3%2C1.3%2C0.7%2C1.7c-0.3%2C0.2-0.6%2C0.5-0.6%2C1.1c0%2C0.5%2C0.2%2C0.7%2C0.5%2C0.9%0D%0A%09%09%09c-0.6%2C0.4-0.9%2C0.9-0.9%2C1.5c0%2C1.2%2C1.3%2C2%2C3.1%2C2c1.9%2C0%2C3-0.9%2C3-2.2c0-1.7-1.7-1.9-2.9-2.1c-0.7-0.1-1.3-0.2-1.3-0.4%0D%0A%09%09%09c0-0.1%2C0-0.1%2C0.1-0.2c0.3%2C0.1%2C0.6%2C0.1%2C0.9%2C0.1c1.6%2C0%2C2.7-1%2C2.7-2.4v-0.4c0-0.5-0.1-1-0.4-1.3C325.9%2C446.1%2C326.1%2C446%2C326.3%2C445.9z%0D%0A%09%09%09%20M324.4%2C447.6v0.4c0%2C0.7-0.4%2C1.2-1%2C1.2c-0.6%2C0-1-0.5-1-1.2v-0.4c0-0.6%2C0.3-1.2%2C1-1.2C324%2C446.4%2C324.4%2C446.9%2C324.4%2C447.6z%0D%0A%09%09%09%20M324.7%2C453c0%2C0.6-0.5%2C0.9-1.4%2C0.9c-0.9%2C0-1.4-0.4-1.4-0.9s0.4-0.7%2C0.8-0.9C323.8%2C452.2%2C324.7%2C452.3%2C324.7%2C453z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%235BC4BF%22%20d%3D%22M297.4%2C449.2c0.6%2C0%2C1.4%2C0%2C1.4-1.3c0-1.5-1.3-2.7-2.9-2.7c-1.7%2C0-3%2C1.3-3%2C2.8v1.3c0%2C1.7%2C1.4%2C2.9%2C3.3%2C2.9%0D%0A%09%09%09c1.4%2C0%2C2.4-0.6%2C2.4-1.2c0-0.3-0.3-0.8-0.7-0.8c-0.2%2C0-0.3%2C0.1-0.5%2C0.2c-0.3%2C0.1-0.6%2C0.3-1.2%2C0.3c-1%2C0-1.6-0.5-1.6-1.4v-0.2h2.8%0D%0A%09%09%09V449.2z%20M294.6%2C448v-0.3c0-0.6%2C0.6-1.1%2C1.3-1.1c0.7%2C0%2C1.2%2C0.5%2C1.2%2C1.2c0%2C0.2%2C0%2C0.3-0.4%2C0.3h-2.1V448z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%235BC4BF%22%20d%3D%22M306.6%2C445.2c-0.8%2C0-1.3%2C0.4-1.7%2C0.7l0%2C0c0-0.4-0.3-0.6-0.8-0.6s-0.8%2C0.3-0.8%2C0.6v8.8%0D%0A%09%09%09c0%2C0.4%2C0.4%2C0.6%2C0.8%2C0.6c0.5%2C0%2C0.9-0.3%2C0.9-0.6v-3.1c0.4%2C0.4%2C1%2C0.7%2C1.7%2C0.7c1.4%2C0%2C2.6-1.3%2C2.6-2.9V448%0D%0A%09%09%09C309.2%2C446.5%2C308%2C445.2%2C306.6%2C445.2z%20M307.5%2C448.1v1.3c0%2C0.7-0.6%2C1.3-1.3%2C1.3c-0.7%2C0-1.2-0.7-1.2-1v-1.6c0-0.8%2C0.6-1.3%2C1.2-1.3%0D%0A%09%09%09C306.9%2C446.8%2C307.5%2C447.5%2C307.5%2C448.1z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%235BC4BF%22%20d%3D%22M300.9%2C441.4c-0.5%2C0-0.9%2C0.3-0.9%2C0.6v9.5c0%2C0.3%2C0.3%2C0.6%2C0.9%2C0.6c0.5%2C0%2C0.9-0.3%2C0.9-0.6V442%0D%0A%09%09%09C301.8%2C441.7%2C301.4%2C441.4%2C300.9%2C441.4z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%235BC4BF%22%20d%3D%22M311.3%2C445.3c-0.5%2C0-0.9%2C0.3-0.9%2C0.6v5.6c0%2C0.3%2C0.4%2C0.6%2C0.9%2C0.6s0.9-0.3%2C0.9-0.6v-5.6%0D%0A%09%09%09C312.1%2C445.5%2C311.8%2C445.3%2C311.3%2C445.3z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%235BC4BF%22%20d%3D%22M338.8%2C445.2c-0.7%2C0-1.3%2C0.3-1.6%2C0.7V442c0-0.3-0.4-0.6-0.9-0.6s-0.9%2C0.3-0.9%2C0.6v9.5%0D%0A%09%09%09c0%2C0.3%2C0.3%2C0.6%2C0.9%2C0.6c0.5%2C0%2C0.9-0.3%2C0.9-0.6V448c0-0.8%2C0.6-1.3%2C1.2-1.3c0.7%2C0%2C1.2%2C0.6%2C1.2%2C1.3v3.4c0%2C0.4%2C0.4%2C0.6%2C0.9%2C0.6%0D%0A%09%09%09c0.4%2C0%2C0.9-0.2%2C0.9-0.6V448C341.3%2C446.5%2C340.1%2C445.2%2C338.8%2C445.2z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%235BC4BF%22%20d%3D%22M317%2C445.2c-0.8%2C0-1.3%2C0.3-1.7%2C0.7l0%2C0c0-0.3-0.3-0.6-0.8-0.6s-0.9%2C0.3-0.9%2C0.6v5.6c0%2C0.3%2C0.3%2C0.6%2C0.9%2C0.6%0D%0A%09%09%09c0.5%2C0%2C0.9-0.3%2C0.9-0.6v-3.4c0-0.8%2C0.6-1.3%2C1.2-1.3c0.7%2C0%2C1.2%2C0.7%2C1.2%2C1.3v3.4c0%2C0.4%2C0.4%2C0.6%2C0.9%2C0.6s0.9-0.3%2C0.9-0.6v-3.4%0D%0A%09%09%09C319.5%2C446.5%2C318.3%2C445.2%2C317%2C445.2z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%235BC4BF%22%20d%3D%22M311.3%2C442.1c-0.5%2C0-1%2C0.4-1%2C0.9s0.5%2C0.9%2C1%2C0.9s1-0.4%2C1-0.9C312.3%2C442.5%2C311.8%2C442.1%2C311.3%2C442.1z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%235BC4BF%22%20d%3D%22M409.5%2C442.1c-0.5%2C0-1%2C0.4-1%2C0.9s0.5%2C0.9%2C1%2C0.9s1-0.4%2C1-0.9C410.5%2C442.5%2C410%2C442.1%2C409.5%2C442.1z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%235BC4BF%22%20d%3D%22M404.7%2C445.2c-0.8%2C0-1.6%2C0.4-2.1%2C1.2c-0.4-0.7-1.1-1.2-1.8-1.2c-0.8%2C0-1.4%2C0.4-1.7%2C0.7l0%2C0%0D%0A%09%09%09c0-0.3-0.3-0.6-0.8-0.6s-0.9%2C0.3-0.9%2C0.6v5.6c0%2C0.3%2C0.3%2C0.6%2C0.9%2C0.6c0.5%2C0%2C0.9-0.3%2C0.9-0.6v-3.4c0-0.9%2C0.6-1.3%2C1.1-1.3%0D%0A%09%09%09s1.1%2C0.4%2C1.1%2C1.3v3.4c0%2C0.4%2C0.4%2C0.6%2C0.9%2C0.6s0.9-0.2%2C0.9-0.6V448c0-0.8%2C0.6-1.3%2C1.1-1.3c0.6%2C0%2C1.1%2C0.4%2C1.1%2C1.3v3.4%0D%0A%09%09%09c0%2C0.3%2C0.4%2C0.6%2C0.9%2C0.6s0.9-0.3%2C0.9-0.6V448C407.3%2C446.2%2C406%2C445.2%2C404.7%2C445.2z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%235BC4BF%22%20d%3D%22M394.2%2C448c-0.7-0.2-1.1-0.3-1.1-0.7c0-0.3%2C0.3-0.7%2C1-0.7c0.4%2C0%2C0.8%2C0.1%2C1%2C0.3c0.2%2C0.1%2C0.3%2C0.1%2C0.5%2C0.1%0D%0A%09%09%09c0.5%2C0%2C0.7-0.6%2C0.7-0.8c0-0.6-1.1-1-2.2-1c-1.8%2C0-2.5%2C1.1-2.5%2C2.1c0%2C1.4%2C1.1%2C1.7%2C2%2C2c0.8%2C0.2%2C1.3%2C0.4%2C1.3%2C0.9s-0.6%2C0.6-1%2C0.6%0D%0A%09%09%09c-0.7%2C0-1.1-0.2-1.3-0.4c-0.2-0.1-0.3-0.2-0.5-0.2c-0.4%2C0-0.6%2C0.5-0.6%2C0.8c0%2C0.7%2C1.1%2C1.2%2C2.5%2C1.2c1.6%2C0%2C2.6-0.8%2C2.6-2.1%0D%0A%09%09%09C396.4%2C448.6%2C395.1%2C448.2%2C394.2%2C448z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%235BC4BF%22%20d%3D%22M409.5%2C445.3c-0.5%2C0-0.9%2C0.3-0.9%2C0.6v5.6c0%2C0.3%2C0.4%2C0.6%2C0.9%2C0.6s0.9-0.3%2C0.9-0.6v-5.6%0D%0A%09%09%09C410.3%2C445.5%2C410%2C445.3%2C409.5%2C445.3z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%235BC4BF%22%20d%3D%22M412.7%2C441.4c-0.5%2C0-0.9%2C0.3-0.9%2C0.6v9.5c0%2C0.3%2C0.3%2C0.6%2C0.9%2C0.6c0.5%2C0%2C0.9-0.3%2C0.9-0.6V442%0D%0A%09%09%09C413.6%2C441.7%2C413.2%2C441.4%2C412.7%2C441.4z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%235BC4BF%22%20d%3D%22M384.6%2C445.2c-0.8%2C0-1.3%2C0.3-1.7%2C0.7l0%2C0c0-0.3-0.3-0.6-0.8-0.6s-0.9%2C0.3-0.9%2C0.6v5.6%0D%0A%09%09%09c0%2C0.3%2C0.3%2C0.6%2C0.9%2C0.6c0.5%2C0%2C0.9-0.3%2C0.9-0.6v-3.4c0-0.8%2C0.6-1.3%2C1.2-1.3c0.7%2C0%2C1.2%2C0.7%2C1.2%2C1.3v3.4c0%2C0.4%2C0.4%2C0.6%2C0.9%2C0.6%0D%0A%09%09%09s0.9-0.3%2C0.9-0.6v-3.4C387.1%2C446.5%2C386%2C445.2%2C384.6%2C445.2z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%235BC4BF%22%20d%3D%22M419.9%2C450.2c-0.2%2C0-0.3%2C0.1-0.5%2C0.2c-0.3%2C0.1-0.6%2C0.3-1.2%2C0.3c-1%2C0-1.6-0.5-1.6-1.4v-0.2h2.8%0D%0A%09%09%09c0.6%2C0%2C1.4%2C0%2C1.4-1.3c0-1.5-1.3-2.7-2.9-2.7c-1.7%2C0-3%2C1.3-3%2C2.8v1.3c0%2C1.7%2C1.4%2C2.9%2C3.3%2C2.9c1.4%2C0%2C2.4-0.6%2C2.4-1.2%0D%0A%09%09%09C420.6%2C450.7%2C420.4%2C450.2%2C419.9%2C450.2z%20M416.5%2C447.7c0-0.6%2C0.6-1.1%2C1.3-1.1c0.7%2C0%2C1.2%2C0.5%2C1.2%2C1.2c0%2C0.2%2C0%2C0.3-0.4%2C0.3h-2.1V447.7%0D%0A%09%09%09z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%235BC4BF%22%20d%3D%22M377%2C445.2c-1.6%2C0-3%2C1.3-3%2C2.9v1.3c0%2C1.5%2C1.4%2C2.9%2C3%2C2.9c1.6%2C0%2C3-1.3%2C3-2.9v-1.3%0D%0A%09%09%09C380.1%2C446.5%2C378.7%2C445.2%2C377%2C445.2z%20M378.3%2C449.3c0%2C0.7-0.6%2C1.3-1.3%2C1.3c-0.7%2C0-1.3-0.6-1.3-1.3V448c0-0.6%2C0.5-1.3%2C1.3-1.3%0D%0A%09%09%09c0.8%2C0%2C1.3%2C0.7%2C1.3%2C1.3V449.3z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%235BC4BF%22%20d%3D%22M346.9%2C449.2c0.6%2C0%2C1.4%2C0%2C1.4-1.3c0-1.5-1.3-2.7-2.9-2.7c-1.7%2C0-3%2C1.3-3%2C2.8v1.3c0%2C1.7%2C1.4%2C2.9%2C3.3%2C2.9%0D%0A%09%09%09c1.4%2C0%2C2.4-0.6%2C2.4-1.2c0-0.3-0.3-0.8-0.7-0.8c-0.2%2C0-0.3%2C0.1-0.5%2C0.2c-0.3%2C0.1-0.6%2C0.3-1.2%2C0.3c-1%2C0-1.6-0.5-1.6-1.4v-0.2h2.8%0D%0A%09%09%09V449.2z%20M344.2%2C447.7c0-0.6%2C0.6-1.1%2C1.3-1.1c0.7%2C0%2C1.2%2C0.5%2C1.2%2C1.2c0%2C0.2%2C0%2C0.3-0.4%2C0.3h-2.1V447.7z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%235BC4BF%22%20d%3D%22M362.1%2C445.2c-0.9%2C0-2.2%2C0.4-2.2%2C1c0%2C0.4%2C0.2%2C0.9%2C0.7%2C0.9c0.1%2C0%2C0.2-0.1%2C0.4-0.1c0.2-0.1%2C0.5-0.3%2C1.2-0.3%0D%0A%09%09%09c0.9%2C0%2C1.2%2C0.7%2C1.2%2C1.3v0.1h-0.5c-1.6%2C0-3.4%2C0.2-3.4%2C2.1c0%2C1.2%2C0.8%2C2%2C2%2C2c0.7%2C0%2C1.4-0.3%2C1.9-0.8v0.1c0%2C0.4%2C0.4%2C0.6%2C0.8%2C0.6%0D%0A%09%09%09c0.5%2C0%2C0.8-0.3%2C0.8-0.6V448C365.1%2C446.3%2C363.9%2C445.2%2C362.1%2C445.2z%20M363.2%2C449.2v0.4c0%2C0.6-0.7%2C1.2-1.3%2C1.2c-0.3%2C0-0.7-0.1-0.7-0.7%0D%0A%09%09%09c0-0.7%2C0.7-0.8%2C1.9-0.8h0.1V449.2z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%235BC4BF%22%20d%3D%22M355.9%2C445.2c-0.8%2C0-1.3%2C0.3-1.7%2C0.7l0%2C0c0-0.3-0.3-0.6-0.8-0.6s-0.9%2C0.3-0.9%2C0.6v5.6%0D%0A%09%09%09c0%2C0.3%2C0.3%2C0.6%2C0.9%2C0.6c0.5%2C0%2C0.9-0.3%2C0.9-0.6v-3.4c0-0.8%2C0.6-1.3%2C1.2-1.3c0.7%2C0%2C1.2%2C0.7%2C1.2%2C1.3v3.4c0%2C0.4%2C0.4%2C0.6%2C0.9%2C0.6%0D%0A%09%09%09s0.9-0.3%2C0.9-0.6v-3.4C358.5%2C446.5%2C357.3%2C445.2%2C355.9%2C445.2z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%235BC4BF%22%20d%3D%22M369.7%2C446.7c0.3%2C0%2C0.5-0.3%2C0.5-0.7s-0.2-0.7-0.5-0.7h-1.5v-2.4c0-0.4-0.5-0.6-0.9-0.6%0D%0A%09%09%09c-0.4%2C0-0.9%2C0.2-0.9%2C0.6v7c0%2C1.5%2C0.9%2C2.3%2C2.6%2C2.3h0.5c0.5%2C0%2C0.8-0.4%2C0.8-0.8s-0.2-0.8-0.8-0.8H369c-0.6%2C0-0.8-0.2-0.8-0.8v-3.2%0D%0A%09%09%09h1.5V446.7z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%235BC4BF%22%20d%3D%22M372%2C445.3c-0.5%2C0-0.9%2C0.3-0.9%2C0.6v5.6c0%2C0.3%2C0.4%2C0.6%2C0.9%2C0.6s0.9-0.3%2C0.9-0.6v-5.6%0D%0A%09%09%09C372.9%2C445.5%2C372.5%2C445.3%2C372%2C445.3z%22/%3E%0D%0A%09%09%3Cpath%20fill%3D%22%235BC4BF%22%20d%3D%22M372%2C442.1c-0.5%2C0-1%2C0.4-1%2C0.9s0.5%2C0.9%2C1%2C0.9s1-0.4%2C1-0.9C373%2C442.5%2C372.5%2C442.1%2C372%2C442.1z%22/%3E%0D%0A%09%3C/g%3E%0D%0A%09%3Cpath%20fill%3D%22%235BC4BF%22%20d%3D%22M185.2%2C449.9c-4.9-1.2-7.3-3.8-7.3-7.9c0-1.3%2C0.2-3%2C0.6-5c0.5-2.5%2C0.7-4.4%2C0.7-5.9c0-2.6-0.9-5-2.7-7.3%0D%0A%09%09c-1.5-1.8-3.4-3.3-5.8-4.4c2.1-1%2C4-2.3%2C5.5-4c1.9-2.1%2C2.9-4.6%2C2.9-7.6c0-1.5-0.2-3.5-0.7-6c-0.4-2-0.6-3.7-0.6-5%0D%0A%09%09c0-1.7%2C0.6-3.3%2C1.8-4.8c1.2-1.5%2C3-2.4%2C5.4-2.8l0.5-0.1v-2.5l-0.7%2C0.1c-3.7%2C0.5-6.9%2C2.1-9.4%2C4.7c-2.5%2C2.7-3.8%2C5.8-3.8%2C9.4%0D%0A%09%09c0%2C1.6%2C0.3%2C3.5%2C0.8%2C5.7s0.8%2C3.9%2C0.8%2C5.1c0%2C1.6-0.6%2C2.9-1.9%2C4.2c-1.3%2C1.3-3%2C2.1-5.1%2C2.4l-0.6%2C0.1v2.4l0.5%2C0.1%0D%0A%09%09c2.4%2C0.4%2C4.2%2C1.3%2C5.4%2C2.6c1.1%2C1.3%2C1.7%2C2.7%2C1.7%2C4.2c0%2C1.1-0.3%2C2.8-0.8%2C5.1c-0.5%2C2.1-0.7%2C3.9-0.7%2C5.5c0%2C3.4%2C1.3%2C6.5%2C3.8%2C9.4%0D%0A%09%09c2.5%2C2.7%2C5.6%2C4.3%2C9.4%2C4.7l0.7%2C0.1v-2.4L185.2%2C449.9z%22/%3E%0D%0A%09%3Cpath%20fill%3D%22%235BC4BF%22%20d%3D%22M278.1%2C418.2c-2.1-0.4-3.8-1.2-5.1-2.4c-1.3-1.2-1.9-2.6-1.9-4.2c0-1.1%2C0.3-2.8%2C0.8-5.1%0D%0A%09%09c0.5-2.2%2C0.8-4.1%2C0.8-5.7c0-3.5-1.3-6.8-3.8-9.5c-2.5-2.6-5.7-4.2-9.4-4.7h-0.8v2.6l0.5%2C0.1c2.4%2C0.4%2C4.2%2C1.3%2C5.4%2C2.8%0D%0A%09%09s1.8%2C3.1%2C1.8%2C4.8c0%2C1.4-0.2%2C3-0.6%2C5c-0.5%2C2.4-0.7%2C4.4-0.7%2C5.9c0%2C2.9%2C1%2C5.4%2C2.9%2C7.6c1.5%2C1.7%2C3.4%2C3%2C5.5%2C4c-2.4%2C1.1-4.3%2C2.6-5.8%2C4.4%0D%0A%09%09c-1.8%2C2.2-2.7%2C4.6-2.7%2C7.3c0%2C1.4%2C0.3%2C3.4%2C0.7%2C5.9c0.4%2C2%2C0.6%2C3.7%2C0.6%2C5c0%2C4-2.4%2C6.7-7.3%2C7.9l-0.5%2C0.1v2.4l0.7-0.1%0D%0A%09%09c3.7-0.4%2C6.9-2%2C9.4-4.7s3.8-5.8%2C3.8-9.4c0-1.6-0.2-3.4-0.7-5.5c-0.5-2.3-0.8-4-0.8-5.1c0-1.5%2C0.6-2.8%2C1.7-4.2%0D%0A%09%09c1.1-1.3%2C2.9-2.2%2C5.4-2.6l0.5-0.1v-2.4H278.1z%22/%3E%0D%0A%3C/g%3E%0D%0A%3C/svg%3E%0D%0A') no-repeat left top transparent;
            background-size: contain;
            max-width: 265px;
            height: 70px;
            display: block;
            text-indent: -9999em;
            margin: 40px auto;
        }

        /* Page specific styles */
        .wrapper {
            margin-left: auto;
            margin-right: auto;
            max-width: 1024px;
            padding: 0 20px;
        }

        .login-form {
            max-width: 540px;
            margin: 0 auto;
            padding: 20px;
            border: 2px solid #778988;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        label {
            cursor: pointer;
            margin-bottom: 2px;
            display: block;
            font-weight: bold;
        }

        input,
        textarea,
        select {
            border-radius: 4px;
            width: 100%;
            display: block;
            padding: 8px 10px;
            margin: 0;
            border: 1px solid #91a09f;
            box-shadow: none;
            background-color: white;
            cursor: pointer;
            box-sizing: border-box;
        }

            input:hover,
            textarea:hover,
            select:hover,
            input:focus,
            textarea:focus,
            select:focus {
                border-color: #576161;
                outline: none;
            }

        .button {
            border-radius: 4px;
            background-color: #b455a0;
            border: none;
            color: white;
            padding: 8px 16px;
            display: inline-block;
            cursor: pointer;
            font-size: 1.2em;
            font-weight: normal;
            margin: 0;
            text-decoration: none;
            width: auto;
            box-shadow: none;
        }

            .button:hover {
                background-color: #7d3b70;
                text-decoration: underline;
            }
    </style>
</head>
<body>
    <main>
        <div class="mydentist-logo">{my}dentist - Helping the nation smile</div>

        <h1 class="align-center">Workflow login</h1>

        <div class="wrapper">
            <div data-form="Login" class="login-form">
                <p data-error="" style="text-align: center; border: 2px solid #a24c90; background-color: #b455a0; color: #FFF; font-size: 16px; padding: 4px 8px; border-radius: 8px; display: none"></p>
                <fieldset>
                    <p>
                        @Html.LabelFor(model => model.UserId, "Email or User ID")
                        @Html.TextBoxFor(model => model.UserId, new { data_propertyname = "UserId" })
                    </p>
                    <p>
                        @Html.LabelFor(model => model.Password, "Password")
                        @Html.PasswordFor(model => model.Password, new { data_propertyname = "Password" })
                    </p>
                    <p>
                        @Html.HiddenFor(model => model.KeyParameter, new { data_propertyname = "KeyParameter" })
                    </p>
                </fieldset>
                <p class="form-buttons">
                    <input class="button" type="button" value="Log in" data-submit="Login" />
                </p>
            </div>
        </div>
    </main>

    <script src="https://code.jquery.com/jquery-3.6.1.min.js"></script>
    <script>

        function LoginPage() {

            var self = this;

            self.$form = $("[data-form='Login']");
            self.$error = $("[data-error]");
            self.$submit = $("[data-submit='Login']");

            self.addEventHandlers = function() {

                self.$submit.on("click", function() {

                    self.authenticate();
                });

                self.$form.on("keydown", "input, button", function(event) {

                    if (event.key === "Enter" || event.keyCode === 13) {

                        self.authenticate();
                    }
                });
            }

            self.authenticate = function() {

                var payload = {
                    userId: $("input[data-propertyname='UserId']").val(),
                    password: $("input[data-propertyname='Password']").val()
                };

                $.ajax({
                    cache: false,
                    method: "POST",
                    url: "./Login",
                    data: JSON.stringify(payload),
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    success: function(result) {

                        if (result.error !== null) {

                            self.$error.text(result.error).show();
                            return;
                        }

                        self.$error.text("").hide();

                        var token = result.value;
                        var imageId = $("[data-propertyname='KeyParameter']").val();

                        window.localStorage.setItem("PublicUserToken", token);
                        window.location.href = `./ApproveClinician?imageid=${imageId}&token=${token}`;
                    },
                    error: (function(xhr) {

                        if (xhr.responseJSON !== null && xhr.responseJSON.Error !== null) {
                            
                            self.$error.text(xhr.responseJSON.error).show();
                        }
                        else
                        {
                            self.$error.text("Failed to authenticate. Please contact the IT Helpdesk.").show();
                        }
                    })
                });

            }
        }

        var loginPage = new LoginPage();
        loginPage.addEventHandlers();
    </script>
</body>
</html>