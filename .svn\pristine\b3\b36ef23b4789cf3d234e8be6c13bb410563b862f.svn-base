﻿using Mydentist.MyDSitefinityAPI.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Mydentist.MyDSitefinityAPI.WebConfigApi
{
    public class WebsiteConfigRoleService : IWebsiteConfigRoleService
    {
        private readonly IWebsiteConfigWrapperService _websiteConfigWrapperService;

        public WebsiteConfigRoleService(IWebsiteConfigWrapperService websiteConfigWrapperService)
        {
            _websiteConfigWrapperService = websiteConfigWrapperService;
        }

        public async Task<WebsiteConfigModel> GetDesiredRoleConfig(string websiteRole)
        {
            websiteRole = websiteRole.Replace("/", "%2f");
            string url = $"api/SearchTerms/{websiteRole}/websiteRole";

            return await _websiteConfigWrapperService.GetApiResponse<WebsiteConfigModel>(url);
        }

        public async Task<IEnumerable<RolePostgraduateLookup>> GetRolePostgraduates()
        {
            string url = $"RolePostgraduateLookup";
            return await _websiteConfigWrapperService.GetApiResponse<IEnumerable<RolePostgraduateLookup>>(url);
        }

    }

    public interface IWebsiteConfigRoleService
    {
        Task<WebsiteConfigModel> GetDesiredRoleConfig(string websiteRole);
        Task<IEnumerable<RolePostgraduateLookup>> GetRolePostgraduates();
    }
}
