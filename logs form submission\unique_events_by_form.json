[{"Name": "Form is submitted", "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49", "Item": {"ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4473\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"<PERSON>wa\",\"lastName\":\"<PERSON>aa<PERSON>\",\"email\":\"<EMAIL>\",\"phone\":\"07888857818\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"NE3 4JS\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}", "FileFieldController": [{"Id": "cc31af37-e608-4dfc-a7d9-c0093b29d70c", "ChildItemId": "a6ef4af4-a649-4a37-a43c-5eb20947a53b", "ParentItemId": "1ac55c9e-cae7-6322-9971-ff0200979a84", "ChildItemProviderName": "OpenAccessDataProvider", "ParentItemProviderName": null, "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document", "ChildItemAdditionalInfo": "4473_<PERSON><PERSON>_Jaafar.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4473_marwa_jaafar.pdf?Status=Master", "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication", "ParentItemAdditionalInfo": null, "LastModified": "2025-07-28T09:18:59.143Z", "Ordinal": 0.0, "Attributes": {}, "ApplicationName": "GroupWebsite/", "ComponentPropertyName": "1ac55c9e-cae7-6322-9971-ff0200979a84", "AvailableForLive": false, "AvailableForMaster": false, "AvailableForTemp": false, "IsParentDeleted": false, "IsChildDeleted": false, "FileName": "4473_<PERSON><PERSON>_Jaafar.pdf", "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4473_marwa_jaafar.pdf?Status=Master"}]}, "OriginalEvent": {"EntryId": "1ac55c9e-cae7-6322-9971-ff0200979a84", "ReferralCode": "5993", "UserId": "00000000-0000-0000-0000-000000000000", "Username": "", "IpAddress": "************", "SubmissionTime": "2025-07-28T09:18:59.0956303Z", "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84", "FormName": "sf_recruitmentjobapplication", "FormTitle": "Recruitment Job Application", "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000", "SendConfirmationEmail": false, "Controls": [{"FieldControlName": null, "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84", "SiblingId": "00000000-0000-0000-0000-000000000000", "Text": null, "Type": 0, "Title": "PayloadJson", "FieldName": "ParagraphTextFieldController", "Value": "{\"Jobinfo\":{\"jobId\":\"4473\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"<PERSON>wa\",\"lastName\":\"<PERSON>aa<PERSON>\",\"email\":\"<EMAIL>\",\"phone\":\"07888857818\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"NE3 4JS\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"7\",\"Question\":\"Do you have any face to face customer service experience?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"10\",\"AnswerValue\":\"10\"}],\"FollowUpQuestions\":null}]}", "OldValue": null}, {"FieldControlName": null, "Id": "ded52a9d-cae7-6322-9971-ff0000979a84", "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 1, "Title": "Untitled", "FieldName": "FileFieldController", "Value": [{"Id": "cc31af37-e608-4dfc-a7d9-c0093b29d70c", "ChildItemId": "a6ef4af4-a649-4a37-a43c-5eb20947a53b", "ParentItemId": "1ac55c9e-cae7-6322-9971-ff0200979a84", "ChildItemProviderName": "OpenAccessDataProvider", "ParentItemProviderName": null, "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document", "ChildItemAdditionalInfo": "4473_<PERSON><PERSON>_Jaafar.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4473_marwa_jaafar.pdf?Status=Master", "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication", "ParentItemAdditionalInfo": null, "LastModified": "2025-07-28T09:18:59.143Z", "Ordinal": 0.0, "Attributes": {}, "ApplicationName": "GroupWebsite/", "ComponentPropertyName": "1ac55c9e-cae7-6322-9971-ff0200979a84", "AvailableForLive": false, "AvailableForMaster": false, "AvailableForTemp": false, "IsParentDeleted": false, "IsChildDeleted": false, "FileName": "4473_<PERSON><PERSON>_Jaafar.pdf", "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4473_marwa_jaafar.pdf?Status=Master"}], "OldValue": null}], "Origin": null}, "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"}, {"Name": "Form is submitted", "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49", "Item": {"ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4487\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"<PERSON> \",\"lastName\":\"<PERSON> \",\"email\":\"<PERSON><PERSON><PERSON><EMAIL>\",\"phone\":\"07903569574\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Sw17 0ea\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"8\",\"Question\":\"What Postcode, Town or City would you like to work near? \",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"Tooting/Wandsworth \"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"10\",\"Question\":\"Are you registered with the GDC?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"11\",\"AnswerValue\":\"11\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"9\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"160037\"}],\"ParentQuestionId\":10}]},{\"QuestionId\":\"11\",\"Question\":\"The questions below are for those not yet GDC registered, if you are a qualified nurse please select 'none applicable' option. \\n\\nWould you be comfortable assisting with dental procedures?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"13\",\"AnswerValue\":\"13\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"12\",\"Question\":\"Are you able to commit to a 12-18 month training program?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"17\",\"AnswerValue\":\"17\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"14\",\"Question\":\"Please select the option that best describes you\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"19\",\"AnswerValue\":\"19\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"15\",\"Question\":\"If you are currently/already registered on a Dental Nursing course, please provide the following details:\\n\\nWhat is the date you started on your current programme?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"16\",\"Question\":\"Who is your current training provider?\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"17\",\"Question\":\"Please provide contact details for your current training provider\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"19\",\"Question\":\"What is your predicted completion date?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null}]}", "FileFieldController": [{"Id": "75dab35d-be42-4d4d-8395-6f4c9174286d", "ChildItemId": "273f70f6-519a-40f9-a722-b63f8a6d4cc4", "ParentItemId": "1dc85c9e-cae7-6322-9971-ff0200979a84", "ChildItemProviderName": "OpenAccessDataProvider", "ParentItemProviderName": null, "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document", "ChildItemAdditionalInfo": "4487_<PERSON> _<PERSON> .pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master", "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication", "ParentItemAdditionalInfo": null, "LastModified": "2025-07-28T13:23:11.637Z", "Ordinal": 0.0, "Attributes": {}, "ApplicationName": "GroupWebsite/", "ComponentPropertyName": "1dc85c9e-cae7-6322-9971-ff0200979a84", "AvailableForLive": false, "AvailableForMaster": false, "AvailableForTemp": false, "IsParentDeleted": false, "IsChildDeleted": false, "FileName": "4487_<PERSON> _<PERSON> .pdf", "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master"}]}, "OriginalEvent": {"EntryId": "1dc85c9e-cae7-6322-9971-ff0200979a84", "ReferralCode": "5996", "UserId": "00000000-0000-0000-0000-000000000000", "Username": "", "IpAddress": "*************", "SubmissionTime": "2025-07-28T13:23:11.6222822Z", "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84", "FormName": "sf_recruitmentjobapplication", "FormTitle": "Recruitment Job Application", "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000", "SendConfirmationEmail": false, "Controls": [{"FieldControlName": null, "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84", "SiblingId": "00000000-0000-0000-0000-000000000000", "Text": null, "Type": 0, "Title": "PayloadJson", "FieldName": "ParagraphTextFieldController", "Value": "{\"Jobinfo\":{\"jobId\":\"4487\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"<PERSON> \",\"lastName\":\"<PERSON> \",\"email\":\"<PERSON><PERSON><PERSON><EMAIL>\",\"phone\":\"07903569574\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"Sw17 0ea\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.*********\"},\"jobQuestions\":[{\"QuestionId\":\"8\",\"Question\":\"What Postcode, Town or City would you like to work near? \",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"Tooting/Wandsworth \"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"10\",\"Question\":\"Are you registered with the GDC?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"11\",\"AnswerValue\":\"11\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"9\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"160037\"}],\"ParentQuestionId\":10}]},{\"QuestionId\":\"11\",\"Question\":\"The questions below are for those not yet GDC registered, if you are a qualified nurse please select 'none applicable' option. \\n\\nWould you be comfortable assisting with dental procedures?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"13\",\"AnswerValue\":\"13\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"12\",\"Question\":\"Are you able to commit to a 12-18 month training program?\\n\\nNote: If you answer 'No' to this question, you are unable to commit to our programme right now and we'll be unable to take your application forward. However, please feel free to reapply in the future, if anything changes.\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"17\",\"AnswerValue\":\"17\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"14\",\"Question\":\"Please select the option that best describes you\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"19\",\"AnswerValue\":\"19\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"15\",\"Question\":\"If you are currently/already registered on a Dental Nursing course, please provide the following details:\\n\\nWhat is the date you started on your current programme?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"16\",\"Question\":\"Who is your current training provider?\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"17\",\"Question\":\"Please provide contact details for your current training provider\",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"19\",\"Question\":\"What is your predicted completion date?\",\"QuestionType\":\"Date\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"\"}],\"FollowUpQuestions\":null}]}", "OldValue": null}, {"FieldControlName": null, "Id": "ded52a9d-cae7-6322-9971-ff0000979a84", "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 1, "Title": "Untitled", "FieldName": "FileFieldController", "Value": [{"Id": "75dab35d-be42-4d4d-8395-6f4c9174286d", "ChildItemId": "273f70f6-519a-40f9-a722-b63f8a6d4cc4", "ParentItemId": "1dc85c9e-cae7-6322-9971-ff0200979a84", "ChildItemProviderName": "OpenAccessDataProvider", "ParentItemProviderName": null, "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document", "ChildItemAdditionalInfo": "4487_<PERSON> _<PERSON> .pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master", "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication", "ParentItemAdditionalInfo": null, "LastModified": "2025-07-28T13:23:11.637Z", "Ordinal": 0.0, "Attributes": {}, "ApplicationName": "GroupWebsite/", "ComponentPropertyName": "1dc85c9e-cae7-6322-9971-ff0200979a84", "AvailableForLive": false, "AvailableForMaster": false, "AvailableForTemp": false, "IsParentDeleted": false, "IsChildDeleted": false, "FileName": "4487_<PERSON> _<PERSON> .pdf", "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4487_sarah-_lawrence-.pdf?Status=Master"}], "OldValue": null}], "Origin": null}, "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"}, {"Name": "Form is submitted", "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49", "Item": {"ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"2549\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"<PERSON> \",\"lastName\":\"<PERSON>\",\"email\":\"<EMAIL>\",\"phone\":\"7767 952712 \",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"CA13 0JA \",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.1545655197\"},\"jobQuestions\":[{\"QuestionId\":\"6\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"5\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"320786\"}],\"ParentQuestionId\":6}]}]}", "FileFieldController": [{"Id": "5a59d79a-d4c2-47cb-86b9-1b0746373c3b", "ChildItemId": "0bde2ef2-4b3d-4d74-ba63-b4e10ee8ccc7", "ParentItemId": "35ca5c9e-cae7-6322-9971-ff0200979a84", "ChildItemProviderName": "OpenAccessDataProvider", "ParentItemProviderName": null, "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document", "ChildItemAdditionalInfo": "2549_<PERSON> _<PERSON>.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/2549_sharon-treesa-_james.pdf?Status=Master", "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication", "ParentItemAdditionalInfo": null, "LastModified": "2025-07-28T19:05:25.947Z", "Ordinal": 0.0, "Attributes": {}, "ApplicationName": "GroupWebsite/", "ComponentPropertyName": "35ca5c9e-cae7-6322-9971-ff0200979a84", "AvailableForLive": false, "AvailableForMaster": false, "AvailableForTemp": false, "IsParentDeleted": false, "IsChildDeleted": false, "FileName": "2549_<PERSON> _<PERSON>.pdf", "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/2549_sharon-treesa-_james.pdf?Status=Master"}]}, "OriginalEvent": {"EntryId": "35ca5c9e-cae7-6322-9971-ff0200979a84", "ReferralCode": "6001", "UserId": "00000000-0000-0000-0000-000000000000", "Username": "", "IpAddress": "*************", "SubmissionTime": "2025-07-28T19:05:25.931617Z", "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84", "FormName": "sf_recruitmentjobapplication", "FormTitle": "Recruitment Job Application", "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000", "SendConfirmationEmail": false, "Controls": [{"FieldControlName": null, "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84", "SiblingId": "00000000-0000-0000-0000-000000000000", "Text": null, "Type": 0, "Title": "PayloadJson", "FieldName": "ParagraphTextFieldController", "Value": "{\"Jobinfo\":{\"jobId\":\"2549\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"<PERSON> \",\"lastName\":\"<PERSON>\",\"email\":\"<EMAIL>\",\"phone\":\"7767 952712 \",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"CA13 0JA \",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.1545655197\"},\"jobQuestions\":[{\"QuestionId\":\"6\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"5\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"320786\"}],\"ParentQuestionId\":6}]}]}", "OldValue": null}, {"FieldControlName": null, "Id": "ded52a9d-cae7-6322-9971-ff0000979a84", "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 1, "Title": "Untitled", "FieldName": "FileFieldController", "Value": [{"Id": "5a59d79a-d4c2-47cb-86b9-1b0746373c3b", "ChildItemId": "0bde2ef2-4b3d-4d74-ba63-b4e10ee8ccc7", "ParentItemId": "35ca5c9e-cae7-6322-9971-ff0200979a84", "ChildItemProviderName": "OpenAccessDataProvider", "ParentItemProviderName": null, "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document", "ChildItemAdditionalInfo": "2549_<PERSON> _<PERSON>.pdf|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/2549_sharon-treesa-_james.pdf?Status=Master", "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication", "ParentItemAdditionalInfo": null, "LastModified": "2025-07-28T19:05:25.947Z", "Ordinal": 0.0, "Attributes": {}, "ApplicationName": "GroupWebsite/", "ComponentPropertyName": "35ca5c9e-cae7-6322-9971-ff0200979a84", "AvailableForLive": false, "AvailableForMaster": false, "AvailableForTemp": false, "IsParentDeleted": false, "IsChildDeleted": false, "FileName": "2549_<PERSON> _<PERSON>.pdf", "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/2549_sharon-treesa-_james.pdf?Status=Master"}], "OldValue": null}], "Origin": null}, "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"}, {"Name": "Form is submitted", "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49", "Item": {"ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4496\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"<PERSON>isma\",\"lastName\":\"Nadeem \",\"email\":\"<EMAIL>\",\"phone\":\"07392807565\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"EH48 2HD\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.1748454867\"},\"jobQuestions\":[{\"QuestionId\":\"22\",\"Question\":\"What postcode / Town / City  would you like to work near? \",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"Edinburgh \"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"23\",\"Question\":\"Do you have a Grade C/4 or above in English GCSE and are able to provide evidence?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"32\",\"AnswerValue\":\"32\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"24\",\"Question\":\"Do you have a Grade C/4 or above in Maths GCSE and are able to provide evidence?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"34\",\"AnswerValue\":\"34\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"25\",\"Question\":\"Would you be comfortable assisting with dental procedures\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"36\",\"AnswerValue\":\"36\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"26\",\"Question\":\"Are you able to commit to an 18 month apprenticeship?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"38\",\"AnswerValue\":\"38\"}],\"FollowUpQuestions\":null}]}", "FileFieldController": null}, "OriginalEvent": {"EntryId": "9fd15c9e-cae7-6322-9971-ff0200979a84", "ReferralCode": "6015", "UserId": "00000000-0000-0000-0000-000000000000", "Username": "", "IpAddress": "**************", "SubmissionTime": "2025-07-29T15:05:43.8168149Z", "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84", "FormName": "sf_recruitmentjobapplication", "FormTitle": "Recruitment Job Application", "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000", "SendConfirmationEmail": false, "Controls": [{"FieldControlName": null, "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84", "SiblingId": "00000000-0000-0000-0000-000000000000", "Text": null, "Type": 0, "Title": "PayloadJson", "FieldName": "ParagraphTextFieldController", "Value": "{\"Jobinfo\":{\"jobId\":\"4496\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"<PERSON>isma\",\"lastName\":\"Nadeem \",\"email\":\"<EMAIL>\",\"phone\":\"07392807565\",\"Phonenumbercode\":\"GBR_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"EH48 2HD\",\"CurrentlyLive\":null,\"GraduateFrom\":null,\"GAClientId\":\"1.1748454867\"},\"jobQuestions\":[{\"QuestionId\":\"22\",\"Question\":\"What postcode / Town / City  would you like to work near? \",\"QuestionType\":\"Text\",\"Answers\":[{\"AnswerId\":\"0\",\"AnswerValue\":\"Edinburgh \"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"23\",\"Question\":\"Do you have a Grade C/4 or above in English GCSE and are able to provide evidence?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"32\",\"AnswerValue\":\"32\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"24\",\"Question\":\"Do you have a Grade C/4 or above in Maths GCSE and are able to provide evidence?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"34\",\"AnswerValue\":\"34\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"25\",\"Question\":\"Would you be comfortable assisting with dental procedures\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"36\",\"AnswerValue\":\"36\"}],\"FollowUpQuestions\":null},{\"QuestionId\":\"26\",\"Question\":\"Are you able to commit to an 18 month apprenticeship?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"38\",\"AnswerValue\":\"38\"}],\"FollowUpQuestions\":null}]}", "OldValue": null}, {"FieldControlName": null, "Id": "ded52a9d-cae7-6322-9971-ff0000979a84", "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 1, "Title": "Untitled", "FieldName": "FileFieldController", "Value": null, "OldValue": null}], "Origin": null}, "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"}, {"Name": "Form is submitted", "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49", "Item": {"ParagraphTextFieldController": "{\"Jobinfo\":{\"jobId\":\"4201\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"<PERSON><PERSON><PERSON>\",\"lastName\":\"<PERSON>\",\"email\":\"<EMAIL>\",\"phone\":\"7828012913\",\"Phonenumbercode\":\"IMN_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"B74 4YD\",\"CurrentlyLive\":\"GBR\",\"GraduateFrom\":\"IND\",\"GAClientId\":\"1.1353945850\"},\"jobQuestions\":[{\"QuestionId\":\"3\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"2\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"191500\"}],\"ParentQuestionId\":3}]}]}", "FileFieldController": [{"Id": "a8dcca67-d560-40d1-a8a7-313c0dee66ed", "ChildItemId": "f55e36d3-9118-4b3f-94d1-61448c6088e8", "ParentItemId": "f7d55c9e-cae7-6322-9971-ff0200979a84", "ChildItemProviderName": "OpenAccessDataProvider", "ParentItemProviderName": null, "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document", "ChildItemAdditionalInfo": "4201_<PERSON><PERSON><PERSON>_Bose.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4201_sunandita_bose.docx?Status=Master", "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication", "ParentItemAdditionalInfo": null, "LastModified": "2025-07-30T07:23:34.237Z", "Ordinal": 0.0, "Attributes": {}, "ApplicationName": "GroupWebsite/", "ComponentPropertyName": "f7d55c9e-cae7-6322-9971-ff0200979a84", "AvailableForLive": false, "AvailableForMaster": false, "AvailableForTemp": false, "IsParentDeleted": false, "IsChildDeleted": false, "FileName": "4201_<PERSON><PERSON><PERSON>_Bose.docx", "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4201_sunand<PERSON>_bose.docx?Status=Master"}]}, "OriginalEvent": {"EntryId": "f7d55c9e-cae7-6322-9971-ff0200979a84", "ReferralCode": "6020", "UserId": "00000000-0000-0000-0000-000000000000", "Username": "", "IpAddress": "***************", "SubmissionTime": "2025-07-30T07:23:34.2208646Z", "FormId": "6ad52a9d-cae7-6322-9971-ff0000979a84", "FormName": "sf_recruitmentjobapplication", "FormTitle": "Recruitment Job Application", "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000", "SendConfirmationEmail": false, "Controls": [{"FieldControlName": null, "Id": "8bd52a9d-cae7-6322-9971-ff0000979a84", "SiblingId": "00000000-0000-0000-0000-000000000000", "Text": null, "Type": 0, "Title": "PayloadJson", "FieldName": "ParagraphTextFieldController", "Value": "{\"Jobinfo\":{\"jobId\":\"4201\",\"jobTitle\":null,\"jobCompany\":null,\"jobLocation\":null},\"Applicant\":{\"firstName\":\"<PERSON><PERSON><PERSON>\",\"lastName\":\"<PERSON>\",\"email\":\"<EMAIL>\",\"phone\":\"7828012913\",\"Phonenumbercode\":\"IMN_44\",\"Phonetype\":\"Mobile\",\"Homepostcode\":\"B74 4YD\",\"CurrentlyLive\":\"GBR\",\"GraduateFrom\":\"IND\",\"GAClientId\":\"1.1353945850\"},\"jobQuestions\":[{\"QuestionId\":\"3\",\"Question\":\"Do you currently hold a GDC Registration?\",\"QuestionType\":\"Multiple Choice - Single Select\",\"Answers\":[{\"AnswerId\":\"5\",\"AnswerValue\":\"5\"}],\"FollowUpQuestions\":[{\"FollowupQuestionId\":\"2\",\"FollowupQuestion\":\"Please enter your GDC registration number.  \",\"FollowupQuestionType\":\"Number\",\"followupAnswers\":[{\"AnswerId\":\"\",\"AnswerValue\":\"191500\"}],\"ParentQuestionId\":3}]}]}", "OldValue": null}, {"FieldControlName": null, "Id": "ded52a9d-cae7-6322-9971-ff0000979a84", "SiblingId": "8bd52a9d-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 1, "Title": "Untitled", "FieldName": "FileFieldController", "Value": [{"Id": "a8dcca67-d560-40d1-a8a7-313c0dee66ed", "ChildItemId": "f55e36d3-9118-4b3f-94d1-61448c6088e8", "ParentItemId": "f7d55c9e-cae7-6322-9971-ff0200979a84", "ChildItemProviderName": "OpenAccessDataProvider", "ParentItemProviderName": null, "ChildItemType": "Telerik.Sitefinity.Libraries.Model.Document", "ChildItemAdditionalInfo": "4201_<PERSON><PERSON><PERSON>_Bose.docx|https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4201_sunandita_bose.docx?Status=Master", "ParentItemType": "Telerik.Sitefinity.DynamicTypes.Model.sf_recruitmentjobapplication", "ParentItemAdditionalInfo": null, "LastModified": "2025-07-30T07:23:34.237Z", "Ordinal": 0.0, "Attributes": {}, "ApplicationName": "GroupWebsite/", "ComponentPropertyName": "f7d55c9e-cae7-6322-9971-ff0200979a84", "AvailableForLive": false, "AvailableForMaster": false, "AvailableForTemp": false, "IsParentDeleted": false, "IsChildDeleted": false, "FileName": "4201_<PERSON><PERSON><PERSON>_Bose.docx", "FileUrl": "https://www.mydentist.co.uk/docs/default-source/jobapplicationcv/4201_sunand<PERSON>_bose.docx?Status=Master"}], "OldValue": null}], "Origin": null}, "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"}]