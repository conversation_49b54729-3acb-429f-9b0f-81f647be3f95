﻿using SitefinityAuthentication.mydentist;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Runtime.InteropServices.WindowsRuntime;
using System.ServiceModel;
using System.Text;
using System.Web;
using IDHGroup.SharedLibraries.Logging;

namespace SitefinityAuthentication
{
    /// <summary>
    /// Authenticates to Sitefinity and retrieves a claims-based authentication token
    /// Credit to https://knowledgebase.progress.com/articles/Article/Authenticate-user-in-Sitefinity-using-a-console-application-call-Sitefinity-services-server-side
    /// </summary>
    public static class SitefinityAuthenticator
    {
        public static string GetAuthenticationCookieHeader()
        {
            try
            {
                if (CookieContainer != null)
                {
                    CookieCollection cookieCollection = CookieContainer.GetCookies(SiteUri);
                    Cookie lastUsedCookie = cookieCollection.Count > 0 ? cookieCollection[FedAuthKey] : null;

                    if (lastUsedCookie != null && lastUsedCookie.Expires >= DateTime.Now && CookiesAreValid())
                    {
                        Log.Debug(ConfigurationManager.AppSettings["ApplicationName"], "Cookies are still valid. Returning cookie header");
                        return CookieContainer.GetCookieHeader(SiteUri);
                    }

                    Log.Debug(ConfigurationManager.AppSettings["ApplicationName"], "Cookies have expired. Logging in.");
                }
                else
                {
                    Log.Debug(ConfigurationManager.AppSettings["ApplicationName"], "Application must have restarted as we have no cookies ready to use. Logging in.");
                }

                CookieContainer = new CookieContainer();

                Login();

                Log.Debug(ConfigurationManager.AppSettings["ApplicationName"], "Logged in successfully. Returning cookie header");
                return CookieContainer.GetCookieHeader(SiteUri);
            }
            catch (Exception exception)
            {
                Log.Error(ConfigurationManager.AppSettings["ApplicationName"], "cob-websvr1",  "Exception occured while getting authentication header", "*******",  exception);
                return "";
            }
        }

        private static HttpWebRequest GetRequest(string url, IDictionary<string, string> formData)
        {
            string backendUrl = GetServiceUrl(url);
            HttpWebRequest backendRequest = (HttpWebRequest)WebRequest.Create(backendUrl);
            backendRequest.Method = "POST";
            backendRequest.ContentType = "application/json";
            backendRequest.CookieContainer = CookieContainer;

            byte[] formBytes;

            if (formData != null && formData.Any())
            {
                IEnumerable<string> formDataFields = formData
                    .Select(formDataPair =>
                        $"\"{formDataPair.Key}\":\"{formDataPair.Value}\""
                    );

                string formDataString = $"{{{string.Join(",", formDataFields)}}}";
                formBytes = Encoding.UTF8.GetBytes(formDataString);
            }
            else
            {
                formBytes = new byte[0];
            }

            backendRequest.ContentLength = formBytes.Length;

            using (Stream dataStream = backendRequest.GetRequestStream())
            {
                dataStream.Write(formBytes, 0, formBytes.Length);
                dataStream.Close();
            }

            return backendRequest;
        }

        private static bool CookiesAreValid()
        {
            try
            {
                DateTime startTime = DateTime.Now;
                HttpWebRequest backendRequest = GetRequest("GetAuthenticationResults", null);

                WebResponse webResponse = backendRequest.GetResponse();

                string webResponseString = GetResponseString(webResponse);

                if (webResponseString.Contains("Access Granted"))
                    return true;

                if (webResponseString.Contains("500 Unexpected Error"))
                {
                    Log.Fatal(ConfigurationManager.AppSettings["ApplicationName"], $"Sitefinity encountered a server error while trying to check if we're authenticated.");
                    return false;
                }

                return false;
            }
            catch (Exception exception)
            {
                Log.Error(ConfigurationManager.AppSettings["ApplicationName"], "cob-websvr1",  "Encountered an exception while checking if the cookies are valid", "*******",  exception);
                return false;
            }
        }

        private static WebResponse GetAuthenticationResponse()
        {
            string username = ConfigurationManager.AppSettings["sitefinityUsername"];
            string password = ConfigurationManager.AppSettings["sitefinityPassword"];

            IDictionary<string, string> formData = new Dictionary<string, string>
            {
                {"username", username},
                {"password", password}
            };

            HttpWebRequest signInRequest = GetRequest("Authenticate", formData);
            WebResponse tokenResponse = signInRequest.GetResponse();

            return tokenResponse;
        }

        private static string GetResponseString(WebResponse webResponse)
        {

            using (Stream responseStream = webResponse.GetResponseStream())
            {
                if (responseStream == null)
                {
                    Exception exception = new SitefinityAuthenticationException($"Tried to sign into sitefinity via url {webResponse.ResponseUri} but received a null response stream");

                    throw exception;
                }

                using (StreamReader responseReader = new StreamReader(responseStream))
                {
                    return responseReader.ReadToEnd();
                }
            }
        }

        private static string GetSiteUrl(string relativePath)
        {
            string baseUrl = AddTrailingSlash(SiteUrl);
            return String.Concat(baseUrl, relativePath);
        }

        private static string GetServiceUrl(string relativePath)
        {
            return $"{ClientAddress.Uri}/{relativePath}";
        }

        private static string AddTrailingSlash(string siteUrl)
        {
            return siteUrl.EndsWith("/") ? siteUrl : String.Concat(siteUrl, "/");
        }

        private static string GetWrapAccessToken(WebResponse webResponse)
        {
            string tokenResponseString = GetResponseString(webResponse);
            NameValueCollection responseCollection = HttpUtility.ParseQueryString(tokenResponseString);
            return HttpUtility.UrlEncode(responseCollection["wrap_access_token"]);
        }

        private static void Login(int attemptCounter = 0)
        {

            if (attemptCounter < 0)
                throw new ArgumentOutOfRangeException(nameof(attemptCounter), "attemptCounter must be greater than 0.");


            if (attemptCounter >= MaxAuthenticationAttempts)
            {
                Exception exception = new SitefinityAuthenticationException($"Failed to login after {MaxAuthenticationAttempts}");
                throw exception;
            }

            WebResponse authenticationResponse = GetAuthenticationResponse();
            string authenticationResponseString = GetResponseString(authenticationResponse);

            if (!authenticationResponseString.Contains("Logged in successfully"))
            {
                Log.Error(ConfigurationManager.AppSettings["ApplicationName"], "cob-websvr1",  $"Authentication response doesn't contain a response that suggests the login was successful. Response was actually '{authenticationResponseString}'", "*******",  null);
                throw new SitefinityAuthenticationException("Failed to log into Sitefinity");
            }
        }

        private const int MaxAuthenticationAttempts = 2;
        private static CookieContainer CookieContainer { get; set; }
        private static readonly EndpointAddress ClientAddress = new SitefinityDailyUpdateWebSoapClient().Endpoint.Address;
        private static readonly string SiteUrl = $"{ClientAddress.Uri.Scheme}://{ClientAddress.Uri.Authority}";
        private static readonly Uri SiteUri = ClientAddress.Uri;
        private static string FedAuthKey => SiteUri.Port == 80 || SiteUri.Port == 443 ? "FedAuth" : $"{SiteUri.Port}FedAuth";
    }
}
