﻿using System.Security.Cryptography;
using System.Text;
using MyDSitefinityAPI.DBContext;
using MyDSitefinityAPI.Models;

namespace MyDSitefinityAPI.Services
{
    /// <summary>
    /// An authentication service which allows a request to pass a username and password, which will be authenticated against the GroupWebsitePracticeData records
    /// </summary>
    public class PublicUserAuthenticationService
    {
        private ILogger<PublicUserAuthenticationService> Logger { get; }
        private GroupWebsitePracticeDataContext DbContext { get; }

        /// <summary>
        /// Creates a new <see cref="PublicUserAuthenticationService"/>
        /// </summary>
        /// <param name="logger">A logger for the service</param>
        /// <param name="dbContext">A DBContext. If null is passed, a new <see cref="GroupWebsitePracticeDataContext"/> will be created</param>
        public PublicUserAuthenticationService(ILogger<PublicUserAuthenticationService> logger, GroupWebsitePracticeDataContext? dbContext)
        {
            Logger = logger;
            DbContext = dbContext ?? new GroupWebsitePracticeDataContext();
        }

        public PublicUserAuthenticationService(ILogger<PublicUserAuthenticationService> logger):this(logger, null)
        {
        }

        /// <summary>
        /// Checks whether a password can be used to authenticate against a single user
        /// </summary>
        /// <param name="user">The user which we're authenticating</param>
        /// <param name="password">The password which we're using to prove our identity</param>
        /// <returns></returns>
        public bool Authenticate(PublicUser user, string password)
        {
            string hashedPassword = ComputeHash(password, user.Salt);
            return hashedPassword == user.Password;
        }

        public PublicUser? GetSaltedUser(string? saltedUsername)
        {
            if (string.IsNullOrEmpty(saltedUsername))
            {
                return null;
            }

            IQueryable<PublicUser> publicUsers = DbContext.PublicUsers;
            PublicUser? matchingUser = publicUsers
                .Where(publicUser => publicUser.IsDeleted == false)
                // Escape EF context as it doesn't like functions that can't be converted to SQL
                .AsEnumerable()
                .FirstOrDefault(publicUser => ComputeHash(publicUser.UserId, publicUser.Salt) == saltedUsername);
            
            if (matchingUser != null)
            {
                Logger.LogDebug($"Salted username matches against {matchingUser.UserId}");
                return matchingUser;
            }

            return null;
        }

        public PublicUser? GetUserById(string? userId)
        {
            if (string.IsNullOrEmpty(userId))
            {
                return null;
            }

            IQueryable<PublicUser> publicUsers = DbContext.PublicUsers;
            PublicUser? matchingUser = publicUsers
                .Where(publicUser => publicUser.IsDeleted == false)
                // Escape EF context as it doesn't like functions that can't be converted to SQL
                .AsEnumerable()
                .FirstOrDefault(publicUser => publicUser.UserId == userId);

            return matchingUser;
        }
        
        public string ComputeHash(string unsaltedString, string salt)
        {
            SHA512Managed hashAlg = new SHA512Managed();
            byte[] hash = hashAlg.ComputeHash(Encoding.UTF8.GetBytes(unsaltedString + salt + Entropy));
            return Convert.ToBase64String(hash);
        }

        public string GenerateSalt(int byteCount)
        {
            RNGCryptoServiceProvider rng = new RNGCryptoServiceProvider();
            byte[] salt = new byte[byteCount];
            rng.GetBytes(salt);
            return Convert.ToBase64String(salt);
        }
        
        /// <summary>
        /// Added entropy for hashing the user's ID and salt
        /// </summary>
        private const string Entropy = "xl1k5ss5NTE=";
    }
}
