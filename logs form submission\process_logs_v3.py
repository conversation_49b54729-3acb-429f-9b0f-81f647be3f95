
import json
import os
import re

def parse_log_content(content, filename):
    events = []
    # Regex to find all occurrences of the event and its JSON payload
    matches = re.finditer(r'The raw json at endpoint is:  (\{.*?\})\s\([a-f0-9]{8}\)', content, re.DOTALL)
    for match in matches:
        payload_text = match.group(1).strip()
        events.append({
            "event_name": "The raw json at endpoint is:",
            "payload_text": payload_text,
            "source_file": filename
        })
    return events

def main():
    log_dir = r'C:\Dev\MyDSitefinityAPI\logs form submission'
    all_events = []
    seen_events = set()

    log_files = sorted([f for f in os.listdir(log_dir) if f.startswith('log-') and f.endswith('.txt')])

    for log_file in log_files:
        file_path = os.path.join(log_dir, log_file)
        with open(file_path, 'r') as f:
            content = f.read()
            events = parse_log_content(content, log_file)
            for event in events:
                # Create a unique key for the event
                event_key = (event['event_name'], event['payload_text'])
                if event_key not in seen_events:
                    all_events.append(event)
                    seen_events.add(event_key)

    output_file = os.path.join(log_dir, 'deduplicated_events.json')
    with open(output_file, 'w') as f:
        json.dump(all_events, f, indent=2)

    print(f"Processed {len(log_files)} log files and found {len(all_events)} unique events.")
    print(f"Output written to {output_file}")

if __name__ == "__main__":
    main()
