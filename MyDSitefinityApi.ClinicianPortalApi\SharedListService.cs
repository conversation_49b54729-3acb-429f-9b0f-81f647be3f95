﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mydentist.MyDSitefinityApi.ClinicianPortalApi
{

    public class SharedListService : ISharedListService
    {
        private readonly IClinicianPortalApiWrapper _clinicianPortalApiWrapper;

        public SharedListService(IClinicianPortalApiWrapper clinicianPortalApiWrapper)
        {
            _clinicianPortalApiWrapper = clinicianPortalApiWrapper;
        }

        public async Task<List<SharedListItemViewModel>> GetByListName(string listName)
        {
            return await _clinicianPortalApiWrapper.GetApiResponse<List<SharedListItemViewModel>>("List/SearchListByValue?listName=" + listName);
        }
    }

    public interface ISharedListService
    {
        Task<List<SharedListItemViewModel>> GetByListName(string listName);
    }

}
