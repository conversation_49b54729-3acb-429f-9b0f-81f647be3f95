CREATE TABLE [dbo].[sf_feedback](
	[id] [uniqueidentifier] NOT NULL,
	[form_text_box__c008] [nvarchar](255) NULL,
	[form_text_box__c007] [nvarchar](255) NULL,
	[form_paragraph_text_box__c004] [nvarchar](max) NULL,
	[form_multiple_choice__c011] [nvarchar](255) NULL,
	[form_text_box__c014] [nvarchar](255) NULL,
	[form_text_box__c007_0] [nvarchar](255) NULL,
	[practice_name] [nvarchar](255) NULL,
	[form_text_box] [nvarchar](255) NULL,
	[rawJson] [nvarchar](MAX) NULL,
	[CreatedDate] [datetime2](7) NULL,
 CONSTRAINT [pk_sf_feedback] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]

ALTER TABLE [dbo].[sf_feedback] ADD  CONSTRAINT [DF_sf_feedback_createdDate]  DEFAULT (getdate()) FOR [CreatedDate]

GO