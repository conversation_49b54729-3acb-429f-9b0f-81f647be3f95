﻿using MyDSitefinityAPI.Interfaces;

namespace MyDSitefinityAPI.EmailHelpers
{
    public class EmailSender : IEmailSender
    {
        private IViewRender _renderer;
        public ILogger _logger { get; set; }

        public EmailSender(IViewRender renderer, ILogger logger)
        {
            _renderer = renderer;
            _logger = logger;
        }

        public async Task SendEmail(EmailDetail detail)
        {
            string templateLocation = detail.TemplateLocation;
            string emailContent = await _renderer.RenderViewToString(templateLocation, detail.ViewModel);

            new Email
            {
                To = new List<string>() { detail.EmailAddress },
                Subject = detail.Subject,
                Body = emailContent,
                _logger = _logger

            }.Add();
        }

        public void SendMail(EmailDetail detail)
        {
            new Email
            {
                To = new List<string>() { detail.EmailAddress },
                Subject = detail.Subject,
                Body = detail.ViewModel?.ToString() ?? "",
                _logger = _logger
            }.Add();
        }

    }
}
