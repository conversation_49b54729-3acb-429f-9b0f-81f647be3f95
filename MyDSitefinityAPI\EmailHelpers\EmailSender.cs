﻿using MyDSitefinityAPI.Interfaces;
using Microsoft.Extensions.Logging;

namespace MyDSitefinityAPI.EmailHelpers
{
    public class EmailSender : IEmailSender
    {
        private IViewRender _renderer;
        public ILogger<EmailSender> _logger { get; set; }
        private ILogger<Email> _emailLogger { get; set; }

        public EmailSender(IViewRender renderer, ILogger<EmailSender> logger, ILogger<Email> emailLogger)
        {
            _renderer = renderer;
            _logger = logger;
            _emailLogger = emailLogger;
        }

        public async Task SendEmail(EmailDetail detail)
        {
            string templateLocation = detail.TemplateLocation;
            string emailContent = await _renderer.RenderViewToString(templateLocation, detail.ViewModel);

            new Email
            {
                To = new List<string>() { detail.EmailAddress },
                Subject = detail.Subject,
                Body = emailContent,
                _logger = _emailLogger

            }.Add();
        }

        public void SendMail(EmailDetail detail)
        {
            new Email
            {
                To = new List<string>() { detail.EmailAddress },
                Subject = detail.Subject,
                Body = detail.ViewModel?.ToString() ?? "",
                _logger = _emailLogger
            }.Add();
        }

    }
}
