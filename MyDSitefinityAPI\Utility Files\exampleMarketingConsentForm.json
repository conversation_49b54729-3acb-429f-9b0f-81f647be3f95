{"Name": "Form is submitted", "SiteId": "efcc8f09-b6fe-4870-9895-58fb64dc1f49", "Item": {"CheckboxesFieldController": "Dental implants,Facial aesthetics (e.g. line and wrinkle treatments)", "EmailTextFieldController": "<EMAIL>"}, "OriginalEvent": {"EntryId": "95496a9c-cae7-6322-9971-ff0200979a84", "ReferralCode": "2681", "UserId": "00000000-0000-0000-0000-000000000000", "Username": "", "IpAddress": "***************", "SubmissionTime": "2023-07-05T15:43:35.5893886Z", "FormId": "5c89d99b-cae7-6322-9971-ff0000979a84", "FormName": "sf_marketing_consent", "FormTitle": "Marketing consent", "FormSubscriptionListId": "00000000-0000-0000-0000-000000000000", "SendConfirmationEmail": false, "Controls": [{"FieldControlName": null, "Id": "5d8ad99b-cae7-6322-9971-ff0000979a84", "SiblingId": "00000000-0000-0000-0000-000000000000", "Text": null, "Type": 0, "Title": "Sign up to receive information on our latest dental advice and promotions.", "FieldName": "CheckboxesFieldController", "Value": "Dental implants,Facial aesthetics (e.g. line and wrinkle treatments)", "OldValue": null}, {"FieldControlName": null, "Id": "688ad99b-cae7-6322-9971-ff0000979a84", "SiblingId": "5d8ad99b-cae7-6322-9971-ff0000979a84", "Text": null, "Type": 0, "Title": "Email", "FieldName": "EmailTextFieldController", "Value": "<EMAIL>", "OldValue": null}], "Origin": null}, "OriginalEventType": "Telerik.Sitefinity.Modules.Forms.Events.FormEntryCreatedEvent"}